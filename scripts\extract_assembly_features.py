#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
装配体特征提取器使用示例

本脚本用于批量提取装配体的多模态特征，包括结构化特征、形状特征和语义特征，并对特征进行对齐、PCA降维、归一化和加权融合等处理。

使用方法：
python scripts/extract_assembly_features.py [--output-dir 输出目录] [--assembly-ids 装配体ID列表] [--max-assemblies 最大装配体数] [--load-only] [--no-process] [--enable-fusion]

参数说明：
--output-dir        特征输出目录，默认为 dataset
--assembly-ids      指定要处理的装配体ID列表
--max-assemblies    限制处理的最大装配体数量
--load-only         仅加载和测试现有特征，不重新提取
--no-process        不进行特征处理（对齐、PCA、归一化、融合）
--enable-fusion     启用加权拼接融合特征（仅融合形状和语义特征），默认为False

输出：
- assembly_features.pkl      提取的装配体特征（字典，包含多种特征），结构如下：
    {
        'assembly_ids': [装配体ID列表],
        'structural': np.array,     # 结构化特征矩阵 (n_assemblies, n_features)
        'shape': np.array,          # 形状特征矩阵 (n_assemblies, n_features)
        'semantic': np.array,       # 语义特征矩阵 (n_assemblies, n_features)
        'fused_features': np.array, # 融合特征矩阵 (n_assemblies, n_features) - 可选
        'metadata': {               # 元数据信息
            'processing_enabled': bool,
            'fusion_enabled': bool,
            'feature_dimensions': {
                'structural': int,
                'shape': int,
                'semantic': int,
                'fused': int
            }
        }
    }

- assembly_feature_scalers.pkl    装配体特征标准化器
- 日志输出特征统计和分析信息

特征说明：
结构化特征 (12维):
- length, width, height, area, volume (几何特征 5维)
- density, mass (物理特征 2维)
- part_count (组成特征 1维)
- joints_count, contacts_count (连接特征 2维)
- part_volume_std, part_mass_std (统计特征 2维)

向量特征:
- 形状向量：从Milvus中的cad_collection_en集合的shape_vector字段提取（768维 -> PCA降至64维）
- 语义向量：从Milvus中的cad_collection_en集合的dense_vector字段提取（1024维 -> PCA降至64维）

依赖：
- src/assembly_feature_extractor.py
- numpy, argparse, logging

示例：
python scripts/extract_assembly_features.py --output-dir dataset --max-assemblies 100
python scripts/extract_assembly_features.py --no-process
python scripts/extract_assembly_features.py --enable-fusion --max-assemblies 50
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.extractors.assembly_feature_extractor import AssemblyFeatureExtractor
import numpy as np
import argparse
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def extract_and_save_features(output_dir='dataset', assembly_ids=None, max_assemblies=None, process_features=True, enable_fusion=False):
    """
    提取并保存装配体特征
    
    Args:
        output_dir: 输出目录
        assembly_ids: 指定的装配体ID列表
        max_assemblies: 最大装配体数量限制
        process_features: 是否进行特征处理
        enable_fusion: 是否启用加权拼接融合特征
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建装配体特征提取器
    extractor = AssemblyFeatureExtractor()
    
    # 如果指定了最大装配体数量，则限制assembly_ids
    if max_assemblies and assembly_ids and len(assembly_ids) > max_assemblies:
        assembly_ids = assembly_ids[:max_assemblies]
        logger.info(f"限制装配体数量为: {max_assemblies}")
    
    # 提取所有特征（包括特征处理）
    logger.info("开始提取装配体特征...")
    features = extractor.extract_all_features(
        assembly_ids=assembly_ids,
        include_shape_features=True,
        include_semantic_features=True,
        process_features=process_features,  # 根据参数决定是否进行特征处理
        enable_fusion=enable_fusion  # 根据参数决定是否启用加权拼接
    )
    
    if not features:
        logger.error("未能提取到任何装配体特征")
        return
    
    # 添加元数据信息
    features['metadata'] = {
        'processing_enabled': process_features,
        'fusion_enabled': enable_fusion and process_features,
        'feature_dimensions': {}
    }
    
    # 记录各特征维度
    for feature_type in ['structural', 'shape', 'semantic', 'fused_features']:
        if feature_type in features and isinstance(features[feature_type], np.ndarray):
            features['metadata']['feature_dimensions'][feature_type] = features[feature_type].shape[1]
    
    # 打印特征统计信息
    logger.info("=== 装配体特征提取统计 ===")
    total_assemblies = len(features.get('assembly_ids', []))
    logger.info(f"总装配体数: {total_assemblies}")
    
    for feature_type, data in features.items():
        if isinstance(data, np.ndarray):
            logger.info(f"{feature_type}: {data.shape}")
        elif isinstance(data, list):
            logger.info(f"{feature_type}: {len(data)} items")
    
    # 保存特征
    features_path = os.path.join(output_dir, 'assembly_features.pkl')
    extractor.save_features(features, features_path)
    logger.info(f"装配体特征已保存到: {features_path}")
    
    # 保存标准化器
    scalers_path = os.path.join(output_dir, 'assembly_feature_scalers.pkl')
    extractor.save_scalers(scalers_path)
    logger.info(f"装配体标准化器已保存到: {scalers_path}")
    
    # 分析特征质量
    analyze_feature_quality(features)
    
    return features

def analyze_feature_quality(features):
    """分析装配体特征质量"""
    logger.info("=== 装配体特征质量分析 ===")
    
    # 分析结构化特征
    if 'structural' in features:
        structural = features['structural']
        logger.info(f"装配体结构化特征统计:")
        logger.info(f"  维度: {structural.shape}")
        logger.info(f"  均值范围: [{np.min(np.mean(structural, axis=0)):.4f}, {np.max(np.mean(structural, axis=0)):.4f}]")
        logger.info(f"  标准差范围: [{np.min(np.std(structural, axis=0)):.4f}, {np.max(np.std(structural, axis=0)):.4f}]")
        logger.info(f"  缺失值: {np.sum(np.isnan(structural))}")
        logger.info(f"  L2范数范围: [{np.min(np.linalg.norm(structural, axis=1)):.4f}, {np.max(np.linalg.norm(structural, axis=1)):.4f}]")
    
    # 分析形状特征
    if 'shape' in features:
        shape = features['shape']
        logger.info(f"装配体形状特征统计:")
        logger.info(f"  维度: {shape.shape}")
        logger.info(f"  均值范围: [{np.min(np.mean(shape, axis=0)):.4f}, {np.max(np.mean(shape, axis=0)):.4f}]")
        logger.info(f"  标准差范围: [{np.min(np.std(shape, axis=0)):.4f}, {np.max(np.std(shape, axis=0)):.4f}]")
        logger.info(f"  缺失值: {np.sum(np.isnan(shape))}")
        logger.info(f"  L2范数范围: [{np.min(np.linalg.norm(shape, axis=1)):.4f}, {np.max(np.linalg.norm(shape, axis=1)):.4f}]")
    
    # 分析语义特征
    if 'semantic' in features:
        semantic = features['semantic']
        logger.info(f"装配体语义特征统计:")
        logger.info(f"  维度: {semantic.shape}")
        logger.info(f"  均值范围: [{np.min(np.mean(semantic, axis=0)):.4f}, {np.max(np.mean(semantic, axis=0)):.4f}]")
        logger.info(f"  标准差范围: [{np.min(np.std(semantic, axis=0)):.4f}, {np.max(np.std(semantic, axis=0)):.4f}]")
        logger.info(f"  缺失值: {np.sum(np.isnan(semantic))}")
        logger.info(f"  L2范数范围: [{np.min(np.linalg.norm(semantic, axis=1)):.4f}, {np.max(np.linalg.norm(semantic, axis=1)):.4f}]")
    
    # 分析融合特征
    if 'fused_features' in features:
        fused = features['fused_features']
        logger.info(f"装配体融合特征统计:")
        logger.info(f"  维度: {fused.shape}")
        logger.info(f"  均值范围: [{np.min(np.mean(fused, axis=0)):.4f}, {np.max(np.mean(fused, axis=0)):.4f}]")
        logger.info(f"  标准差范围: [{np.min(np.std(fused, axis=0)):.4f}, {np.max(np.std(fused, axis=0)):.4f}]")
        logger.info(f"  缺失值: {np.sum(np.isnan(fused))}")
        logger.info(f"  L2范数范围: [{np.min(np.linalg.norm(fused, axis=1)):.4f}, {np.max(np.linalg.norm(fused, axis=1)):.4f}]")
    
    # 分析特征覆盖率
    analyze_feature_coverage(features)

def analyze_feature_coverage(features):
    """分析装配体特征覆盖率"""
    logger.info("=== 装配体特征覆盖率分析 ===")
    
    total_assemblies = len(features.get('assembly_ids', []))
    if total_assemblies == 0:
        logger.warning("没有装配体数据")
        return
    
    # 结构化特征覆盖率
    if 'structural' in features:
        structural_count = features['structural'].shape[0]
        logger.info(f"结构化特征覆盖率: {structural_count}/{total_assemblies} ({structural_count/total_assemblies*100:.1f}%)")
    
    # 形状特征覆盖率（处理前）
    if 'shape' in features:
        shape_count = features['shape'].shape[0]
        logger.info(f"形状特征覆盖率: {shape_count}/{total_assemblies} ({shape_count/total_assemblies*100:.1f}%)")
    
    # 语义特征覆盖率（处理前）
    if 'semantic' in features:
        semantic_count = features['semantic'].shape[0]
        logger.info(f"语义特征覆盖率: {semantic_count}/{total_assemblies} ({semantic_count/total_assemblies*100:.1f}%)")
    
    # 融合特征覆盖率
    if 'fused_features' in features:
        fused_count = features['fused_features'].shape[0]
        logger.info(f"融合特征覆盖率: {fused_count}/{total_assemblies} ({fused_count/total_assemblies*100:.1f}%)")
    
    # 特征处理信息
    logger.info("=== 装配体特征处理信息 ===")
    if features.get('metadata', {}).get('processing_enabled', False):
        logger.info("已启用装配体特征处理：")
        logger.info("✓ 特征对齐（去除缺失特征的装配体）")
        logger.info("✓ PCA降维（形状和语义特征降至64维）")
        logger.info("✓ L2归一化（所有特征类型）")
        if features.get('metadata', {}).get('fusion_enabled', False):
            logger.info("✓ 加权拼接（仅形状:0.6, 语义:0.4，跳过结构化特征）")
        else:
            logger.info("✗ 加权拼接（未启用）")
    else:
        logger.info("✗ 装配体特征处理（未启用）")

def load_and_test_features(output_dir='dataset'):
    """加载和测试现有装配体特征"""
    logger.info("=== 加载和测试现有装配体特征 ===")
    
    features_path = os.path.join(output_dir, 'assembly_features.pkl')
    scalers_path = os.path.join(output_dir, 'assembly_feature_scalers.pkl')
    
    if not os.path.exists(features_path):
        logger.error(f"特征文件不存在: {features_path}")
        return
    
    if not os.path.exists(scalers_path):
        logger.error(f"标准化器文件不存在: {scalers_path}")
        return
    
    try:
        # 创建提取器并加载
        extractor = AssemblyFeatureExtractor()
        
        # 加载特征
        features = extractor.load_features(features_path)
        logger.info("装配体特征加载成功")
        
        # 加载标准化器
        extractor.load_scalers(scalers_path)
        logger.info("装配体标准化器加载成功")
        
        # 分析加载的特征
        analyze_feature_quality(features)
        
        # 测试单个装配体特征提取
        if 'assembly_ids' in features and features['assembly_ids']:
            sample_assembly_id = features['assembly_ids'][0]
            logger.info(f"测试单个装配体特征提取 (ID: {sample_assembly_id})")
            
            try:
                single_features = extractor.get_assembly_features_by_ids([sample_assembly_id])
                logger.info("单个装配体特征提取测试成功")
                
                for feature_type, data in single_features.items():
                    if isinstance(data, np.ndarray):
                        logger.info(f"  {feature_type}: {data.shape}")
                    elif isinstance(data, list):
                        logger.info(f"  {feature_type}: {len(data)} items")
                        
            except Exception as e:
                logger.error(f"单个装配体特征提取测试失败: {e}")
        
        logger.info("装配体特征加载和测试完成")
        
    except Exception as e:
        logger.error(f"加载装配体特征失败: {e}")
        raise

def get_assembly_statistics():
    """获取装配体数据库统计信息"""
    logger.info("=== 装配体数据库统计信息 ===")
    
    try:
        extractor = AssemblyFeatureExtractor()
        conn = extractor.connect_db()
        
        with conn.cursor() as cursor:
            # 统计总装配体数
            cursor.execute("SELECT COUNT(*) FROM assemblies")
            total_count = cursor.fetchone()[0]
            logger.info(f"数据库中总装配体数: {total_count}")
            
            # 统计有效特征数（非空值）
            cursor.execute("""
                SELECT 
                    COUNT(*) as total,
                    COUNT(length) as has_length,
                    COUNT(width) as has_width,
                    COUNT(height) as has_height,
                    COUNT(area) as has_area,
                    COUNT(volume) as has_volume,
                    COUNT(density) as has_density,
                    COUNT(mass) as has_mass,
                    COUNT(part_count) as has_part_count,
                    COUNT(joints_count) as has_joints_count,
                    COUNT(contacts_count) as has_contacts_count,
                    COUNT(part_volume_std) as has_part_volume_std,
                    COUNT(part_mass_std) as has_part_mass_std
                FROM assemblies
            """)
            stats = cursor.fetchone()
            
            logger.info("装配体特征完整性统计:")
            logger.info(f"  长度 (length): {stats[1]}/{stats[0]} ({stats[1]/stats[0]*100:.1f}%)")
            logger.info(f"  宽度 (width): {stats[2]}/{stats[0]} ({stats[2]/stats[0]*100:.1f}%)")
            logger.info(f"  高度 (height): {stats[3]}/{stats[0]} ({stats[3]/stats[0]*100:.1f}%)")
            logger.info(f"  表面积 (area): {stats[4]}/{stats[0]} ({stats[4]/stats[0]*100:.1f}%)")
            logger.info(f"  体积 (volume): {stats[5]}/{stats[0]} ({stats[5]/stats[0]*100:.1f}%)")
            logger.info(f"  密度 (density): {stats[6]}/{stats[0]} ({stats[6]/stats[0]*100:.1f}%)")
            logger.info(f"  质量 (mass): {stats[7]}/{stats[0]} ({stats[7]/stats[0]*100:.1f}%)")
            logger.info(f"  零件数量 (part_count): {stats[8]}/{stats[0]} ({stats[8]/stats[0]*100:.1f}%)")
            logger.info(f"  关节数量 (joints_count): {stats[9]}/{stats[0]} ({stats[9]/stats[0]*100:.1f}%)")
            logger.info(f"  接触面数量 (contacts_count): {stats[10]}/{stats[0]} ({stats[10]/stats[0]*100:.1f}%)")
            logger.info(f"  零件体积标准差 (part_volume_std): {stats[11]}/{stats[0]} ({stats[11]/stats[0]*100:.1f}%)")
            logger.info(f"  零件质量标准差 (part_mass_std): {stats[12]}/{stats[0]} ({stats[12]/stats[0]*100:.1f}%)")
            
            # 统计数值范围
            cursor.execute("""
                SELECT 
                    MIN(length), MAX(length), AVG(length),
                    MIN(width), MAX(width), AVG(width),
                    MIN(height), MAX(height), AVG(height),
                    MIN(area), MAX(area), AVG(area),
                    MIN(volume), MAX(volume), AVG(volume),
                    MIN(density), MAX(density), AVG(density),
                    MIN(mass), MAX(mass), AVG(mass),
                    MIN(part_count), MAX(part_count), AVG(part_count)
                FROM assemblies
                WHERE length IS NOT NULL AND width IS NOT NULL AND height IS NOT NULL
            """)
            ranges = cursor.fetchone()
            
            feature_names = ['length', 'width', 'height', 'area', 'volume', 'density', 'mass', 'part_count']
            logger.info("装配体特征数值范围:")
            for i, name in enumerate(feature_names):
                min_val = ranges[i*3]
                max_val = ranges[i*3 + 1]
                avg_val = ranges[i*3 + 2]
                if min_val is not None:
                    logger.info(f"  {name}: [{min_val:.6f}, {max_val:.6f}], 平均: {avg_val:.6f}")
        
        conn.close()
        
    except Exception as e:
        logger.error(f"获取装配体统计信息失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='装配体特征提取器使用示例')
    parser.add_argument('--output-dir', default='data/features/assemblies', help='输出目录')
    parser.add_argument('--assembly-ids', nargs='+', help='指定装配体ID列表')
    parser.add_argument('--max-assemblies', type=int, help='最大装配体数量限制')
    parser.add_argument('--load-only', action='store_true', help='仅加载和测试现有特征')
    parser.add_argument('--no-process', action='store_true', help='不进行特征处理（对齐、PCA、归一化、融合）')
    parser.add_argument('--enable-fusion', action='store_true', help='启用加权拼接融合特征（仅融合形状和语义特征），默认为False')
    parser.add_argument('--show-stats', action='store_true', help='显示装配体数据库统计信息')
    
    args = parser.parse_args()
    
    if args.show_stats:
        get_assembly_statistics()
        return
    
    if args.load_only:
        # 仅加载和测试现有特征
        load_and_test_features(args.output_dir)
    else:
        # 提取并保存特征
        logger.info(f"装配体特征处理{'禁用' if args.no_process else '启用'}")
        logger.info(f"装配体加权拼接{'启用' if args.enable_fusion else '禁用'}")
        
        features = extract_and_save_features(
            output_dir=args.output_dir,
            assembly_ids=args.assembly_ids,
            max_assemblies=args.max_assemblies,
            process_features=not args.no_process,
            enable_fusion=args.enable_fusion
        )
        
        if features:
            logger.info("装配体特征提取和保存完成")
        else:
            logger.error("装配体特征提取失败")

if __name__ == "__main__":
    main()
