#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
MCQ-Bench生成器启动脚本

简化的启动脚本，避免导入问题
"""

import sys
import os
import json
import random
import logging
from pathlib import Path
from tqdm import tqdm

# 添加项目根目录到系统路径
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(script_dir))
sys.path.insert(0, project_root)

# 添加当前目录到系统路径
sys.path.insert(0, script_dir)
sys.path.insert(0, os.path.join(script_dir, 'utils'))
sys.path.insert(0, os.path.join(script_dir, 'prompts'))

# 现在导入所需模块
try:
    from src.models.LLM import MultiProviderLLM
    from src.config import Config
    
    # 导入本地模块
    import data_sampler
    import query_generator
    import assembly_info_extractor
    
    # 导入提示词模块
    import attribute_query_prompt
    import structure_query_prompt
    import semantic_query_prompt
    import hybrid_query_prompt
    
    print("所有模块导入成功!")
    
except ImportError as e:
    print(f"模块导入失败: {e}")
    sys.exit(1)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def main():
    """主函数 - 简化版本"""
    logger.info("开始生成MCQ-Bench基准数据集...")
    
    # 创建输出目录
    output_dir = Path("data/benchmark")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建日志目录
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    try:
        # 初始化组件
        logger.info("初始化组件...")
        llm = MultiProviderLLM()
        sampler = data_sampler.DataSampler()
        extractor = assembly_info_extractor.AssemblyInfoExtractor()
        generator = query_generator.QueryGenerator(llm)
        
        # 随机抽取装配体
        logger.info("从数据库中随机抽取装配体...")
        assemblies = sampler.sample_assemblies(5)  # 先测试5个
        
        if not assemblies:
            logger.error("未能从数据库中获取装配体数据")
            return
        
        logger.info(f"成功抽取{len(assemblies)}个装配体")
        
        # 为每个装配体生成查询
        for i, assembly in enumerate(tqdm(assemblies, desc="生成查询"), 1):
            try:
                logger.info(f"处理装配体 {i}/{len(assemblies)}: {assembly['uuid']}")
                
                # 创建装配体文件夹
                assembly_dir = output_dir / assembly['uuid']
                assembly_dir.mkdir(parents=True, exist_ok=True)
                
                # 提取装配体完整信息
                logger.info("提取装配体信息...")
                assembly_info = extractor.extract_full_info(assembly)
                
                # 生成单模态查询
                logger.info("生成单模态查询...")
                single_queries = []
                
                # 属性查询
                attr_queries = generator.generate_attribute_queries(assembly_info, count=1)
                single_queries.extend(attr_queries)
                
                # 结构查询
                struct_queries = generator.generate_structure_queries(assembly_info, count=1)
                single_queries.extend(struct_queries)
                
                # 语义查询
                semantic_queries = generator.generate_semantic_queries(assembly_info, count=1)
                single_queries.extend(semantic_queries)
                
                # 生成混合查询
                logger.info("生成混合查询...")
                hybrid_queries = []
                
                if len(single_queries) >= 2:
                    # 生成一个混合查询
                    hybrid_query = generator.generate_hybrid_query(
                        assembly_info, 
                        single_queries[:2]
                    )
                    hybrid_queries.append(hybrid_query)
                
                # 保存查询文件
                logger.info("保存查询文件...")
                
                # 保存单模态查询
                with open(assembly_dir / "single_modal_query.json", 'w', encoding='utf-8') as f:
                    json.dump(single_queries, f, ensure_ascii=False, indent=2)
                
                # 保存混合查询
                with open(assembly_dir / "Hybrid_query.json", 'w', encoding='utf-8') as f:
                    json.dump(hybrid_queries, f, ensure_ascii=False, indent=2)
                
                logger.info(f"装配体 {assembly['uuid']} 处理完成")
                
            except Exception as e:
                logger.error(f"处理装配体 {assembly['uuid']} 失败: {e}")
                continue
        
        logger.info("MCQ-Bench基准数据集生成完成!")
        
    except Exception as e:
        logger.error(f"生成过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
