# 向量嵌入可视化指南

本指南介绍如何使用 `visualize_vector_embeddings.py` 脚本对向量特征进行降维可视化。

## 概述

`visualize_vector_embeddings.py` 脚本专门设计用于处理键为 ID、值为向量的字典格式的 pickle 文件（如 `assembly_representations.pkl`）。

## 功能特性

- 支持多种降维算法：UMAP、t-SNE、PCA
- 支持 2D 和 3D 可视化
- 多种着色方案：随机、ID 前缀、聚类标签
- 详细的统计分析
- 高质量图片输出

## 使用方法

### 基本用法

```bash
# 使用UMAP进行2D可视化（推荐）
python evaluate_scripts/clustering_analysis/visualize_vector_embeddings.py \
    --feature_path dataset/assembly_representations.pkl \
    --method umap

# 使用t-SNE进行可视化
python evaluate_scripts/clustering_analysis/visualize_vector_embeddings.py \
    --feature_path dataset/assembly_representations.pkl \
    --method tsne

# 使用PCA进行可视化
python evaluate_scripts/clustering_analysis/visualize_vector_embeddings.py \
    --feature_path dataset/assembly_representations.pkl \
    --method pca
```

### 高级用法

#### 1. 3D 可视化

```bash
python evaluate_scripts/clustering_analysis/visualize_vector_embeddings.py \
    --feature_path dataset/assembly_representations.pkl \
    --method umap \
    --n_components 3
```

#### 2. 显示标签

```bash
# 显示少量标签
python evaluate_scripts/clustering_analysis/visualize_vector_embeddings.py \
    --feature_path dataset/assembly_representations.pkl \
    --method umap \
    --show_labels 1 \
    --max_labels 20
```

#### 3. 使用聚类标签着色

```bash
python evaluate_scripts/clustering_analysis/visualize_vector_embeddings.py \
    --feature_path dataset/assembly_representations.pkl \
    --method umap \
    --color_by cluster \
    --cluster_labels dataset/hdbscan_assembly_vector_labels.pkl
```

#### 4. 根据 ID 前缀着色

```bash
python evaluate_scripts/clustering_analysis/visualize_vector_embeddings.py \
    --feature_path dataset/assembly_representations.pkl \
    --method umap \
    --color_by prefix
```

#### 5. 自定义参数

```bash
python evaluate_scripts/clustering_analysis/visualize_vector_embeddings.py \
    --feature_path dataset/assembly_representations.pkl \
    --method umap \
    --figsize 15 12 \
    --alpha 0.8 \
    --umap_n_neighbors 30 \
    --umap_min_dist 0.5 \
    --title_suffix "自定义分析"
```

## 参数说明

### 必需参数

- `--feature_path`: 特征文件路径（默认：`dataset/assembly_representations.pkl`）

### 可选参数

#### 降维相关

- `--method`: 降维方法，可选 `umap`、`tsne`、`pca`（默认：`umap`）
- `--n_components`: 降维后的维度，可选 2 或 3（默认：2）
- `--random_state`: 随机种子（默认：42）

#### 可视化相关

- `--output_dir`: 输出目录（默认：`visualization_results`）
- `--figsize`: 图形大小，两个数字（默认：12 10）
- `--alpha`: 点的透明度（默认：0.7）
- `--show_labels`: 是否显示标签，0 或 1（默认：0）
- `--max_labels`: 最大标签数量（默认：50）
- `--title_suffix`: 标题后缀（默认：空）

#### 着色相关

- `--color_by`: 着色方式，可选 `random`、`prefix`、`cluster`（默认：`random`）
- `--cluster_labels`: 聚类标签文件路径（用于 cluster 着色）

#### 分析相关

- `--analyze_stats`: 是否分析向量统计信息，0 或 1（默认：1）

#### 算法特定参数

- `--tsne_perplexity`: t-SNE 的 perplexity 参数（默认：30）
- `--umap_n_neighbors`: UMAP 的 n_neighbors 参数（默认：15）
- `--umap_min_dist`: UMAP 的 min_dist 参数（默认：0.1）

## 输出说明

### 文件输出

脚本会在指定的输出目录下创建以下文件：

- `vector_embeddings_2d_{method}.png`: 2D 可视化图片
- `vector_embeddings_3d_{method}.png`: 3D 可视化图片（如果选择 3D）

### 统计信息

脚本会输出详细的向量统计信息：

- 向量数量和维度
- 向量范围、均值、标准差
- 零向量和 NaN 值统计
- 向量模长统计
- 向量相似度分析（当向量数量不超过 1000 时）

## VS Code 任务

项目中预定义了以下 VS Code 任务，可以通过 `Ctrl+Shift+P` -> `Tasks: Run Task` 执行：

1. **Visualize Vector Embeddings (UMAP 2D)**: 基本 UMAP 2D 可视化
2. **Visualize Vector Embeddings (TSNE 2D)**: t-SNE 2D 可视化，显示标签
3. **Visualize Vector Embeddings (PCA 2D)**: PCA 2D 可视化
4. **Visualize Vector Embeddings (UMAP 3D)**: UMAP 3D 可视化
5. **Visualize Vector Embeddings with Clusters**: 使用聚类标签着色的可视化
6. **Test Vector Embeddings Visualization**: 测试可视化功能

## 最佳实践

### 1. 选择合适的降维方法

- **UMAP**: 推荐用于大多数情况，保持全局和局部结构，速度较快
- **t-SNE**: 适合强调局部结构，但速度较慢，不适合大数据集
- **PCA**: 线性降维，速度最快，适合快速预览和理解主要变化方向

### 2. 可视化策略

- 对于大数据集（>1000 个点），建议：

  - 使用 UMAP
  - 不显示标签（`--show_labels 0`）
  - 降低透明度（`--alpha 0.5`）

- 对于小数据集（<100 个点），建议：
  - 可以尝试所有方法
  - 显示标签（`--show_labels 1`）
  - 使用较高透明度（`--alpha 0.8`）

### 3. 聚类分析结合

1. 首先运行聚类分析：

```bash
python evaluate_scripts/clustering_analysis/hdbscan_cluster_assembly_vectors.py
```

2. 然后使用聚类结果进行着色可视化：

```bash
python evaluate_scripts/clustering_analysis/visualize_vector_embeddings.py \
    --color_by cluster \
    --cluster_labels dataset/hdbscan_assembly_vector_labels.pkl
```

## 常见问题

### 1. 内存不足

- 对于大数据集，考虑使用 PCA 先降维到 50 维以下
- 减小图形大小（`--figsize 8 6`）

### 2. t-SNE 运行缓慢

- 确保数据先用 PCA 降维到 50 维以下
- 调整 perplexity 参数（`--tsne_perplexity`）

### 3. 可视化结果不理想

- 尝试不同的降维方法
- 调整算法参数（如 UMAP 的`n_neighbors`和`min_dist`）
- 检查数据质量（零向量、NaN 值）

## 测试

运行测试脚本验证功能：

```bash
python test_scripts/test_vector_embeddings_visualization.py
```

测试包括：

- 模块导入测试
- 数据加载测试
- 降维算法测试
- 命令行接口测试

## 示例工作流

完整的向量分析工作流：

```bash
# 1. 提取装配体向量
python scripts/extract_assembly_vectors.py

# 2. 进行聚类分析
python evaluate_scripts/clustering_analysis/hdbscan_cluster_assembly_vectors.py

# 3. 基本可视化
python evaluate_scripts/clustering_analysis/visualize_vector_embeddings.py

# 4. 聚类结果可视化
python evaluate_scripts/clustering_analysis/visualize_vector_embeddings.py \
    --color_by cluster \
    --cluster_labels dataset/hdbscan_assembly_vector_labels.pkl

# 5. 3D可视化
python evaluate_scripts/clustering_analysis/visualize_vector_embeddings.py \
    --n_components 3 \
    --method umap
```
