#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
查询生成器

使用LLM生成各种类型的查询
"""

import sys
import os
import logging
from typing import Dict, List, Any, Optional

# 添加项目根目录到系统路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
sys.path.append(project_root)

from src.models.LLM import MultiProviderLLM

# 导入提示词模块
try:
    from prompts.attribute_query_prompt import get_attribute_query_prompt, get_attribute_task_params
    from prompts.structure_query_prompt import get_structure_query_prompt, get_structure_task_params
    from prompts.semantic_query_prompt import get_semantic_query_prompt, get_semantic_task_params
    from prompts.hybrid_query_prompt import get_hybrid_query_prompt, get_hybrid_task_params, get_hybrid_query_type
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    prompts_dir = os.path.join(os.path.dirname(current_dir), 'prompts')
    sys.path.insert(0, prompts_dir)
    from attribute_query_prompt import get_attribute_query_prompt, get_attribute_task_params
    from structure_query_prompt import get_structure_query_prompt, get_structure_task_params
    from semantic_query_prompt import get_semantic_query_prompt, get_semantic_task_params
    from hybrid_query_prompt import get_hybrid_query_prompt, get_hybrid_task_params, get_hybrid_query_type

logger = logging.getLogger(__name__)


class QueryGenerator:
    """Query Generator"""
    
    def __init__(self, llm: MultiProviderLLM):
        """
        Initialize query generator
        
        Args:
            llm: LLM instance
        """
        self.llm = llm
        self.query_id_counter = 1
    
    def generate_attribute_queries(self, assembly_info: Dict[str, Any], count: int = 3) -> List[Dict[str, Any]]:
        """
        Generate attribute queries
        
        Args:
            assembly_info: Assembly information
            count: Number to generate
            
        Returns:
            List of attribute queries
        """
        queries = []
        
        for i in range(count):
            try:
                # 生成提示词
                prompt = get_attribute_query_prompt(assembly_info, i + 1)
                
                # 调用LLM生成查询
                messages = [{"role": "user", "content": prompt}]
                response = self.llm.chat(messages)
                query_text = response.strip()
                
                # 构建查询对象
                query = {
                    "query_id": f"Attribute_{self.query_id_counter:03d}",
                    "query_type": "Attribute",
                    "natural_language_prompt": query_text,
                    "ground_truth_plan": {
                        "sub_queries": [
                            {
                                "agent": "属性查询智能体",
                                "task_params": get_attribute_task_params(query_text)
                            }
                        ],
                        "fusion_strategy": "NONE"
                    },
                    "ground_truth_results": [
                        {
                            "uuid": assembly_info['uuid']
                        }
                    ]
                }
                
                queries.append(query)
                self.query_id_counter += 1
                
                logger.info(f"Successfully generated attribute query: {query_text}")
                
            except Exception as e:
                logger.error(f"Failed to generate attribute query: {e}")
        
        return queries
    
    def generate_structure_queries(self, assembly_info: Dict[str, Any], count: int = 3) -> List[Dict[str, Any]]:
        """
        Generate structure queries
        
        Args:
            assembly_info: Assembly information
            count: Number to generate
            
        Returns:
            List of structure queries
        """
        queries = []
        
        for i in range(count):
            try:
                # 生成提示词
                prompt = get_structure_query_prompt(assembly_info, i + 1)
                
                # 调用LLM生成查询
                messages = [{"role": "user", "content": prompt}]
                response = self.llm.chat(messages)
                query_text = response.strip()
                
                # 构建查询对象
                query = {
                    "query_id": f"Structure_{self.query_id_counter:03d}",
                    "query_type": "Structure",
                    "natural_language_prompt": query_text,
                    "ground_truth_plan": {
                        "sub_queries": [
                            {
                                "agent": "结构关系查询智能体",
                                "task_params": get_structure_task_params(query_text)
                            }
                        ],
                        "fusion_strategy": "NONE"
                    },
                    "ground_truth_results": [
                        {
                            "uuid": assembly_info['uuid']
                        }
                    ]
                }
                
                queries.append(query)
                self.query_id_counter += 1
                
                logger.info(f"Successfully generated structure query: {query_text}")
                
            except Exception as e:
                logger.error(f"Failed to generate structure query: {e}")
        
        return queries
    
    def generate_semantic_queries(self, assembly_info: Dict[str, Any], count: int = 3) -> List[Dict[str, Any]]:
        """
        Generate semantic queries
        
        Args:
            assembly_info: Assembly information
            count: Number to generate
            
        Returns:
            List of semantic queries
        """
        queries = []
        
        for i in range(count):
            try:
                # 生成提示词
                prompt = get_semantic_query_prompt(assembly_info, i + 1)
                
                # 调用LLM生成查询
                messages = [{"role": "user", "content": prompt}]
                response = self.llm.chat(messages)
                query_text = response.strip()
                
                # 构建查询对象
                query = {
                    "query_id": f"Semantic_{self.query_id_counter:03d}",
                    "query_type": "Semantic",
                    "natural_language_prompt": query_text,
                    "ground_truth_plan": {
                        "sub_queries": [
                            {
                                "agent": "几何和语义查询智能体",
                                "task_params": get_semantic_task_params(query_text)
                            }
                        ],
                        "fusion_strategy": "NONE"
                    },
                    "ground_truth_results": [
                        {
                            "uuid": assembly_info['uuid']
                        }
                    ]
                }
                
                queries.append(query)
                self.query_id_counter += 1
                
                logger.info(f"Successfully generated semantic query: {query_text}")
                
            except Exception as e:
                logger.error(f"Failed to generate semantic query: {e}")
        
        return queries
    
    def generate_hybrid_query(self, assembly_info: Dict[str, Any], 
                            single_queries: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Generate hybrid query
        
        Args:
            assembly_info: Assembly information
            single_queries: Single modal query list
            
        Returns:
            Hybrid query object
        """
        try:
            # 生成提示词
            prompt = get_hybrid_query_prompt(assembly_info, single_queries)
            
            # 调用LLM生成查询
            messages = [{"role": "user", "content": prompt}]
            response = self.llm.chat(messages)
            query_text = response.strip()
            
            # 确定查询类型
            query_type = get_hybrid_query_type(single_queries)
            
            # 构建查询对象
            query = {
                "query_id": f"Hybrid_{self.query_id_counter:03d}",
                "query_type": query_type,
                "natural_language_prompt": query_text,
                "ground_truth_plan": {
                    "sub_queries": get_hybrid_task_params(single_queries),
                    "fusion_strategy": "INTERSECT"
                },
                "ground_truth_results": [
                    {
                        "uuid": assembly_info['uuid']
                    }
                ]
            }
            
            self.query_id_counter += 1
            
            logger.info(f"Successfully generated hybrid query: {query_text}")
            
            return query
            
        except Exception as e:
            logger.error(f"Failed to generate hybrid query: {e}")
            raise
    
    def reset_counter(self):
        """Reset query ID counter"""
        self.query_id_counter = 1
