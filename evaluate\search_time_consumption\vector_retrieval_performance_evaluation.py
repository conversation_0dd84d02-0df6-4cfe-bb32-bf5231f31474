#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
向量检索性能评估脚本

评估内容：
1. 使用CUDA情况下，CLIP模型编码一张图片的平均时间
2. 在整个集合中的检索性能：
   - search_cad_by_shape的平均耗时
   - search_cad_by_text的平均时间
   - search_cad_hybrid的平均耗时
3. 分析不同搜索类型的性能特征
"""

import os
import sys
import time
import random
import logging
import numpy as np
import torch
from pathlib import Path
from typing import List, Dict, Any, Tuple, Optional
import pickle
from PIL import Image
from pymilvus import Collection, connections, AnnSearchRequest, RRFRanker
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from src.database.milvus_utils import MilvusManager
from src.models.clip import CLIPFeatureExtractor
from src.config import Config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class VectorRetrievalPerformanceEvaluator:
    """向量检索性能评估器"""
    
    def __init__(self, collection_name="cad_collection_en", device="cuda", use_reranker=True, query_subset_size=100):
        """
        初始化性能评估器
        
        参数:
            collection_name: Milvus集合名称
            device: 使用的设备，'cpu'或'cuda'
            use_reranker: 是否使用重排序器
            query_subset_size: 查询子集大小
        """
        self.collection_name = collection_name
        self.device = device if torch.cuda.is_available() else "cpu"
        self.use_reranker = use_reranker
        self.query_subset_size = query_subset_size
        
        # 初始化Milvus管理器
        logger.info(f"初始化Milvus管理器，设备: {self.device}")
        self.milvus_manager = MilvusManager(use_reranker=self.use_reranker, device=self.device)
        
        # 初始化CLIP特征提取器
        logger.info("初始化CLIP特征提取器...")
        self.clip_extractor = CLIPFeatureExtractor(device=self.device)
        
        # 数据集路径
        self.dataset_path = Path("data/datasets/fusion360_assembly")
        
        # 存储评估结果
        self.evaluation_results = {}
        
        # 存储查询子集的ID列表
        self.query_subset_ids = []
    def get_random_assemblies(self, num_assemblies=100) -> List[Dict[str, Any]]:
        """
        从Milvus集合中随机抽取指定数量的装配体
        
        参数:
            num_assemblies: 要抽取的装配体数量
            
        返回:
            包含装配体信息的列表
        """
        logger.info(f"从集合 '{self.collection_name}' 中随机抽取 {num_assemblies} 个装配体...")
        
        self.milvus_manager.connect()
        collection = Collection(name=self.collection_name)
        collection.load()
        
        # 查询所有装配体
        assembly_results = collection.query(
            expr='type == "assembly"',
            output_fields=["pk", "description"],
            limit=16384  # 设置一个较大的限制
        )
        
        if len(assembly_results) < num_assemblies:
            logger.warning(f"集合中只有 {len(assembly_results)} 个装配体，小于请求的 {num_assemblies} 个")
            num_assemblies = len(assembly_results)
        
        # 随机抽取
        random_assemblies = random.sample(assembly_results, num_assemblies)
        
        logger.info(f"成功抽取 {len(random_assemblies)} 个装配体")
        return random_assemblies
    
    def find_assembly_image(self, assembly_id: str) -> Optional[str]:
        """
        根据装配体ID找到对应的assembly.png文件路径
        
        参数:
            assembly_id: 装配体ID
            
        返回:
            图片路径，如果不存在则返回None
        """
        # 在数据集目录中查找包含该ID的文件夹
        for assembly_folder in self.dataset_path.iterdir():
            if assembly_folder.is_dir() and assembly_id in assembly_folder.name:
                image_path = assembly_folder / "assembly.png"
                if image_path.exists():
                    return str(image_path)
        return None
    
    def evaluate_clip_encoding_time(self, assemblies: List[Dict[str, Any]], num_samples=50) -> Dict[str, float]:
        """
        评估CLIP模型编码单张图片的平均时间
        
        参数:
            assemblies: 装配体列表
            num_samples: 用于测试的样本数量
            
        返回:
            包含时间统计的字典
        """
        logger.info(f"评估CLIP编码时间，使用 {num_samples} 个样本...")
        
        encoding_times = []
        successful_encodings = 0
        
        # 随机选择样本进行测试
        test_assemblies = random.sample(assemblies, min(num_samples, len(assemblies)))
        
        for assembly in test_assemblies:
            assembly_id = assembly["pk"]
            image_path = self.find_assembly_image(assembly_id)
            
            if image_path is None:
                logger.warning(f"未找到装配体 {assembly_id} 的图片")
                continue
            
            try:
                # 加载图片
                image = Image.open(image_path).convert('RGB')
                processed_image = self.clip_extractor.preprocess(image).unsqueeze(0)
                
                # 预热GPU（如果使用CUDA）
                if self.device == "cuda":
                    _ = self.clip_extractor(processed_image, normalize=True)
                    torch.cuda.synchronize()
                
                # 测量编码时间
                start_time = time.time()
                
                with torch.no_grad():
                    features = self.clip_extractor(processed_image, normalize=True)
                    
                # 如果使用CUDA，等待GPU操作完成
                if self.device == "cuda":
                    torch.cuda.synchronize()
                
                end_time = time.time()
                encoding_time = end_time - start_time
                encoding_times.append(encoding_time)
                successful_encodings += 1
                
            except Exception as e:
                logger.error(f"编码图片 {image_path} 时出错: {e}")
                continue
        
        if not encoding_times:
            logger.error("没有成功编码任何图片")
            return {}
        
        # 计算统计信息
        avg_time = np.mean(encoding_times)
        min_time = np.min(encoding_times)
        max_time = np.max(encoding_times)
        std_time = np.std(encoding_times)
        
        results = {
            "average_time": avg_time,
            "min_time": min_time,
            "max_time": max_time,
            "std_time": std_time,
            "successful_samples": successful_encodings,
            "total_samples": len(test_assemblies),
            "device": self.device
        }
        
        logger.info(f"CLIP编码时间评估完成:")
        logger.info(f"  平均时间: {avg_time:.4f}s")
        logger.info(f"  最小时间: {min_time:.4f}s")
        logger.info(f"  最大时间: {max_time:.4f}s")
        logger.info(f"  标准差: {std_time:.4f}s")
        logger.info(f"  成功样本: {successful_encodings}/{len(test_assemblies)}")
        
        return results
    
    def extract_shape_vectors(self, assemblies: List[Dict[str, Any]]) -> Tuple[List[np.ndarray], List[str]]:
        """
        提取装配体的形状向量
        
        参数:
            assemblies: 装配体列表
            
        返回:
            形状向量列表和对应的装配体ID列表
        """
        logger.info(f"提取 {len(assemblies)} 个装配体的形状向量...")
        
        shape_vectors = []
        assembly_ids = []
        
        for assembly in assemblies:
            assembly_id = assembly["pk"]
            image_path = self.find_assembly_image(assembly_id)
            
            if image_path is None:
                logger.warning(f"未找到装配体 {assembly_id} 的图片，跳过")
                continue
            
            try:
                # 加载并处理图片
                image = Image.open(image_path).convert('RGB')
                processed_image = self.clip_extractor.preprocess(image).unsqueeze(0)
                
                # 提取特征向量
                with torch.no_grad():
                    features = self.clip_extractor(processed_image, normalize=True)
                    shape_vector = features.cpu().numpy().flatten()
                
                shape_vectors.append(shape_vector)
                assembly_ids.append(assembly_id)
                
            except Exception as e:
                logger.error(f"提取装配体 {assembly_id} 的形状向量时出错: {e}")
                continue
        
        logger.info(f"成功提取 {len(shape_vectors)} 个形状向量")
        return shape_vectors, assembly_ids
    
    def evaluate_shape_search_time(self, shape_vectors: List[np.ndarray], top_k=5, search_in_subset=False) -> Dict[str, float]:
        """
        评估基于形状的搜索时间
        
        参数:
            shape_vectors: 形状向量列表
            top_k: 返回结果数量
            search_in_subset: 是否在查询子集中搜索
            
        返回:
            包含时间统计的字典
        """
        search_type = "查询子集" if search_in_subset else "整个集合"
        logger.info(f"评估形状搜索时间（{search_type}），使用 {len(shape_vectors)} 个查询向量...")
        
        search_times = []
        
        for i, query_vector in enumerate(shape_vectors):
            try:
                start_time = time.time()
                
                if search_in_subset:
                    # 在子集中搜索
                    results = self._search_cad_by_shape_in_subset(query_vector, top_k)
                else:
                    # 在整个集合中搜索
                    results = self.milvus_manager.search_cad_by_shape(
                        collection_name=self.collection_name,
                        query_vector=query_vector,
                        top_k=top_k
                    )
                
                end_time = time.time()
                search_time = end_time - start_time
                search_times.append(search_time)
                
                # 每处理20个查询打印一次进度
                if (i + 1) % 20 == 0:
                    logger.info(f"已完成 {i+1}/{len(shape_vectors)} 个形状搜索（{search_type}）")
                
            except Exception as e:
                logger.error(f"执行第 {i+1} 个形状搜索时出错: {e}")
                continue
        
        if not search_times:
            logger.error("没有成功执行任何形状搜索")
            return {}
        
        # 计算统计信息
        avg_time = np.mean(search_times)
        min_time = np.min(search_times)
        max_time = np.max(search_times)
        std_time = np.std(search_times)
        
        results = {
            "average_time": avg_time,
            "min_time": min_time,
            "max_time": max_time,
            "std_time": std_time,
            "successful_searches": len(search_times),
            "total_queries": len(shape_vectors),
            "top_k": top_k,
            "search_in_subset": search_in_subset
        }
        
        logger.info(f"形状搜索时间评估完成（{search_type}）:")
        logger.info(f"  平均时间: {avg_time:.4f}s")
        logger.info(f"  最小时间: {min_time:.4f}s")
        logger.info(f"  最大时间: {max_time:.4f}s")
        logger.info(f"  标准差: {std_time:.4f}s")
        logger.info(f"  成功搜索: {len(search_times)}/{len(shape_vectors)}")
        
        return results
    
    def evaluate_text_search_time(self, assemblies: List[Dict[str, Any]], top_k=5, search_in_subset=False) -> Dict[str, float]:
        """
        评估基于文本的搜索时间
        
        参数:
            assemblies: 装配体列表
            top_k: 返回结果数量
            search_in_subset: 是否在查询子集中搜索
            
        返回:
            包含时间统计的字典
        """
        search_type = "查询子集" if search_in_subset else "整个集合"
        logger.info(f"评估文本搜索时间（{search_type}），使用 {len(assemblies)} 个查询文本...")
        
        search_times = []
        
        for i, assembly in enumerate(assemblies):
            description = assembly.get("description", "")
            if not description:
                logger.warning(f"装配体 {assembly['pk']} 没有描述文本，跳过")
                continue
            
            try:
                start_time = time.time()
                
                if search_in_subset:
                    # 在子集中搜索
                    results = self._search_cad_by_text_in_subset(description, top_k)
                else:
                    # 在整个集合中搜索
                    results = self.milvus_manager.search_cad_by_text(
                        collection_name=self.collection_name,
                        query_text=description,
                        top_k=top_k
                    )
                
                end_time = time.time()
                search_time = end_time - start_time
                search_times.append(search_time)
                
                # 每处理20个查询打印一次进度
                if (i + 1) % 20 == 0:
                    logger.info(f"已完成 {i+1}/{len(assemblies)} 个文本搜索（{search_type}）")
                
            except Exception as e:
                logger.error(f"执行第 {i+1} 个文本搜索时出错: {e}")
                continue
        
        if not search_times:
            logger.error(f"没有成功执行任何文本搜索（{search_type}）")
            return {}
        
        # 计算统计信息
        avg_time = np.mean(search_times)
        min_time = np.min(search_times)
        max_time = np.max(search_times)
        std_time = np.std(search_times)
        
        results = {
            "average_time": avg_time,
            "min_time": min_time,
            "max_time": max_time,
            "std_time": std_time,
            "successful_searches": len(search_times),
            "total_queries": len(assemblies),
            "top_k": top_k,
            "use_reranker": self.use_reranker,
            "search_in_subset": search_in_subset
        }
        
        logger.info(f"文本搜索时间评估完成（{search_type}）:")
        logger.info(f"  平均时间: {avg_time:.4f}s")
        logger.info(f"  最小时间: {min_time:.4f}s")
        logger.info(f"  最大时间: {max_time:.4f}s")
        logger.info(f"  标准差: {std_time:.4f}s")
        logger.info(f"  成功搜索: {len(search_times)}/{len(assemblies)}")
        
        return results
    
    def evaluate_hybrid_search_time(self, assemblies: List[Dict[str, Any]], 
                                  shape_vectors: List[np.ndarray], 
                                  assembly_ids: List[str], 
                                  top_k=5, shape_weight=0.3, search_in_subset=False) -> Dict[str, float]:
        """
        评估混合搜索时间
        
        参数:
            assemblies: 装配体列表
            shape_vectors: 形状向量列表
            assembly_ids: 形状向量对应的装配体ID列表
            top_k: 返回结果数量
            shape_weight: 形状搜索权重
            search_in_subset: 是否在查询子集中搜索
            
        返回:
            包含时间统计的字典
        """
        search_type = "查询子集" if search_in_subset else "整个集合"
        logger.info(f"评估混合搜索时间（{search_type}），使用 {len(assemblies)} 个查询...")
        
        # 创建装配体ID到形状向量的映射
        id_to_vector = dict(zip(assembly_ids, shape_vectors))
        
        search_times = []
        
        for i, assembly in enumerate(assemblies):
            assembly_id = assembly["pk"]
            description = assembly.get("description", "")
            
            if not description:
                logger.warning(f"装配体 {assembly_id} 没有描述文本，跳过")
                continue
            
            # 获取对应的形状向量
            shape_vector = id_to_vector.get(assembly_id)
            if shape_vector is None:
                logger.warning(f"装配体 {assembly_id} 没有形状向量，跳过")
                continue
            
            try:
                start_time = time.time()
                
                if search_in_subset:
                    # 在子集中搜索
                    results = self._search_cad_hybrid_in_subset(
                        description, shape_vector, top_k, shape_weight
                    )
                else:
                    # 在整个集合中搜索
                    results = self.milvus_manager.search_cad_hybrid(
                        collection_name=self.collection_name,
                        query_text=description,
                        query_shape_vector=shape_vector,
                        top_k=top_k,
                        shape_weight=shape_weight
                    )
                
                end_time = time.time()
                search_time = end_time - start_time
                search_times.append(search_time)
                
                # 每处理20个查询打印一次进度
                if (i + 1) % 20 == 0:
                    logger.info(f"已完成 {i+1}/{len(assemblies)} 个混合搜索（{search_type}）")
                
            except Exception as e:
                logger.error(f"执行第 {i+1} 个混合搜索时出错: {e}")
                continue
        
        if not search_times:
            logger.error("没有成功执行任何混合搜索")
            return {}
        
        # 计算统计信息
        avg_time = np.mean(search_times)
        min_time = np.min(search_times)
        max_time = np.max(search_times)
        std_time = np.std(search_times)
        
        results = {
            "average_time": avg_time,
            "min_time": min_time,
            "max_time": max_time,
            "std_time": std_time,
            "successful_searches": len(search_times),
            "total_queries": len(assemblies),
            "top_k": top_k,
            "shape_weight": shape_weight,
            "use_reranker": self.use_reranker,
            "search_in_subset": search_in_subset
        }
        
        logger.info(f"混合搜索时间评估完成（{search_type}）:")
        logger.info(f"  平均时间: {avg_time:.4f}s")
        logger.info(f"  最小时间: {min_time:.4f}s")
        logger.info(f"  最大时间: {max_time:.4f}s")
        logger.info(f"  标准差: {std_time:.4f}s")
        logger.info(f"  成功搜索: {len(search_times)}/{len(assemblies)}")
        
        return results
    
    def run_full_evaluation(self, num_assemblies=100, num_clip_samples=50, top_k=5) -> Dict[str, Any]:
        """
        运行完整的性能评估
        
        参数:
            num_assemblies: 要测试的装配体数量
            num_clip_samples: CLIP编码测试的样本数量
            top_k: 搜索返回的结果数量
            
        返回:
            完整的评估结果
        """
        logger.info("开始向量检索性能评估...")
        logger.info(f"评估参数: 装配体数量={num_assemblies}, CLIP样本={num_clip_samples}, top_k={top_k}")
        logger.info(f"查询子集大小: {self.query_subset_size}")
        
        # 1. 随机抽取装配体用于测试
        assemblies = self.get_random_assemblies(num_assemblies)
        
        # 2. 创建查询子集
        self.create_query_subset(num_assemblies)
        
        # 3. 评估CLIP编码时间
        clip_results = self.evaluate_clip_encoding_time(assemblies, num_clip_samples)
        
        # 4. 提取形状向量
        shape_vectors, shape_assembly_ids = self.extract_shape_vectors(assemblies)
        
        # 5. 在整个集合上评估搜索性能
        logger.info("开始在整个集合上进行性能评估...")
        shape_search_full = self.evaluate_shape_search_time(shape_vectors, top_k, search_in_subset=False)
        text_search_full = self.evaluate_text_search_time(assemblies, top_k, search_in_subset=False)
        hybrid_search_full = self.evaluate_hybrid_search_time(
            assemblies, shape_vectors, shape_assembly_ids, top_k, search_in_subset=False
        )
        
        # 6. 在查询子集上评估搜索性能
        logger.info("开始在查询子集上进行性能评估...")
        logger.info(f"查询子集包含 {len(self.query_subset_ids)} 个装配体")
        shape_search_subset = self.evaluate_shape_search_time(shape_vectors, top_k, search_in_subset=True)
        text_search_subset = self.evaluate_text_search_time(assemblies, top_k, search_in_subset=True)
        hybrid_search_subset = self.evaluate_hybrid_search_time(
            assemblies, shape_vectors, shape_assembly_ids, top_k, search_in_subset=True
        )
        
        # 汇总结果
        evaluation_results = {
            "evaluation_info": {
                "num_assemblies": num_assemblies,
                "num_clip_samples": num_clip_samples,
                "query_subset_size": self.query_subset_size,
                "top_k": top_k,
                "device": self.device,
                "use_reranker": self.use_reranker,
                "collection_name": self.collection_name,
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
            },
            "clip_encoding": clip_results,
            "shape_search": {
                "full_collection": shape_search_full,
                "query_subset": shape_search_subset
            },
            "text_search": {
                "full_collection": text_search_full,
                "query_subset": text_search_subset
            },
            "hybrid_search": {
                "full_collection": hybrid_search_full,
                "query_subset": hybrid_search_subset
            }
        }
        
        self.evaluation_results = evaluation_results
        
        # 打印摘要和生成对比表格
        self.print_evaluation_summary()
        self.generate_comparison_table()
        
        return evaluation_results
    
    def print_evaluation_summary(self):
        """打印评估结果摘要"""
        if not self.evaluation_results:
            logger.warning("没有评估结果可以显示")
            return
        
        logger.info("\n" + "="*80)
        logger.info("向量检索性能评估摘要")
        logger.info("="*80)
        
        eval_info = self.evaluation_results["evaluation_info"]
        logger.info(f"评估时间: {eval_info['timestamp']}")
        logger.info(f"设备: {eval_info['device']}")
        logger.info(f"重排序器: {'启用' if eval_info['use_reranker'] else '禁用'}")
        logger.info(f"测试装配体数量: {eval_info['num_assemblies']}")
        logger.info(f"查询子集大小: {eval_info['query_subset_size']}")
        logger.info(f"Top-K: {eval_info['top_k']}")
        
        # CLIP编码结果
        clip_results = self.evaluation_results.get("clip_encoding", {})
        if clip_results:
            logger.info(f"\n1. CLIP编码时间:")
            logger.info(f"   平均时间: {clip_results['average_time']:.4f}s")
            logger.info(f"   成功率: {clip_results['successful_samples']}/{clip_results['total_samples']}")
        
        # 形状搜索结果对比
        shape_results = self.evaluation_results.get("shape_search", {})
        if shape_results:
            logger.info(f"\n2. 形状搜索时间对比:")
            full_result = shape_results.get("full_collection", {})
            subset_result = shape_results.get("query_subset", {})
            
            if full_result:
                logger.info(f"   整个集合: {full_result['average_time']:.4f}s (成功率: {full_result['successful_searches']}/{full_result['total_queries']})")
            if subset_result:
                logger.info(f"   查询子集: {subset_result['average_time']:.4f}s (成功率: {subset_result['successful_searches']}/{subset_result['total_queries']})")
            
            # 计算性能提升
            if full_result and subset_result:
                speedup = full_result['average_time'] / subset_result['average_time']
                logger.info(f"   性能提升: {speedup:.2f}x")
        
        # 文本搜索结果对比
        text_results = self.evaluation_results.get("text_search", {})
        if text_results:
            logger.info(f"\n3. 文本搜索时间对比:")
            full_result = text_results.get("full_collection", {})
            subset_result = text_results.get("query_subset", {})
            
            if full_result:
                logger.info(f"   整个集合: {full_result['average_time']:.4f}s (成功率: {full_result['successful_searches']}/{full_result['total_queries']})")
            if subset_result:
                logger.info(f"   查询子集: {subset_result['average_time']:.4f}s (成功率: {subset_result['successful_searches']}/{subset_result['total_queries']})")
            else:
                logger.warning("   查询子集: 没有结果数据")
            
            # 计算性能提升
            if full_result and subset_result and subset_result.get('average_time', 0) > 0:
                speedup = full_result['average_time'] / subset_result['average_time']
                logger.info(f"   性能提升: {speedup:.2f}x")
        
        # 混合搜索结果对比
        hybrid_results = self.evaluation_results.get("hybrid_search", {})
        if hybrid_results:
            logger.info(f"\n4. 混合搜索时间对比:")
            full_result = hybrid_results.get("full_collection", {})
            subset_result = hybrid_results.get("query_subset", {})
            
            if full_result:
                logger.info(f"   整个集合: {full_result['average_time']:.4f}s (成功率: {full_result['successful_searches']}/{full_result['total_queries']})")
            if subset_result:
                logger.info(f"   查询子集: {subset_result['average_time']:.4f}s (成功率: {subset_result['successful_searches']}/{subset_result['total_queries']})")
            else:
                logger.warning("   查询子集: 没有结果数据")
            
            # 计算性能提升
            if full_result and subset_result and subset_result.get('average_time', 0) > 0:
                speedup = full_result['average_time'] / subset_result['average_time']
                logger.info(f"   性能提升: {speedup:.2f}x")
        
        logger.info("="*80)
    
    def save_results(self, output_path: str):
        """
        保存评估结果到文件
        
        参数:
            output_path: 输出文件路径
        """
        if not self.evaluation_results:
            logger.warning("没有评估结果可以保存")
            return
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        try:
            with open(output_path, 'wb') as f:
                pickle.dump(self.evaluation_results, f)
            logger.info(f"评估结果已保存到: {output_path}")
        except Exception as e:
            logger.error(f"保存评估结果失败: {e}")
            raise
    
    def cleanup(self):
        """清理资源"""
        try:
            if hasattr(self.milvus_manager, 'release_collection'):
                self.milvus_manager.release_collection(self.collection_name)
        except Exception as e:
            logger.warning(f"释放集合资源时出错: {e}")
    
    def _search_cad_by_shape_in_subset(self, query_vector: np.ndarray, top_k=5) -> List[Dict[str, Any]]:
        """
        在查询子集中进行形状搜索
        
        参数:
            query_vector: 查询向量
            top_k: 返回结果数量
            
        返回:
            搜索结果列表
        """
        try:
            if not self.query_subset_ids:
                logger.error("查询子集ID列表为空，无法执行子集形状搜索")
                return []
            
            self.milvus_manager.connect()
            collection = Collection(name=self.collection_name)
            collection.load()
            
            # 构建子集过滤条件
            id_list = ', '.join([f'"{aid}"' for aid in self.query_subset_ids])
            expr = f'pk in [{id_list}]'
            
            # 确保查询向量格式正确
            if hasattr(query_vector, 'tolist'):
                query_vector = query_vector.tolist()
            
            search_params = {"metric_type": "COSINE"}
            
            # 在子集中执行搜索
            results = collection.search(
                data=[query_vector],
                anns_field="shape_vector",
                param=search_params,
                limit=min(top_k, len(self.query_subset_ids)),  # 确保不超过子集大小
                expr=expr,  # 添加子集过滤条件
                output_fields=["pk", "type", "description"]
            )
            
            # 格式化返回结果
            formatted_results = []
            for hit in results[0]:
                formatted_results.append({
                    'id': hit.fields["pk"],
                    'type': hit.fields["type"],
                    'description': hit.fields["description"],
                    'score': hit.score
                })
            return formatted_results
            
        except Exception as e:
            logger.error(f"在子集中执行形状搜索时出错: {e}")
            return []
    
    def _search_cad_by_text_in_subset(self, query_text: str, top_k=5) -> List[Dict[str, Any]]:
        """
        在查询子集中进行文本搜索
        
        参数:
            query_text: 查询文本
            top_k: 返回结果数量
            
        返回:
            搜索结果列表
        """
        try:
            if not self.query_subset_ids:
                logger.error("查询子集ID列表为空，无法执行子集搜索")
                return []
            
            self.milvus_manager.connect()
            collection = Collection(name=self.collection_name)
            collection.load()
            
            # 构建子集过滤条件
            id_list = ', '.join([f'"{aid}"' for aid in self.query_subset_ids])
            expr = f'pk in [{id_list}]'
            
            # 生成查询向量
            query_embeddings = self.milvus_manager.generate_embeddings([query_text])
            
            # 准备搜索请求（在AnnSearchRequest中设置expr参数）
            sparse_search_params = {"metric_type": "IP"}
            sparse_req = AnnSearchRequest(
                query_embeddings["sparse"],
                "sparse_vector", 
                sparse_search_params, 
                limit=min(top_k, len(self.query_subset_ids)),
                expr=expr  # 在请求中设置过滤条件
            )
            
            dense_search_params = {"metric_type": "IP"}
            dense_req = AnnSearchRequest(
                query_embeddings["dense"],
                "dense_vector", 
                dense_search_params, 
                limit=min(top_k, len(self.query_subset_ids)),
                expr=expr  # 在请求中设置过滤条件
            )
            
            # 执行混合搜索（不再在hybrid_search中传递expr）
            results = collection.hybrid_search(
                [sparse_req, dense_req], 
                rerank=RRFRanker(),
                limit=min(top_k, len(self.query_subset_ids)),
                output_fields=["pk", "type", "description"]
            )
            
            # 格式化返回结果
            formatted_results = []
            for hit in results[0]:
                formatted_results.append({
                    'id': hit.fields["pk"],
                    'type': hit.fields["type"],
                    'description': hit.fields["description"],
                    'score': hit.score
                })
            
            return formatted_results
            
        except Exception as e:
            logger.error(f"在子集中执行文本搜索时出错: {e}")
            return []
    
    def _search_cad_hybrid_in_subset(self, query_text: str, query_shape_vector: np.ndarray, 
                                   top_k=5, shape_weight=0.3) -> List[Dict[str, Any]]:
        """
        在查询子集中进行混合搜索
        
        参数:
            query_text: 查询文本
            query_shape_vector: 查询形状向量
            top_k: 返回结果数量
            shape_weight: 形状搜索权重
            
        返回:
            搜索结果列表
        """
        try:
            if not self.query_subset_ids:
                logger.error("查询子集ID列表为空，无法执行子集混合搜索")
                return []
            
            self.milvus_manager.connect()
            collection = Collection(name=self.collection_name)
            collection.load()
            
            # 构建子集过滤条件
            id_list = ', '.join([f'"{aid}"' for aid in self.query_subset_ids])
            expr = f'pk in [{id_list}]'
            
            # 生成文本查询向量
            query_embeddings = self.milvus_manager.generate_embeddings([query_text])
            
            # 准备文本搜索请求（在AnnSearchRequest中设置expr参数）
            sparse_search_params = {"metric_type": "IP"}
            sparse_req = AnnSearchRequest(
                query_embeddings["sparse"],
                "sparse_vector", 
                sparse_search_params, 
                limit=min(top_k, len(self.query_subset_ids)),
                expr=expr  # 在请求中设置过滤条件
            )
            
            dense_search_params = {"metric_type": "IP"}
            dense_req = AnnSearchRequest(
                query_embeddings["dense"],
                "dense_vector", 
                dense_search_params, 
                limit=min(top_k, len(self.query_subset_ids)),
                expr=expr  # 在请求中设置过滤条件
            )
            
            search_requests = [sparse_req, dense_req]
            
            # 添加形状搜索请求
            if hasattr(query_shape_vector, 'tolist'):
                query_shape_vector = [query_shape_vector.tolist()]
            else:
                query_shape_vector = [query_shape_vector]
            
            shape_search_params = {"metric_type": "COSINE"}
            shape_req = AnnSearchRequest(
                query_shape_vector,
                "shape_vector", 
                shape_search_params, 
                limit=min(top_k, len(self.query_subset_ids)),
                expr=expr  # 在请求中设置过滤条件
            )
            search_requests.append(shape_req)
            
            # 执行混合搜索（不再在hybrid_search中传递expr）
            results = collection.hybrid_search(
                search_requests, 
                rerank=RRFRanker(),
                limit=min(top_k, len(self.query_subset_ids)),
                output_fields=["pk", "type", "description"]
            )
            
            # 格式化返回结果
            formatted_results = []
            for hit in results[0]:
                formatted_results.append({
                    'id': hit.fields["pk"],
                    'type': hit.fields["type"],
                    'description': hit.fields["description"],
                    'score': hit.score
                })
            
            return formatted_results
            
        except Exception as e:
            logger.error(f"在子集中执行混合搜索时出错: {e}")
            return []
    
    def create_query_subset(self, num_assemblies: int) -> List[str]:
        """
        从集合中随机选择指定数量的装配体作为查询子集
        
        参数:
            num_assemblies: 子集中装配体的数量
            
        返回:
            查询子集的装配体ID列表
        """
        logger.info(f"创建查询子集，大小: {self.query_subset_size}")
        
        self.milvus_manager.connect()
        collection = Collection(name=self.collection_name)
        collection.load()
        
        # 查询所有装配体
        assembly_results = collection.query(
            expr='type == "assembly"',
            output_fields=["pk"],
            limit=16384  # 设置一个较大的限制
        )
        
        if len(assembly_results) < self.query_subset_size:
            logger.warning(f"集合中只有 {len(assembly_results)} 个装配体，小于请求的子集大小 {self.query_subset_size}")
            self.query_subset_size = len(assembly_results)
        
        # 随机选择子集
        subset_assemblies = random.sample(assembly_results, self.query_subset_size)
        self.query_subset_ids = [assembly["pk"] for assembly in subset_assemblies]
        
        logger.info(f"查询子集创建完成，包含 {len(self.query_subset_ids)} 个装配体")
        return self.query_subset_ids

    def generate_comparison_table(self):
        """生成性能对比表格和可视化"""
        if not self.evaluation_results:
            logger.warning("没有评估结果可以生成表格")
            return
        
        logger.info("\n" + "="*80)
        logger.info("生成性能对比表格和可视化")
        logger.info("="*80)
        
        # 准备数据
        data = []
        
        # 处理形状搜索结果
        shape_results = self.evaluation_results.get("shape_search", {})
        if shape_results:
            full_result = shape_results.get("full_collection", {})
            subset_result = shape_results.get("query_subset", {})
            
            if full_result and subset_result:
                speedup = full_result.get('average_time', 0) / subset_result.get('average_time', 1)
                data.append({
                    '搜索类型': '形状搜索',
                    '整个集合平均时间(s)': f"{full_result.get('average_time', 0):.4f}",
                    '查询子集平均时间(s)': f"{subset_result.get('average_time', 0):.4f}",
                    '性能提升倍数': f"{speedup:.2f}x",
                    '整个集合成功率': f"{full_result.get('successful_searches', 0)}/{full_result.get('total_queries', 0)}",
                    '查询子集成功率': f"{subset_result.get('successful_searches', 0)}/{subset_result.get('total_queries', 0)}"
                })
        
        # 处理文本搜索结果
        text_results = self.evaluation_results.get("text_search", {})
        if text_results:
            full_result = text_results.get("full_collection", {})
            subset_result = text_results.get("query_subset", {})
            
            if full_result and subset_result:
                speedup = full_result.get('average_time', 0) / subset_result.get('average_time', 1)
                data.append({
                    '搜索类型': '文本搜索',
                    '整个集合平均时间(s)': f"{full_result.get('average_time', 0):.4f}",
                    '查询子集平均时间(s)': f"{subset_result.get('average_time', 0):.4f}",
                    '性能提升倍数': f"{speedup:.2f}x",
                    '整个集合成功率': f"{full_result.get('successful_searches', 0)}/{full_result.get('total_queries', 0)}",
                    '查询子集成功率': f"{subset_result.get('successful_searches', 0)}/{subset_result.get('total_queries', 0)}"
                })
        
        # 处理混合搜索结果
        hybrid_results = self.evaluation_results.get("hybrid_search", {})
        if hybrid_results:
            full_result = hybrid_results.get("full_collection", {})
            subset_result = hybrid_results.get("query_subset", {})
            
            if full_result and subset_result:
                speedup = full_result.get('average_time', 0) / subset_result.get('average_time', 1)
                data.append({
                    '搜索类型': '混合搜索',
                    '整个集合平均时间(s)': f"{full_result.get('average_time', 0):.4f}",
                    '查询子集平均时间(s)': f"{subset_result.get('average_time', 0):.4f}",
                    '性能提升倍数': f"{speedup:.2f}x",
                    '整个集合成功率': f"{full_result.get('successful_searches', 0)}/{full_result.get('total_queries', 0)}",
                    '查询子集成功率': f"{subset_result.get('successful_searches', 0)}/{subset_result.get('total_queries', 0)}"
                })
        
        if not data:
            logger.warning("没有足够的数据生成对比表格")
            return
        
        # 创建DataFrame
        df = pd.DataFrame(data)
        
        # 打印表格
        logger.info("\n性能对比表格:")
        logger.info("\n" + df.to_string(index=False, justify='center'))
        
        # 生成可视化图表
        self._generate_performance_charts(df)
        
        # 保存表格到CSV
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        csv_path = f"evaluate/search_time_consumption/performance_comparison_{timestamp}.csv"
        try:
            os.makedirs(os.path.dirname(csv_path), exist_ok=True)
            df.to_csv(csv_path, index=False, encoding='utf-8-sig')
            logger.info(f"性能对比表格已保存到: {csv_path}")
        except Exception as e:
            logger.error(f"保存CSV文件失败: {e}")
    
    def _generate_performance_charts(self, df: pd.DataFrame):
        """生成性能对比图表"""
        try:
            # 设置中文字体
            plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False
            
            # 创建图表
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
            fig.suptitle('向量检索性能对比分析', fontsize=16, fontweight='bold')
            
            # 提取数值数据用于绘图
            search_types = df['搜索类型'].tolist()
            full_times = [float(x) for x in df['整个集合平均时间(s)'].tolist()]
            subset_times = [float(x) for x in df['查询子集平均时间(s)'].tolist()]
            speedups = [float(x.replace('x', '')) for x in df['性能提升倍数'].tolist()]
            
            # 1. 平均搜索时间对比（条形图）
            x = np.arange(len(search_types))
            width = 0.35
            
            bars1 = ax1.bar(x - width/2, full_times, width, label='整个集合', alpha=0.8, color='skyblue')
            bars2 = ax1.bar(x + width/2, subset_times, width, label='查询子集', alpha=0.8, color='lightcoral')
            
            ax1.set_xlabel('搜索类型')
            ax1.set_ylabel('平均搜索时间 (秒)')
            ax1.set_title('平均搜索时间对比')
            ax1.set_xticks(x)
            ax1.set_xticklabels(search_types)
            ax1.legend()
            ax1.grid(axis='y', alpha=0.3)
            
            # 在条形图上添加数值标签
            for bar in bars1:
                height = bar.get_height()
                ax1.annotate(f'{height:.4f}', xy=(bar.get_x() + bar.get_width() / 2, height),
                           xytext=(0, 3), textcoords="offset points", ha='center', va='bottom', fontsize=8)
            for bar in bars2:
                height = bar.get_height()
                ax1.annotate(f'{height:.4f}', xy=(bar.get_x() + bar.get_width() / 2, height),
                           xytext=(0, 3), textcoords="offset points", ha='center', va='bottom', fontsize=8)
            
            # 2. 性能提升倍数（条形图）
            bars3 = ax2.bar(search_types, speedups, alpha=0.8, color='lightgreen')
            ax2.set_xlabel('搜索类型')
            ax2.set_ylabel('性能提升倍数')
            ax2.set_title('性能提升倍数')
            ax2.grid(axis='y', alpha=0.3)
            
            # 添加数值标签
            for bar in bars3:
                height = bar.get_height()
                ax2.annotate(f'{height:.2f}x', xy=(bar.get_x() + bar.get_width() / 2, height),
                           xytext=(0, 3), textcoords="offset points", ha='center', va='bottom')
            
            # 3. 搜索时间分布（箱线图模拟 - 使用散点图）
            for i, search_type in enumerate(search_types):
                ax3.scatter([i], [full_times[i]], s=100, alpha=0.7, label=f'{search_type}-整个集合' if i == 0 else "", color='skyblue')
                ax3.scatter([i], [subset_times[i]], s=100, alpha=0.7, label=f'{search_type}-查询子集' if i == 0 else "", color='lightcoral')
            
            ax3.set_xlabel('搜索类型')
            ax3.set_ylabel('搜索时间 (秒)')
            ax3.set_title('搜索时间分布')
            ax3.set_xticks(range(len(search_types)))
            ax3.set_xticklabels(search_types)
            ax3.grid(alpha=0.3)
            
            # 4. 相对性能改进（百分比）
            relative_improvement = [(full - subset) / full * 100 for full, subset in zip(full_times, subset_times)]
            bars4 = ax4.bar(search_types, relative_improvement, alpha=0.8, color='gold')
            ax4.set_xlabel('搜索类型')
            ax4.set_ylabel('相对性能改进 (%)')
            ax4.set_title('相对性能改进百分比')
            ax4.grid(axis='y', alpha=0.3)
            
            # 添加数值标签
            for bar in bars4:
                height = bar.get_height()
                ax4.annotate(f'{height:.1f}%', xy=(bar.get_x() + bar.get_width() / 2, height),
                           xytext=(0, 3), textcoords="offset points", ha='center', va='bottom')
            
            plt.tight_layout()
            
            # 保存图表
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            chart_path = f"evaluate/search_time_consumption/performance_charts_{timestamp}.png"
            try:
                os.makedirs(os.path.dirname(chart_path), exist_ok=True)
                plt.savefig(chart_path, dpi=300, bbox_inches='tight')
                logger.info(f"性能对比图表已保存到: {chart_path}")
            except Exception as e:
                logger.error(f"保存图表失败: {e}")
            
            # 显示图表（如果在支持的环境中）
            try:
                plt.show()
            except:
                logger.info("无法显示图表，但已保存到文件")
            
        except Exception as e:
            logger.error(f"生成图表时出错: {e}")
    
    # ...existing code...
def main():
    """主函数"""
    # 设置随机种子以确保结果可复现
    random.seed(42)
    np.random.seed(42)
    torch.manual_seed(42)
    
    # 配置参数
    num_assemblies = 1000
    num_clip_samples = 1000
    top_k = 10
    device = "cuda" if torch.cuda.is_available() else "cpu"
    use_reranker = True
    collection_name = "cad_collection_en"
    query_subset_size = 100  # 查询子集大小
    
    logger.info(f"开始性能评估，设备: {device}")
    logger.info(f"查询子集大小: {query_subset_size}")
    
    try:
        # 创建评估器
        evaluator = VectorRetrievalPerformanceEvaluator(
            collection_name=collection_name,
            device=device,
            use_reranker=use_reranker,
            query_subset_size=query_subset_size
        )
        
        # 运行评估
        results = evaluator.run_full_evaluation(
            num_assemblies=num_assemblies,
            num_clip_samples=num_clip_samples,
            top_k=top_k
        )
        
        # 保存结果
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        output_file = f"evaluate/search_time_consumption/vector_retrieval_performance_results_{timestamp}.pkl"
        evaluator.save_results(output_file)
        
        # 清理资源
        evaluator.cleanup()
        
        logger.info("性能评估完成!")
        
    except Exception as e:
        logger.error(f"性能评估过程中出错: {e}")
        raise


if __name__ == "__main__":
    main()
