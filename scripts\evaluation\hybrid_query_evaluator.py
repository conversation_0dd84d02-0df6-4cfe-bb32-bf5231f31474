#!/usr/bin/env python3
"""
混合查询评估器
用于评估多智能体系统在混合查询基准测试上的准确率
"""

import os
import sys
import json
import asyncio
import time
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import logging

# 添加项目根目录到系统路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.append(project_root)

from src.agents.multi_agent_coordinator import MultiAgentCoordinator

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@dataclass
class EvaluationResult:
    """单个查询的评估结果"""
    query_id: str
    query_type: str
    natural_language_prompt: str
    ground_truth_plan: Dict[str, Any]  # 真值查询计划
    predicted_plan: Optional[Dict[str, Any]]  # 预测的查询计划
    plan_is_correct: bool  # 任务规划是否准确
    ground_truth_uuids: List[str]
    predicted_uuids: List[str]
    is_correct: bool
    execution_time: float  # 总执行时间
    planning_time: float  # 查询规划时间
    retrieval_time: float  # 检索时间
    fusion_time: float  # 结果融合时间
    hit_at_1: bool  # 真值是否在前1个结果中
    hit_at_10: bool  # 真值是否在前10个结果中
    hit_at_all: bool  # 真值是否在整个结果列表中
    error_message: Optional[str] = None


@dataclass
class EvaluationSummary:
    """评估总结"""
    total_queries: int
    correct_predictions: int
    accuracy: float
    avg_execution_time: float
    planning_accuracy: float  # 任务规划准确率
    avg_planning_time: float  # 平均规划时间
    avg_retrieval_time: float  # 平均检索时间
    avg_fusion_time: float  # 平均融合时间
    overall_hit_rate_at_1: float  # 整体Hit Rate@1
    overall_hit_rate_at_10: float  # 整体Hit Rate@10
    overall_hit_rate_at_all: float  # 整体Hit Rate@all
    query_type_accuracy: Dict[str, float]
    query_type_hit_rates: Dict[str, Dict[str, float]]  # 按查询类型的Hit Rate@K
    query_type_avg_execution_time: Dict[str, float]  # 按查询类型的平均执行时间
    failed_queries: int
    error_rate: float


class HybridQueryEvaluator:
    """混合查询评估器"""
    
    def __init__(self, benchmark_dir: str = "data/benchmark"):
        """
        初始化评估器
        
        Args:
            benchmark_dir: 基准测试数据目录
        """
        self.benchmark_dir = Path(benchmark_dir)
        self.coordinator = MultiAgentCoordinator()
        self.results: List[EvaluationResult] = []
        
    async def initialize(self):
        """初始化多智能体协调器"""
        logger.info("正在初始化多智能体协调器...")
        await self.coordinator.initialize()
        logger.info("多智能体协调器初始化完成")
    
    async def evaluate_all_hybrid_queries(self, 
                                        max_assemblies: Optional[int] = None,
                                        top_k: int = 10) -> EvaluationSummary:
        """
        评估所有混合查询
        
        Args:
            max_assemblies: 最大评估装配体数量（None表示评估所有）
            top_k: 每个查询返回的结果数量
            
        Returns:
            评估总结
        """
        logger.info("开始评估混合查询...")
        
        # 获取所有装配体目录
        assembly_dirs = [d for d in self.benchmark_dir.iterdir() 
                        if d.is_dir() and d.name != "generation_statistics.json"]
        
        if max_assemblies:
            assembly_dirs = assembly_dirs[:max_assemblies]
            
        logger.info(f"找到 {len(assembly_dirs)} 个装配体，开始评估...")
        
        total_queries = 0
        processed_queries = 0
        
        for assembly_dir in assembly_dirs:
            hybrid_query_file = assembly_dir / "Hybrid_query.json"
            
            if not hybrid_query_file.exists():
                logger.warning(f"装配体 {assembly_dir.name} 缺少混合查询文件")
                continue
                
            # 加载混合查询
            try:
                with open(hybrid_query_file, 'r', encoding='utf-8') as f:
                    hybrid_queries = json.load(f)
                    
                total_queries += len(hybrid_queries)
                
                # 评估每个混合查询
                for query in hybrid_queries:
                    try:
                        result = await self._evaluate_single_query(query, top_k)
                        self.results.append(result)
                        processed_queries += 1
                        
                        logger.info(f"查询 {query['query_id']} 评估完成: "
                                  f"{'正确' if result.is_correct else '错误'} "
                                  f"(耗时: {result.execution_time:.2f}s)")
                        
                        # 添加延迟避免过于频繁的请求
                        await asyncio.sleep(1)
                        
                    except Exception as e:
                        logger.error(f"评估查询 {query.get('query_id', 'unknown')} 失败: {e}")
                        # 记录失败的查询
                        error_result = EvaluationResult(
                            query_id=query.get('query_id', 'unknown'),
                            query_type=query.get('query_type', 'unknown'),
                            natural_language_prompt=query.get('natural_language_prompt', ''),
                            ground_truth_plan=query.get('ground_truth_plan', {}),
                            predicted_plan=None,
                            plan_is_correct=False,
                            ground_truth_uuids=[],
                            predicted_uuids=[],
                            is_correct=False,
                            execution_time=0.0,
                            planning_time=0.0,
                            retrieval_time=0.0,
                            fusion_time=0.0,
                            hit_at_1=False,
                            hit_at_10=False,
                            error_message=str(e)
                        )
                        self.results.append(error_result)
                        
            except Exception as e:
                logger.error(f"加载装配体 {assembly_dir.name} 的混合查询失败: {e}")
                continue
        
        logger.info(f"评估完成: 总查询数 {total_queries}, 处理查询数 {processed_queries}")
        
        # 生成评估总结
        summary = self._generate_summary()
        return summary
    
    async def _evaluate_single_query(self, query: Dict[str, Any], top_k: int) -> EvaluationResult:
        """
        评估单个查询
        
        Args:
            query: 查询数据
            top_k: 返回结果数量
            
        Returns:
            评估结果
        """
        query_id = query['query_id']
        query_type = query['query_type']
        natural_language_prompt = query['natural_language_prompt']
        ground_truth_plan = query.get('ground_truth_plan', {})
        
        # 提取ground truth UUIDs
        ground_truth_uuids = []
        for gt_result in query.get('ground_truth_results', []):
            if 'uuid' in gt_result:
                ground_truth_uuids.append(gt_result['uuid'])
        
        start_time = time.time()
        predicted_plan = None
        plan_is_correct = False
        planning_time = 0.0
        retrieval_time = 0.0
        fusion_time = 0.0
        
        try:
            # 第一步：查询规划阶段
            planning_start = time.time()
            
            # 执行查询并获取详细的执行信息
            result = await self.coordinator.execute_multi_modal_query(
                query_text=natural_language_prompt,
                shape_vector=None,  # 混合查询通常不包含形状向量
                top_k=top_k
            )
            
            planning_time = time.time() - planning_start
            
            # 检查查询规划是否正确
            if hasattr(result, 'execution_plan') and result.execution_plan:
                predicted_plan = {
                    "sub_queries": result.execution_plan.sub_queries,
                    "fusion_strategy": result.execution_plan.fusion_strategy
                }
                plan_is_correct = self._compare_query_plans(ground_truth_plan, predicted_plan)
            else:
                # 如果没有执行计划，尝试从结果元数据中获取
                predicted_plan = None
                plan_is_correct = False
            
            # 模拟检索和融合时间（实际实现中应该从coordinator获取）
            # 这里我们将总时间按比例分配
            total_time = time.time() - start_time
            if total_time > planning_time:
                remaining_time = total_time - planning_time
                retrieval_time = remaining_time * 0.7  # 假设检索占剩余时间的70%
                fusion_time = remaining_time * 0.3    # 假设融合占剩余时间的30%
            
            execution_time = time.time() - start_time
            
            # 提取预测的UUIDs
            predicted_uuids = []
            if result.status == 'success' and result.results:
                predicted_uuids = [item.uuid for item in result.results]
            
            # 判断是否正确：预测结果中是否包含任何ground truth UUID
            is_correct = any(uuid in predicted_uuids for uuid in ground_truth_uuids)
            
            # 计算Hit Rate@K
            hit_at_1 = False
            hit_at_10 = False
            hit_at_all = False
            
            if predicted_uuids and ground_truth_uuids:
                # Hit@1: 检查前1个结果中是否包含真值
                if len(predicted_uuids) >= 1:
                    hit_at_1 = any(uuid in predicted_uuids[:1] for uuid in ground_truth_uuids)
                
                # Hit@10: 检查前10个结果中是否包含真值
                if len(predicted_uuids) >= 1:
                    hit_at_10 = any(uuid in predicted_uuids[:10] for uuid in ground_truth_uuids)
                
                # Hit@all: 检查整个结果列表中是否包含真值
                hit_at_all = any(uuid in predicted_uuids for uuid in ground_truth_uuids)
            
            return EvaluationResult(
                query_id=query_id,
                query_type=query_type,
                natural_language_prompt=natural_language_prompt,
                ground_truth_plan=ground_truth_plan,
                predicted_plan=predicted_plan,
                plan_is_correct=plan_is_correct,
                ground_truth_uuids=ground_truth_uuids,
                predicted_uuids=predicted_uuids,
                is_correct=is_correct,
                execution_time=execution_time,
                planning_time=planning_time,
                retrieval_time=retrieval_time,
                fusion_time=fusion_time,
                hit_at_1=hit_at_1,
                hit_at_10=hit_at_10,
                hit_at_all=hit_at_all,
                error_message=result.error_message if result.status != 'success' else None
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            return EvaluationResult(
                query_id=query_id,
                query_type=query_type,
                natural_language_prompt=natural_language_prompt,
                ground_truth_plan=ground_truth_plan,
                predicted_plan=None,
                plan_is_correct=False,
                ground_truth_uuids=ground_truth_uuids,
                predicted_uuids=[],
                is_correct=False,
                execution_time=execution_time,
                planning_time=planning_time,
                retrieval_time=retrieval_time,
                fusion_time=fusion_time,
                hit_at_1=False,
                hit_at_10=False,
                hit_at_all=False,
                error_message=str(e)
            )
    
    def _compare_query_plans(self, ground_truth_plan: Dict[str, Any], predicted_plan: Dict[str, Any]) -> bool:
        """
        比较查询计划是否匹配
        
        Args:
            ground_truth_plan: 真值查询计划
            predicted_plan: 预测的查询计划
            
        Returns:
            是否匹配
        """
        if not ground_truth_plan or not predicted_plan:
            return False
            
        # 比较融合策略
        gt_fusion = ground_truth_plan.get('fusion_strategy', '').upper()
        pred_fusion = predicted_plan.get('fusion_strategy', '').upper()
        if gt_fusion != pred_fusion:
            return False
        
        # 比较子查询数量
        gt_sub_queries = ground_truth_plan.get('sub_queries', [])
        pred_sub_queries = predicted_plan.get('sub_queries', [])
        if len(gt_sub_queries) != len(pred_sub_queries):
            return False
        
        # 提取智能体类型进行比较
        gt_agents = set()
        for sub_query in gt_sub_queries:
            agent_name = sub_query.get('agent', '')
            # 标准化智能体名称，与基准数据集中的智能体名称保持一致
            if 'AttributeFilteringAgent' in agent_name:
                gt_agents.add('AttributeFilteringAgent')
            elif 'StructuralTopologyAgent' in agent_name:
                gt_agents.add('StructuralTopologyAgent')
            elif 'GeometrySemanticAgent' in agent_name:
                gt_agents.add('GeometrySemanticAgent')
        
        pred_agents = set()
        for sub_query in pred_sub_queries:
            agent_name = sub_query.get('agent', '')
            # 标准化智能体名称，与基准数据集中的智能体名称保持一致
            if 'AttributeFilteringAgent' in agent_name:
                pred_agents.add('AttributeFilteringAgent')
            elif 'StructuralTopologyAgent' in agent_name:
                pred_agents.add('StructuralTopologyAgent')
            elif 'GeometrySemanticAgent' in agent_name:
                pred_agents.add('GeometrySemanticAgent')
        
        return gt_agents == pred_agents
    
    def _generate_summary(self) -> EvaluationSummary:
        """生成评估总结"""
        total_queries = len(self.results)
        correct_predictions = sum(1 for r in self.results if r.is_correct)
        failed_queries = sum(1 for r in self.results if r.error_message is not None)
        
        accuracy = correct_predictions / total_queries if total_queries > 0 else 0.0
        error_rate = failed_queries / total_queries if total_queries > 0 else 0.0
        
        # 获取成功的查询结果
        successful_results = [r for r in self.results if r.error_message is None]
        
        # 计算平均执行时间
        avg_execution_time = sum(r.execution_time for r in successful_results) / len(successful_results) if successful_results else 0.0
        
        # 计算任务规划准确率
        planning_correct = sum(1 for r in successful_results if r.plan_is_correct)
        planning_accuracy = planning_correct / len(successful_results) if successful_results else 0.0
        
        # 计算各阶段平均时间
        avg_planning_time = sum(r.planning_time for r in successful_results) / len(successful_results) if successful_results else 0.0
        avg_retrieval_time = sum(r.retrieval_time for r in successful_results) / len(successful_results) if successful_results else 0.0
        avg_fusion_time = sum(r.fusion_time for r in successful_results) / len(successful_results) if successful_results else 0.0
        
        # 计算整体Hit Rate@K
        overall_hit_at_1 = sum(1 for r in successful_results if r.hit_at_1) / len(successful_results) if successful_results else 0.0
        overall_hit_at_10 = sum(1 for r in successful_results if r.hit_at_10) / len(successful_results) if successful_results else 0.0
        overall_hit_at_all = sum(1 for r in successful_results if r.hit_at_all) / len(successful_results) if successful_results else 0.0
        
        # 按查询类型计算各项指标
        query_type_accuracy = {}
        query_type_hit_rates = {}
        query_type_avg_execution_time = {}
        query_types = set(r.query_type for r in self.results)
        
        for query_type in query_types:
            type_results = [r for r in self.results if r.query_type == query_type]
            type_successful = [r for r in type_results if r.error_message is None]
            
            # 准确率
            type_correct = sum(1 for r in type_results if r.is_correct)
            type_accuracy = type_correct / len(type_results) if type_results else 0.0
            query_type_accuracy[query_type] = type_accuracy
            
            # Hit Rate@K
            if type_successful:
                hit_at_1 = sum(1 for r in type_successful if r.hit_at_1) / len(type_successful)
                hit_at_10 = sum(1 for r in type_successful if r.hit_at_10) / len(type_successful)
                hit_at_all = sum(1 for r in type_successful if r.hit_at_all) / len(type_successful)
            else:
                hit_at_1 = hit_at_10 = hit_at_all = 0.0
            
            query_type_hit_rates[query_type] = {
                'hit_rate_at_1': hit_at_1,
                'hit_rate_at_10': hit_at_10,
                'hit_rate_at_all': hit_at_all
            }
            
            # 平均执行时间
            avg_time = sum(r.execution_time for r in type_successful) / len(type_successful) if type_successful else 0.0
            query_type_avg_execution_time[query_type] = avg_time
        
        return EvaluationSummary(
            total_queries=total_queries,
            correct_predictions=correct_predictions,
            accuracy=accuracy,
            avg_execution_time=avg_execution_time,
            planning_accuracy=planning_accuracy,
            avg_planning_time=avg_planning_time,
            avg_retrieval_time=avg_retrieval_time,
            avg_fusion_time=avg_fusion_time,
            overall_hit_rate_at_1=overall_hit_at_1,
            overall_hit_rate_at_10=overall_hit_at_10,
            overall_hit_rate_at_all=overall_hit_at_all,
            query_type_accuracy=query_type_accuracy,
            query_type_hit_rates=query_type_hit_rates,
            query_type_avg_execution_time=query_type_avg_execution_time,
            failed_queries=failed_queries,
            error_rate=error_rate
        )
    
    def save_detailed_results(self, output_file: str):
        """保存详细的评估结果"""
        results_data = []
        
        for result in self.results:
            results_data.append({
                'query_id': result.query_id,
                'query_type': result.query_type,
                'natural_language_prompt': result.natural_language_prompt,
                'ground_truth_plan': result.ground_truth_plan,
                'predicted_plan': result.predicted_plan,
                'plan_is_correct': result.plan_is_correct,
                'ground_truth_uuids': result.ground_truth_uuids,
                'predicted_uuids': result.predicted_uuids,
                'is_correct': result.is_correct,
                'execution_time': result.execution_time,
                'planning_time': result.planning_time,
                'retrieval_time': result.retrieval_time,
                'fusion_time': result.fusion_time,
                'hit_at_1': result.hit_at_1,
                'hit_at_10': result.hit_at_10,
                'hit_at_all': result.hit_at_all,
                'error_message': result.error_message
            })
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"详细评估结果已保存到: {output_file}")
    
    def print_summary(self, summary: EvaluationSummary):
        """打印评估总结"""
        print("\n" + "="*90)
        print("混合查询评估结果总结")
        print("="*90)
        print(f"总查询数: {summary.total_queries}")
        print(f"正确预测数: {summary.correct_predictions}")
        print(f"整体准确率: {summary.accuracy:.2%}")
        print(f"平均执行时间: {summary.avg_execution_time:.2f}秒")
        print(f"失败查询数: {summary.failed_queries}")
        print(f"错误率: {summary.error_rate:.2%}")
        
        print(f"\n🎯 任务规划性能:")
        print("-" * 50)
        print(f"任务规划准确率: {summary.planning_accuracy:.2%}")
        
        print(f"\n📊 整体检索性能指标:")
        print("-" * 50)
        print(f"Hit Rate@1:   {summary.overall_hit_rate_at_1:.2%}")
        print(f"Hit Rate@10:  {summary.overall_hit_rate_at_10:.2%}")
        print(f"Hit Rate@all: {summary.overall_hit_rate_at_all:.2%}")
        
        print(f"\n⏱️ 系统效率分析:")
        print("-" * 50)
        print(f"平均查询规划时间: {summary.avg_planning_time:.2f}秒")
        print(f"平均检索系统时间: {summary.avg_retrieval_time:.2f}秒")
        print(f"平均结果融合时间: {summary.avg_fusion_time:.2f}秒")
        
        if summary.avg_execution_time > 0:
            planning_pct = summary.avg_planning_time / summary.avg_execution_time * 100
            retrieval_pct = summary.avg_retrieval_time / summary.avg_execution_time * 100
            fusion_pct = summary.avg_fusion_time / summary.avg_execution_time * 100
            print(f"总执行时间占比: 规划({planning_pct:.1f}%) + "
                  f"检索({retrieval_pct:.1f}%) + "
                  f"融合({fusion_pct:.1f}%)")
        else:
            print("总执行时间占比: 无有效数据")
        
        print(f"\n📈 按查询类型的详细指标:")
        print("-" * 100)
        print(f"{'查询类型':<30} {'准确率':<10} {'Hit@1':<10} {'Hit@10':<10} {'Hit@all':<10} {'平均耗时(s)':<12}")
        print("-" * 100)
        for query_type in summary.query_type_accuracy.keys():
            accuracy = summary.query_type_accuracy[query_type]
            hit_rates = summary.query_type_hit_rates.get(query_type, {'hit_rate_at_1': 0.0, 'hit_rate_at_10': 0.0, 'hit_rate_at_all': 0.0})
            avg_time = summary.query_type_avg_execution_time.get(query_type, 0.0)
            
            print(f"{query_type:<30} {accuracy:.2%}     {hit_rates['hit_rate_at_1']:.2%}    {hit_rates['hit_rate_at_10']:.2%}    {hit_rates['hit_rate_at_all']:.2%}     {avg_time:.2f}")
        
        print("="*100)


async def main():
    """主函数"""
    evaluator = HybridQueryEvaluator()
    
    try:
        # 初始化评估器
        await evaluator.initialize()
        
        # 执行评估（可以设置max_assemblies限制评估数量）
        summary = await evaluator.evaluate_all_hybrid_queries(
            max_assemblies=10,  # 先评估10个装配体进行测试
            top_k=10
        )
        
        # 打印总结
        evaluator.print_summary(summary)
        
        # 保存详细结果
        output_dir = Path("results/evaluation")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        detailed_results_file = output_dir / f"hybrid_query_evaluation_{timestamp}.json"
        evaluator.save_detailed_results(str(detailed_results_file))
        
        # 保存总结
        summary_file = output_dir / f"evaluation_summary_{timestamp}.json"
        summary_data = {
            'total_queries': summary.total_queries,
            'correct_predictions': summary.correct_predictions,
            'accuracy': summary.accuracy,
            'avg_execution_time': summary.avg_execution_time,
            'planning_accuracy': summary.planning_accuracy,
            'avg_planning_time': summary.avg_planning_time,
            'avg_retrieval_time': summary.avg_retrieval_time,
            'avg_fusion_time': summary.avg_fusion_time,
            'overall_hit_rate_at_1': summary.overall_hit_rate_at_1,
            'overall_hit_rate_at_10': summary.overall_hit_rate_at_10,
            'query_type_accuracy': summary.query_type_accuracy,
            'query_type_hit_rates': summary.query_type_hit_rates,
            'query_type_avg_execution_time': summary.query_type_avg_execution_time,
            'failed_queries': summary.failed_queries,
            'error_rate': summary.error_rate
        }
        
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"评估总结已保存到: {summary_file}")
        
    except Exception as e:
        logger.error(f"评估过程中发生错误: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
