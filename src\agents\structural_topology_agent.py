"""
结构拓扑智能体 (Structural Topology Agent)
与Neo4j图数据库交互，实现Text2Cypher功能
"""

import logging
import asyncio
from typing import List, Dict, Any, Optional
from neo4j import GraphDatabase
from src.agents.base_agent import BaseAgent
from src.agents.data_models import QueryResult, UnifiedStructuralQueryTask, SearchResultItem
from src.models.LLM import MultiProviderLLM
from src.config import Config

logger = logging.getLogger(__name__)


class StructuralTopologyAgent(BaseAgent):
    """
    结构拓扑智能体
    
    该智能体专门处理零件之间的结构关系查询，基于Neo4j图数据库进行操作。
    它专注于探索产品内部的"设计蓝图"——即组件间的BOM（物料清单）层级关系和物理连接约束。
    """
    
    def __init__(self, agent_name: str = "StructuralTopologyAgent"):
        super().__init__(agent_name)
        self.driver = None
        self.llm = None
        self.schema_info = None
        self._connected = False
        
    async def connect(self):
        """连接到Neo4j图数据库"""
        try:
            # 建立数据库连接
            neo4j_config = Config.get_neo4j_config()
            self.driver = GraphDatabase.driver(
                neo4j_config["uri"],
                auth=(neo4j_config["user"], neo4j_config["password"])
            )
            
            # 测试连接
            with self.driver.session() as session:
                session.run("RETURN 1")
            
            # 初始化LLM
            self.llm = MultiProviderLLM()
            
            # 加载图数据库模式信息
            await self._load_schema_info()
            
            logger.info(f"{self.agent_name} 成功连接到Neo4j数据库")
            
        except Exception as e:
            logger.error(f"{self.agent_name} 连接Neo4j失败: {e}")
            raise
    
    async def disconnect(self):
        """断开数据库连接"""
        try:
            if self.driver:
                self.driver.close()
            logger.info(f"{self.agent_name} 已断开Neo4j连接")
        except Exception as e:
            logger.error(f"断开连接时出错: {e}")
    
    async def _load_schema_info(self):
        """加载图数据库模式信息"""
        try:
            # 直接使用默认模式信息，不从数据库获取
            self.schema_info = self._get_default_schema()
            logger.info("Neo4j默认模式信息加载完成")
                
        except Exception as e:
            logger.error(f"加载Neo4j模式信息失败: {e}")
            # 使用默认模式信息
            self.schema_info = self._get_default_schema()
    
    def _get_default_schema(self) -> Dict[str, Any]:
        """获取默认的Neo4j模式信息"""
        return {
            "node_labels": ["Assembly", "SubAssembly", "Part", "Feature"],
            "relationship_types": ["hasComponent", "hasFeature"],
            "node_properties": {
                "Assembly": ["uuid", "name", "description", "area", "density", "mass", "volume", "category", "industry", "shape_embedding", "description_embedding"],
                "SubAssembly": ["uuid", "name", "area", "density", "mass", "volume"],
                "Part": ["uuid", "name", "description", "area", "density", "mass", "volume", "material", "shape_embedding", "description_embedding"],
                "Feature": ["uuid", "name", "type", "diameter", "length"]
            },
            "relationship_patterns": [
                {"start_label": "Assembly", "relationship": "hasComponent", "end_label": "SubAssembly"},
                {"start_label": "SubAssembly", "relationship": "hasComponent", "end_label": "SubAssembly"},
                {"start_label": "Assembly", "relationship": "hasComponent", "end_label": "Part"},
                {"start_label": "SubAssembly", "relationship": "hasComponent", "end_label": "Part"},
                {"start_label": "Part", "relationship": "hasFeature", "end_label": "Feature"}
            ],
            "description": "CAD装配体图数据库，存储零件、子装配体和制造特征的层级关系"
        }
    
    def _build_system_prompt(self) -> str:
        """构建Text2Cypher的系统提示词"""
        schema_str = self._format_schema_for_prompt()
        
        return f"""You are a professional Cypher query generator. Your task is to convert user's natural language questions into accurate Cypher query statements.

Neo4j Database Schema Information:
{schema_str}

Important Rules:
1. Return only Cypher query statements, do not include any explanations or preamble
2. Query statements must conform to Neo4j Cypher syntax
3. Use correct node labels and relationship types
4. For fuzzy queries, use CONTAINS, STARTS WITH or regular expressions for fuzzy matching
5. Do not limit the number of returned results, do not add LIMIT clause
6. Use RETURN clause to explicitly specify attributes to return
7. Use WHERE clause appropriately for filtering
8. For path queries, use variable-length path syntax like [:hasComponent*1..3]
9. CRITICAL: When using hasComponent relationships, ALWAYS use :Part|SubAssembly for target nodes since components can be either parts or subassemblies
10. NEVER use only :Part or only :SubAssembly in hasComponent relationships unless specifically filtering by component type

Common Query Patterns:
- Find all components in an assembly: MATCH (a:Assembly)-[:hasComponent*]->(c:Part|SubAssembly) WHERE a.name CONTAINS 'name' RETURN c
- Find parent assembly of a component: MATCH (c:Part|SubAssembly)<-[:hasComponent]-(parent) WHERE c.name = 'component name' RETURN parent
- Find parts with specific features: MATCH (p:Part)-[:hasFeature]->(f:Feature) WHERE f.type = 'feature type' RETURN p
- Find assembly hierarchy: MATCH path=(a:Assembly)-[:hasComponent*]->(s:Part|SubAssembly) RETURN path
- Find assemblies containing specific components (e.g., containing components A, B, and C): MATCH (a:Assembly) WHERE ALL(name IN ['Component A', 'Component B', 'Component C'] WHERE (a)-[:hasComponent*1..]->(:Part|SubAssembly {{name: name}}) ) RETURN a.uuid, a.name, a.description

CRITICAL: When using hasComponent relationships, ALWAYS use :Part|SubAssembly for target nodes since components can be either parts or subassemblies. Never use only :Part or only :SubAssembly unless you specifically need to filter by component type.

"""
    
    def _format_schema_for_prompt(self) -> str:
        """格式化Neo4j模式信息用于提示词"""
        if not self.schema_info:
            return "图数据库模式信息未加载"
        
        schema_parts = []
        
        # 节点标签信息
        schema_parts.append("## 节点类型:")
        for label in self.schema_info["node_labels"]:
            properties = self.schema_info["node_properties"].get(label, [])
            props_str = ", ".join(properties) if properties else "无属性信息"
            schema_parts.append(f"- {label}: {self._get_node_description(label)} (属性: {props_str})")
        
        # 关系类型信息
        schema_parts.append("\n## 关系类型:")
        for rel_type in self.schema_info["relationship_types"]:
            schema_parts.append(f"- {rel_type}: {self._get_relationship_description(rel_type)}")
        
        # 关系模式
        schema_parts.append("\n## 关系模式:")
        for pattern in self.schema_info["relationship_patterns"]:
            schema_parts.append(f"- ({pattern['start_label']})-[:{pattern['relationship']}]->({pattern['end_label']})")
        
        return "\n".join(schema_parts)
    
    def _get_node_description(self, label: str) -> str:
        """获取节点标签的描述"""
        descriptions = {
            "Assembly": "装配体",
            "SubAssembly": "子装配体", 
            "Part": "零件",
            "Feature": "制造特征，目前只有孔特征"
        }
        return descriptions.get(label, "未知节点类型")
    
    def _get_relationship_description(self, rel_type: str) -> str:
        """获取关系类型的描述"""
        descriptions = {
            "hasComponent": "包含组件（子装配体或零件）",
            "hasFeature": "具有制造特征"
        }
        return descriptions.get(rel_type, "未知关系类型")
    
    async def execute_task(self, task: UnifiedStructuralQueryTask) -> QueryResult:
        """
        执行结构关系查询任务

        Args:
            task: UnifiedStructuralQueryTask实例，包含自然语言查询文本和可选的ID列表

        Returns:
            QueryResult: 包含查询结果的查询结果对象
        """
        import time
        start_time = time.time()

        try:
            if self.driver is None:
                await self.connect()            # 将自然语言转换为Cypher查询，如果有ID列表则包含在提示中
            cypher_query = await self.text_to_cypher(task.query_text, task.id_list)

            # 执行Cypher查询
            raw_results = await self._execute_cypher_query(cypher_query)

            # 转换为标准化的SearchResultItem格式
            search_results = []
            for i, result in enumerate(raw_results):
                search_results.append(self._convert_to_search_result_item(result, i + 1))

            execution_time = time.time() - start_time

            return QueryResult(
                task_id=task.task_id,
                status='success',
                results=search_results,
                execution_time=execution_time,
                total_results=len(search_results)
            )

        except Exception as e:
            logger.error(f"{self.agent_name} 执行任务失败: {e}")
            execution_time = time.time() - start_time
            return QueryResult(
                task_id=task.task_id,
                status='failure',
                error_message=str(e),
                results=[],
                execution_time=execution_time,
                total_results=0
            )



    def _convert_to_search_result_item(self, result: Dict[str, Any], rank: int) -> SearchResultItem:
        """
        将Neo4j查询结果转换为SearchResultItem

        Args:
            result: Neo4j查询结果
            rank: 排名

        Returns:
            SearchResultItem实例
        """
        try:
            # 尝试从结果中提取节点信息
            node_id = ""
            node_name = ""
            node_description = ""

            # 查找节点信息
            for key, value in result.items():
                if isinstance(value, dict) and 'properties' in value:
                    # 这是一个节点对象
                    props = value['properties']
                    node_id = props.get('id', props.get('uuid', str(rank)))
                    node_name = props.get('name', f'Node_{rank}')
                    node_description = props.get('description', '')
                    break
                elif isinstance(value, str) and key in ['id', 'uuid']:
                    node_id = value
                elif isinstance(value, str) and key in ['name']:
                    node_name = value
                elif isinstance(value, str) and key in ['description']:
                    node_description = value

            # 如果没有找到合适的信息，使用默认值
            if not node_id:
                node_id = str(rank)
            if not node_name:
                node_name = f'Item_{rank}'

            return SearchResultItem(
                rank=rank,
                uuid=node_id,
                name=node_name,
                description=node_description,
                search_type='structural',
                metadata=result
            )

        except Exception as e:
            logger.error(f"转换搜索结果项失败: {e}")
            return SearchResultItem(
                rank=rank,
                uuid=str(rank),
                name=f'Item_{rank}',
                description='',
                search_type='structural',
                metadata=result
            )

    async def _execute_cypher_query(self, cypher_query: str) -> List[Dict[str, Any]]:
        """
        执行Cypher查询
        
        Args:
            cypher_query: Cypher查询语句
            
        Returns:
            查询结果列表
        """
        try:
            with self.driver.session() as session:
                result = session.run(cypher_query)
                
                # 转换结果为字典列表
                results = []
                for record in result:
                    # 处理复杂的Neo4j记录格式
                    record_dict = {}
                    for key, value in record.items():
                        record_dict[key] = self._convert_neo4j_value(value)
                    results.append(record_dict)
                
                logger.info(f"Cypher查询成功，返回{len(results)}条记录")
                return results
                
        except Exception as e:
            logger.error(f"Cypher查询执行失败: {e}")
            raise
    
    def _convert_neo4j_value(self, value):
        """
        转换Neo4j特定的数据类型为Python基本数据类型
        
        Args:
            value: Neo4j返回的值
            
        Returns:
            转换后的Python值
        """
        # 处理节点对象
        if hasattr(value, 'labels') and hasattr(value, 'items'):
            return {
                'labels': list(value.labels),
                'properties': dict(value.items())
            }
        
        # 处理关系对象
        elif hasattr(value, 'type') and hasattr(value, 'start_node'):
            return {
                'type': value.type,
                'properties': dict(value.items()),
                'start_node': value.start_node.element_id if hasattr(value.start_node, 'element_id') else str(value.start_node.id),
                'end_node': value.end_node.element_id if hasattr(value.end_node, 'element_id') else str(value.end_node.id)
            }
        
        # 处理路径对象
        elif hasattr(value, 'nodes') and hasattr(value, 'relationships'):
            return {
                'nodes': [self._convert_neo4j_value(node) for node in value.nodes],
                'relationships': [self._convert_neo4j_value(rel) for rel in value.relationships]
            }
        
        # 处理列表
        elif isinstance(value, list):
            return [self._convert_neo4j_value(item) for item in value]
          # 基本数据类型直接返回
        else:
            return value
    
    async def text_to_cypher(self, query_text: str, id_list: Optional[List[str]] = None) -> str:
        """
        将自然语言查询转换为Cypher语句

        Args:
            query_text: 自然语言查询
            id_list: 可选的ID列表，用于缩小查询范围

        Returns:
            生成的Cypher查询语句
        """
        try:
            if self.llm is None:
                await self.connect()

            system_prompt = self._build_system_prompt()

            # 构建用户查询内容
            user_content = f"用户查询: {query_text}"

            # 如果提供了ID列表，添加约束信息但使用占位符
            if id_list:
                user_content += "\n\n额外约束: 只查询特定uuid的节点"
                user_content += "\n请在WHERE子句中添加: n.uuid IN [__ID_LIST_PLACEHOLDER__]"

            user_content += "\n\n请生成相应的Cypher查询语句:"

            # 构建消息列表用于LLM
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_content}
            ]

            # 调用LLM生成Cypher
            response = self.llm.chat(messages)

            # 清理响应，提取Cypher语句
            cypher_query = self._clean_cypher_response(response)

            # 如果有ID列表，替换占位符
            if id_list:
                id_list_str = "', '".join(id_list)
                cypher_query = cypher_query.replace("__ID_LIST_PLACEHOLDER__", f"'{id_list_str}'")

            logger.info(f"Text2Cypher转换完成: {query_text} -> {cypher_query}")
            return cypher_query

        except Exception as e:
            logger.error(f"Text2Cypher转换失败: {e}")
            raise
    
    def _clean_cypher_response(self, response: str) -> str:
        """
        清理LLM响应，提取纯净的Cypher语句
        
        Args:
            response: LLM的原始响应
            
        Returns:
            清理后的Cypher语句
        """
        # 移除markdown代码块标记
        response = response.strip()
        if response.startswith("```cypher"):
            response = response[9:]
        elif response.startswith("```"):
            response = response[3:]
        
        if response.endswith("```"):
            response = response[:-3]
        
        # 移除多余的空行和注释
        lines = response.split('\n')
        clean_lines = []
        for line in lines:
            line = line.strip()
            if line and not line.startswith('//'):
                clean_lines.append(line)
        
        cypher_query = ' '.join(clean_lines)
        
        return cypher_query
    

