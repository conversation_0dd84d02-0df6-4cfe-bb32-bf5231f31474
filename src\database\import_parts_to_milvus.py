import os
import sys
import argparse
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.database.milvus_utils import load_from_clip_features_to_milvus


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='将零件CLIP特征导入到Milvus数据库')
    parser.add_argument('--pkl_path', type=str, default='data/features/parts/clip_features.pkl',
                        help='CLIP特征的pickle文件路径')
    parser.add_argument('--collection', type=str, default='parts',
                        help='Milvus集合名称')
    parser.add_argument('--host', type=str, default='127.0.0.1',
                        help='Milvus服务器主机')
    parser.add_argument('--port', type=str, default='19530',
                        help='Milvus服务器端口')
    return parser.parse_args()


def main():
    """主函数"""
    args = parse_args()
    
    # 确保pickle文件存在
    if not os.path.exists(args.pkl_path):
        print(f"错误：文件 '{args.pkl_path}' 不存在")
        return 1
    
    print(f"开始导入CLIP特征到Milvus...")
    print(f"- 数据源: {args.pkl_path}")
    print(f"- 目标集合: {args.collection}")
    print(f"- Milvus服务器: {args.host}:{args.port}")
    
    try:
        # 导入数据
        inserted_count = load_from_clip_features_to_milvus(
            pkl_path=args.pkl_path,
            collection_name=args.collection,
            host=args.host,
            port=args.port
        )
        
        print(f"导入完成！成功导入 {inserted_count} 个零件")
        return 0
        
    except Exception as e:
        print(f"导入过程中发生错误: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main()) 