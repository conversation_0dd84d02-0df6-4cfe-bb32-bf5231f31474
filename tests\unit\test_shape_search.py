#!/usr/bin/env python3
"""
测试search_cad_by_shape的效果
输入test文件夹路径，对每个子文件夹的assembly.png进行形状搜索，
然后将输入图片和检索到的前5张图片整合显示并保存
"""

import os
import sys
import asyncio
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt
import matplotlib.image as mpimg
from PIL import Image
import torch

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.models.clip import CLIPFeatureExtractor
from src.agents.agent_manager import AgentManager


class ShapeSearchTester:
    def __init__(self, test_folder_path: str, results_folder_path: str):
        """
        初始化测试器
        
        Args:
            test_folder_path: test文件夹路径
            results_folder_path: 结果保存文件夹路径
        """
        self.test_folder_path = Path(test_folder_path)
        self.results_folder_path = Path(results_folder_path)
        self.results_folder_path.mkdir(exist_ok=True)
        
        # 初始化CLIP特征提取器
        self.clip_extractor = CLIPFeatureExtractor()
        print(f"CLIP模型加载完成，使用设备: {self.clip_extractor.device}")
        
        # 初始化智能体管理器
        self.agent_manager = AgentManager()
        
        # 数据集路径
        self.dataset_path = Path("datasets/fusion360_assembly")
        
    async def initialize_agents(self):
        """初始化智能体管理器"""
        try:
            await self.agent_manager.connect_all()
            print("智能体管理器初始化完成")
        except Exception as e:
            print(f"智能体管理器初始化失败: {e}")
            raise
    
    def extract_shape_features(self, image_path: str) -> np.ndarray:
        """
        提取图像的形状特征向量
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            形状特征向量
        """
        try:
            # 加载并预处理图像
            image = Image.open(image_path).convert('RGB')
            processed_image = self.clip_extractor.preprocess(image).unsqueeze(0)
            
            # 提取特征
            with torch.no_grad():
                features = self.clip_extractor(processed_image, normalize=True)
                return features.cpu().numpy().flatten()
                
        except Exception as e:
            print(f"提取特征失败 {image_path}: {e}")
            return None
    
    async def search_by_shape(self, shape_vector: list, top_k: int = 5) -> list:
        """
        使用形状向量进行搜索
        
        Args:
            shape_vector: 形状特征向量
            top_k: 返回结果数量
            
        Returns:
            搜索结果列表
        """
        try:
            result = await self.agent_manager.execute_shape_search(
                shape_vector=shape_vector,
                top_k=top_k
            )
            
            if result.status == 'success':
                return result.results
            else:
                print(f"搜索失败: {result.error_message}")
                return []
                
        except Exception as e:
            print(f"搜索过程出错: {e}")
            return []
    
    def create_result_image(self, query_image_path: str, search_results: list, 
                          output_path: str, query_folder_name: str):
        """
        创建结果展示图像
        
        Args:
            query_image_path: 查询图像路径
            search_results: 搜索结果列表
            output_path: 输出图像路径
            query_folder_name: 查询文件夹名称
        """
        try:
            # 创建子图布局 (2行3列)
            fig, axes = plt.subplots(2, 3, figsize=(15, 10))
            fig.suptitle(f'Shape Search Results for {query_folder_name}', fontsize=16)
            
            # 显示查询图像
            query_img = mpimg.imread(query_image_path)
            axes[0, 0].imshow(query_img)
            axes[0, 0].set_title('Query Image', fontsize=12, fontweight='bold')
            axes[0, 0].axis('off')
            
            # 显示搜索结果
            for i, result in enumerate(search_results[:5]):
                row = (i + 1) // 3
                col = (i + 1) % 3
                
                # 构建结果图像路径
                result_image_path = self.dataset_path / result.uuid / "assembly.png"
                
                if result_image_path.exists():
                    result_img = mpimg.imread(str(result_image_path))
                    axes[row, col].imshow(result_img)
                    
                    # 设置标题，包含相似度分数
                    title = f'#{i+1}: {result.uuid[:8]}...\nScore: {result.similarity_score:.3f}'
                    axes[row, col].set_title(title, fontsize=10)
                else:
                    # 如果图像不存在，显示占位符
                    axes[row, col].text(0.5, 0.5, 'Image\nNot Found', 
                                       ha='center', va='center', transform=axes[row, col].transAxes)
                    axes[row, col].set_title(f'#{i+1}: {result.uuid[:8]}...', fontsize=10)
                
                axes[row, col].axis('off')
            
            # 如果结果不足5个，隐藏多余的子图
            total_plots = min(len(search_results), 5) + 1  # +1 for query image
            for i in range(total_plots, 6):
                row = i // 3
                col = i % 3
                axes[row, col].axis('off')
            
            # 保存图像
            plt.tight_layout()
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"结果图像已保存: {output_path}")
            
        except Exception as e:
            print(f"创建结果图像失败: {e}")
    
    async def test_single_folder(self, folder_path: Path):
        """
        测试单个文件夹
        
        Args:
            folder_path: 文件夹路径
        """
        folder_name = folder_path.name
        assembly_image_path = folder_path / "assembly.png"
        
        if not assembly_image_path.exists():
            print(f"跳过 {folder_name}: assembly.png 不存在")
            return
        
        print(f"\n处理文件夹: {folder_name}")
        
        # 提取形状特征
        print("  - 提取形状特征...")
        shape_vector = self.extract_shape_features(str(assembly_image_path))
        if shape_vector is None:
            print(f"  - 跳过 {folder_name}: 特征提取失败")
            return
        
        # 执行形状搜索
        print("  - 执行形状搜索...")
        search_results = await self.search_by_shape(shape_vector.tolist(), top_k=5)
        
        if not search_results:
            print(f"  - {folder_name}: 没有搜索结果")
            return
        
        print(f"  - 找到 {len(search_results)} 个结果")
        
        # 创建结果图像
        output_path = self.results_folder_path / f"shape_search_{folder_name}.png"
        self.create_result_image(
            str(assembly_image_path),
            search_results,
            str(output_path),
            folder_name
        )
        
        # 打印搜索结果摘要
        print(f"  - 搜索结果:")
        for i, result in enumerate(search_results):
            print(f"    {i+1}. {result.uuid} (相似度: {result.similarity_score:.3f})")
    
    async def run_test(self):
        """运行完整测试"""
        print("开始测试 search_cad_by_shape...")
        
        # 初始化智能体
        await self.initialize_agents()
        
        # 获取所有测试文件夹
        test_folders = [f for f in self.test_folder_path.iterdir() 
                       if f.is_dir()]
        
        if not test_folders:
            print(f"在 {self.test_folder_path} 中没有找到子文件夹")
            return
        
        print(f"找到 {len(test_folders)} 个测试文件夹")
        
        # 处理每个文件夹
        successful_tests = 0
        for folder in sorted(test_folders):
            try:
                await self.test_single_folder(folder)
                successful_tests += 1
            except Exception as e:
                print(f"处理文件夹 {folder.name} 时出错: {e}")
        
        print(f"\n测试完成！")
        print(f"成功处理: {successful_tests}/{len(test_folders)} 个文件夹")
        print(f"结果保存在: {self.results_folder_path}")


async def main():
    """主函数"""
    # 检查命令行参数
    if len(sys.argv) != 2:
        print("用法: python test_shape_search.py <test_folder_path>")
        print("示例: python test_shape_search.py test")
        sys.exit(1)
    
    test_folder_path = sys.argv[1]
    
    # 检查test文件夹是否存在
    if not os.path.exists(test_folder_path):
        print(f"错误: 文件夹 '{test_folder_path}' 不存在")
        sys.exit(1)
    
    # 结果保存文件夹
    results_folder_path = "test_results"
    
    # 创建测试器并运行
    tester = ShapeSearchTester(test_folder_path, results_folder_path)
    await tester.run_test()


if __name__ == "__main__":
    # 运行异步主函数
    asyncio.run(main())
