#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
基于文本描述从Milvus检索装配体脚本

此脚本允许用户输入文本描述，从Milvus中检索相似的装配体
"""

import os
import logging
import argparse
from tabulate import tabulate
import textwrap

import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入项目模块
from src.utils.database.milvus_utils import MilvusManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def format_search_results(results, max_description_length=100, max_id_length=20):
    """
    格式化搜索结果为表格
    
    参数:
        results: 搜索结果（字典列表格式）
        max_description_length: 描述最大长度
        max_id_length: ID最大长度
        
    返回:
        格式化后的表格字符串
    """
    rows = []
    for i, result in enumerate(results):
        # 截断ID
        assembly_id = result['id']
        if len(assembly_id) > max_id_length:
            assembly_id = assembly_id[:max_id_length] + "..."
        
        # 截断和格式化描述
        desc = result['description']
        if len(desc) > max_description_length:
            desc = desc[:max_description_length] + "..."
        desc = textwrap.fill(desc, width=60)
        
        # 添加行
        rows.append([i+1, assembly_id, desc, f"{result['score']:.4f}"])
    
    # 创建表格
    table = tabulate(
        rows,
        headers=["序号", "装配体ID", "描述", "相似度分数"],
        tablefmt="grid"
    )
    
    return table


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="基于文本描述从Milvus检索装配体")
    parser.add_argument("--collection_name", default="assembly_descriptions", help="Milvus集合名称")
    parser.add_argument("--top_k", type=int, default=5, help="返回结果数量")
    parser.add_argument("--use_reranker", action="store_true", default=True, help="是否使用重排序器")
    parser.add_argument("--device", choices=["cpu", "cuda"], default="cpu", help="使用的设备，CPU或CUDA")
    parser.add_argument("--query", help="ABS塑料制造的手柄")
    args = parser.parse_args()
    
    try:
        # 创建Milvus管理器
        milvus_manager = MilvusManager(use_reranker=args.use_reranker, device=args.device)
        
        # 如果提供了查询文本，执行单次查询
        if args.query:
            logger.info(f"执行查询: '{args.query}'")
            results = milvus_manager.search_assemblies_by_text(
                args.collection_name,
                args.query,
                top_k=args.top_k
            )
            
            # 显示搜索结果
            table = format_search_results(results)
            print("\n搜索结果:")
            print(table)
            
        # 否则进入交互模式
        else:
            print("====== 装配体文本搜索 ======")
            print("输入文本描述，系统将返回最相似的装配体")
            print("输入'exit'或'quit'退出")
            print("="*30)
            
            while True:
                # 获取用户输入
                query = input("\n请输入查询文本: ")
                
                # 检查是否退出
                if query.lower() in ['exit', 'quit', 'q']:
                    print("感谢使用，再见！")
                    break
                
                # 执行查询
                if query.strip():
                    try:
                        results = milvus_manager.search_assemblies_by_text(
                            args.collection_name,
                            query,
                            top_k=args.top_k
                        )
                        
                        # 显示搜索结果
                        table = format_search_results(results)
                        print("\n搜索结果:")
                        print(table)
                    except Exception as e:
                        logger.error(f"查询时出错: {e}")
                        print(f"查询时出错: {e}")
                else:
                    print("查询文本不能为空，请重新输入")
        
        # 释放集合资源
        milvus_manager.release_collection(args.collection_name)
    
    except Exception as e:
        logger.error(f"执行过程中出错: {e}", exc_info=True)


if __name__ == "__main__":
    main() 