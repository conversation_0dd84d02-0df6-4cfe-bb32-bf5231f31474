#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
重新生成结构查询脚本

使用修改后的结构查询prompt重新生成所有装配体的结构查询，
覆盖各个装配体的single_modal_query.json中的结构查询
"""

import sys
import os
import json
import logging
import time
from pathlib import Path
from typing import Dict, List, Any, Optional
from tqdm import tqdm

# 添加项目根目录到系统路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.append(project_root)

from src.models.LLM import MultiProviderLLM

# 导入本地模块
try:
    from utils.assembly_info_extractor import AssemblyInfoExtractor
    from utils.data_sampler import DataSampler
    from prompts.structure_query_prompt import get_structure_query_prompt, get_structure_task_params
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    utils_dir = os.path.join(current_dir, 'utils')
    prompts_dir = os.path.join(current_dir, 'prompts')
    sys.path.insert(0, utils_dir)
    sys.path.insert(0, prompts_dir)
    from assembly_info_extractor import AssemblyInfoExtractor
    from data_sampler import DataSampler
    from structure_query_prompt import get_structure_query_prompt, get_structure_task_params

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/regenerate_structure_queries.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class StructureQueryRegenerator:
    """结构查询重新生成器"""
    
    def __init__(self):
        """初始化重新生成器"""
        self.llm = self._initialize_llm()
        self.assembly_info_extractor = AssemblyInfoExtractor()
        self.data_sampler = DataSampler()
        self.benchmark_dir = Path(project_root) / "data" / "benchmark"
        self.stats = {
            'total_assemblies': 0,
            'successfully_regenerated': 0,
            'failed_assemblies': [],
            'start_time': time.time()
        }
    
    def _initialize_llm(self) -> MultiProviderLLM:
        """初始化LLM"""
        try:
            llm = MultiProviderLLM()
            logger.info("LLM initialized successfully")
            return llm
        except Exception as e:
            logger.error(f"Failed to initialize LLM: {e}")
            raise
    
    def regenerate_all_structure_queries(self):
        """重新生成所有装配体的结构查询"""
        logger.info("Starting structure queries regeneration...")
        
        if not self.benchmark_dir.exists():
            logger.error(f"Benchmark directory not found: {self.benchmark_dir}")
            return
        
        # 获取所有装配体目录
        assembly_dirs = [d for d in self.benchmark_dir.iterdir() if d.is_dir()]
        
        if not assembly_dirs:
            logger.error("No assembly directories found in benchmark folder")
            return
        
        logger.info(f"Found {len(assembly_dirs)} assembly directories")
        
        # 为每个装配体重新生成结构查询
        for assembly_dir in tqdm(assembly_dirs, desc="Regenerating structure queries"):
            try:
                self._regenerate_assembly_structure_queries(assembly_dir)
                self.stats['successfully_regenerated'] += 1
            except Exception as e:
                logger.error(f"Failed to regenerate structure queries for {assembly_dir.name}: {e}")
                self.stats['failed_assemblies'].append(assembly_dir.name)
            
            self.stats['total_assemblies'] += 1
        
        # 生成统计报告
        self._generate_statistics()
        
        logger.info("Structure queries regeneration completed!")
    
    def _regenerate_assembly_structure_queries(self, assembly_dir: Path):
        """为单个装配体重新生成结构查询"""
        assembly_uuid = assembly_dir.name
        single_modal_query_file = assembly_dir / "single_modal_query.json"
        
        if not single_modal_query_file.exists():
            logger.warning(f"single_modal_query.json not found for assembly {assembly_uuid}")
            return
        
        # 读取现有查询文件
        with open(single_modal_query_file, 'r', encoding='utf-8') as f:
            queries = json.load(f)
        
        # 获取装配体信息
        assembly_basic_info = self.data_sampler.get_assembly_by_uuid(assembly_uuid)
        if not assembly_basic_info:
            logger.error(f"Failed to get basic assembly info for {assembly_uuid}")
            return
        
        assembly_info = self.assembly_info_extractor.extract_full_info(assembly_basic_info)
        if not assembly_info:
            logger.error(f"Failed to extract full assembly info for {assembly_uuid}")
            return
        
        # 生成新的结构查询
        new_structure_queries = self._generate_structure_queries(assembly_info)
        
        if not new_structure_queries:
            logger.error(f"Failed to generate structure queries for {assembly_uuid}")
            return
        
        # 替换原有的结构查询
        updated_queries = []
        structure_query_index = 0
        
        for query in queries:
            if query['query_type'] == 'Structure':
                if structure_query_index < len(new_structure_queries):
                    # 使用新生成的结构查询，但保持原有的query_id
                    new_query = new_structure_queries[structure_query_index].copy()
                    new_query['query_id'] = query['query_id']  # 保持原有ID
                    updated_queries.append(new_query)
                    structure_query_index += 1
                    logger.info(f"Replaced structure query {query['query_id']} for assembly {assembly_uuid}")
                else:
                    # 如果新查询数量不足，保留原查询
                    updated_queries.append(query)
                    logger.warning(f"Not enough new structure queries, keeping original {query['query_id']}")
            else:
                # 保留非结构查询
                updated_queries.append(query)
        
        # 保存更新后的查询文件
        with open(single_modal_query_file, 'w', encoding='utf-8') as f:
            json.dump(updated_queries, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Successfully regenerated structure queries for assembly {assembly_uuid}")
    
    def _generate_structure_queries(self, assembly_info: Dict[str, Any], count: int = 3) -> List[Dict[str, Any]]:
        """
        生成结构查询
        
        Args:
            assembly_info: 装配体信息
            count: 生成数量
            
        Returns:
            结构查询列表
        """
        queries = []
        
        for i in range(count):
            try:
                # 生成提示词
                prompt = get_structure_query_prompt(assembly_info, i + 1)
                
                # 调用LLM生成查询
                messages = [{"role": "user", "content": prompt}]
                response = self.llm.chat(messages)
                query_text = response.strip()
                
                # 构建查询对象（使用临时ID，稍后会被替换）
                query = {
                    "query_id": f"Structure_temp_{i+1}",
                    "query_type": "Structure",
                    "natural_language_prompt": query_text,
                    "ground_truth_plan": {
                        "sub_queries": [
                            {
                                "agent": "结构关系查询智能体",
                                "task_params": get_structure_task_params(query_text)
                            }
                        ],
                        "fusion_strategy": "NONE"
                    },
                    "ground_truth_results": [
                        {
                            "uuid": assembly_info['uuid']
                        }
                    ]
                }
                
                queries.append(query)
                logger.debug(f"Generated structure query: {query_text}")
                
            except Exception as e:
                logger.error(f"Failed to generate structure query {i+1}: {e}")
                continue
        
        return queries
    
    def _generate_statistics(self):
        """生成统计报告"""
        end_time = time.time()
        duration = end_time - self.stats['start_time']
        
        logger.info("=== Structure Queries Regeneration Statistics ===")
        logger.info(f"Total assemblies processed: {self.stats['total_assemblies']}")
        logger.info(f"Successfully regenerated: {self.stats['successfully_regenerated']}")
        logger.info(f"Failed assemblies: {len(self.stats['failed_assemblies'])}")
        logger.info(f"Success rate: {self.stats['successfully_regenerated']/self.stats['total_assemblies']*100:.1f}%")
        logger.info(f"Total time: {duration:.2f} seconds")
        
        if self.stats['failed_assemblies']:
            logger.info(f"Failed assembly UUIDs: {', '.join(self.stats['failed_assemblies'])}")
        
        # 保存统计信息到文件
        stats_file = self.benchmark_dir / "structure_queries_regeneration_stats.json"
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump({
                'regeneration_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                'total_assemblies': self.stats['total_assemblies'],
                'successfully_regenerated': self.stats['successfully_regenerated'],
                'failed_assemblies': self.stats['failed_assemblies'],
                'duration_seconds': duration,
                'success_rate': self.stats['successfully_regenerated']/self.stats['total_assemblies']*100
            }, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Statistics saved to: {stats_file}")


def main():
    """主函数"""
    # 确保日志目录存在
    logs_dir = Path(project_root) / "logs"
    logs_dir.mkdir(exist_ok=True)
    
    try:
        regenerator = StructureQueryRegenerator()
        regenerator.regenerate_all_structure_queries()
    except Exception as e:
        logger.error(f"Script execution failed: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
