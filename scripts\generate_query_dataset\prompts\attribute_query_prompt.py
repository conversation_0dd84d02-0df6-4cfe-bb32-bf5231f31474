#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
属性查询提示词模板

用于生成基于物理属性的查询
"""

def get_attribute_query_prompt(assembly_info: dict, query_index: int) -> str:
    """
    Get attribute query generation prompt
    
    Args:
        assembly_info: Assembly information
        query_index: Query index (1-3)
        
    Returns:
        Prompt string
    """
    
    # Extract key attribute information
    name = assembly_info.get('name', 'Unknown Assembly')
    mass = assembly_info.get('mass', 0)
    volume = assembly_info.get('volume', 0)
    length = assembly_info.get('length', 0)
    width = assembly_info.get('width', 0)
    height = assembly_info.get('height', 0)
    part_count = assembly_info.get('part_count', 0)
    joints_count = assembly_info.get('joints_count', 0)
    
    # Select different attribute combinations based on query index
    if query_index == 1:
        # Single physical attribute range query (maximum 1 condition)
        prompt_type = "single physical attribute range query"
        example_attributes = ["mass", "volume", "length", "width", "height"]
        max_conditions = 1
    elif query_index == 2:
        # Two physical attribute combination query (maximum 2 conditions)
        prompt_type = "two physical attribute combination query"
        example_attributes = ["mass+volume", "length+width", "height+density"]
        max_conditions = 2
    else:
        # Three attribute query (maximum 3 conditions: physical + material + structural)
        prompt_type = "three attribute query"
        example_attributes = ["mass+material+part_count", "volume+density+dimensions", "size+weight+complexity"]
        max_conditions = 3
    
    prompt = f"""
You are a professional CAD assembly data query expert. Based on the following assembly information, generate a natural language {prompt_type}.

Assembly Information:
- Name: {name}
- Mass: {mass:.2f} kg
- Volume: {volume:.2f} cm³
- Dimensions: {length:.2f} × {width:.2f} × {height:.2f} cm
- Part Count: {part_count}
- Joint Count: {joints_count}

**CRITICAL NAMING REQUIREMENT:**
When referring to any assembly, sub-assembly, or part names in the query, you MUST use the EXACT names as they appear in the database. Do NOT modify, abbreviate, or paraphrase these names. Use the complete, exact names including any special characters, numbers, or unusual formatting.

Query Requirements:
1. Query Type: {prompt_type}
2. **MAXIMUM CONDITIONS LIMIT**: This query MUST contain at most {max_conditions} condition(s). Do not exceed this limit.
3. The query should use this assembly as a reference standard to generate query conditions that can find this assembly
4. Use appropriate comparison operators (greater than, less than, equal to, range, etc.)
5. The query language should be natural and fluent, conforming to engineers' expression habits
6. Focus on the following attributes: {', '.join(example_attributes)}
7. **MANDATORY**: When mentioning the assembly name or any component names, use the EXACT names from the database
8. **CONSTRAINT**: Ensure the query uses exactly {max_conditions} condition(s) or fewer, never more

Please generate a natural language query that can be used to search for assemblies with similar attribute characteristics in the assembly database.

Output Format:
- Output the natural language query statement directly
- Do not include any explanations or additional information
- The statement should be concise and clear, suitable for direct use in query systems
- **ENSURE**: All names used in the query match EXACTLY with the database names

Example References:
- Single condition: "Find assemblies with mass less than 5kg"
- Two conditions: "Find assemblies with length greater than 200mm and width less than 100mm"
- Three conditions: "Find assemblies with more than 30 parts, mass less than 3kg, and steel material"
- "Find assemblies similar to '{name}' with mass between 2kg and 8kg" (using exact assembly name)

**IMPORTANT**: Your query must not exceed {max_conditions} condition(s). Count each comparison or constraint as one condition.
"""
    
    return prompt.strip()


def get_attribute_task_params(query_text: str) -> dict:
    """
    生成属性查询的task_params
    
    Args:
        query_text: 查询文本
        
    Returns:
        task_params字典
    """
    return {
        "query_text": query_text
    }
