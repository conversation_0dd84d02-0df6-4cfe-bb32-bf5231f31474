[install]
extra-index-url = ["https://download.pytorch.org/whl/cu118"]

[project]
name = "cad-rag"
version = "0.1.0"
description = "将 fusion 360 assembly 中的装配体模型构造为知识图谱，然后进行 RAG 检索和问答"
requires-python = ">=3.10"
dependencies = [
    "numpy>=1.24.0",
    "pandas>=2.0.0",
    "matplotlib>=3.7.0",
    "networkx>=3.1",
    "scipy>=1.10.0",
    "neo4j>=5.8.0",
    "python-dotenv>=1.0.0",
    "langchain>=0.3.25",
    "openai>=1.82.1",
    "tiktoken>=0.9.0",
    "transformers>=4.52.3",
    "sentence-transformers>=4.1.0",
    "litellm>=1.71.2",
    "open-clip-torch>=2.32.0",
    "pymilvus[model]>=2.5.11",
    "umap-learn>=0.5.7",
    "hdbscan>=0.8.40",
    "supabase>=2.15.3",
    "psycopg2>=2.9.10",
    "trimesh>=4.6.12",
    "tabulate>=0.9.0",
    "uvicorn>=0.34.3",
    "fastapi>=0.115.13",
    "aiofiles>=24.1.0",
    "python-multipart>=0.0.20",
    "torch==2.7.0",
    "torch-geometric>=2.6.0",
    "pillow>=11.2.1",
    "pydantic>=2.11.5",
    "fastmcp>=2.8.1",
    "minio>=7.2.15",
    "seaborn>=0.13.2",
    "openpyxl>=3.1.5",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0"
] 
