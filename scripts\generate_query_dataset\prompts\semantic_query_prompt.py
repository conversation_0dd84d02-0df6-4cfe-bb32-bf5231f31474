#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
语义查询提示词模板

用于生成基于几何和语义相似性的查询
"""

def get_semantic_query_prompt(assembly_info: dict, query_index: int) -> str:
    """
    Get semantic query generation prompt
    
    Args:
        assembly_info: Assembly information
        query_index: Query index (1-3)
        
    Returns:
        Prompt string
    """
    
    # Extract basic information
    name = assembly_info.get('name', 'Unknown Assembly')
    description = assembly_info.get('description', '')
    
    # Extract other available information
    industry = assembly_info.get('industry', '')
    category = assembly_info.get('category', '')
    mass = assembly_info.get('mass', 0)
    volume = assembly_info.get('volume', 0)
    part_count = assembly_info.get('part_count', 0)
    
    # Select different semantic query types based on query index
    if query_index == 1:
        # Function-oriented query (maximum 2 conditions)
        prompt_type = "function-oriented query"
        focus_area = "assembly's functional purpose and working principles"
        max_conditions = 2
    elif query_index == 2:
        # Shape and geometric feature query (maximum 2 conditions)
        prompt_type = "shape and geometric feature query"
        focus_area = "assembly's shape characteristics and geometric structure"
        max_conditions = 2
    else:
        # Application scenario query (maximum 3 conditions)
        prompt_type = "application scenario query"
        focus_area = "assembly's application fields and usage scenarios"
        max_conditions = 3
    
    prompt = f"""
You are a professional CAD assembly semantic analysis expert. Based on the following assembly information, generate a natural language {prompt_type}.

Assembly Information:
- Name: {name}
- Description: {description}
- Industry Domain: {industry if industry else 'Unspecified'}
- Category: {category if category else 'Unspecified'}
- Mass: {mass:.2f} kg
- Volume: {volume:.2f} cm³
- Part Count: {part_count}

**CRITICAL NAMING REQUIREMENT:**
When referring to any assembly, sub-assembly, or part names in the query, you MUST use the EXACT names as they appear in the database. Do NOT modify, abbreviate, or paraphrase these names. Use the complete, exact names including any special characters, numbers, or unusual formatting.

Query Requirements:
1. Query Type: {prompt_type}
2. Focus Area: {focus_area}
3. **MAXIMUM CONDITIONS LIMIT**: This query MUST contain at most {max_conditions} condition(s). Do not exceed this limit.
4. The query should be based on the assembly's description information, analyze the semantic features within, and generate query conditions that can find assemblies with similar semantic features
5. Extract and understand semantic information about function, shape, application, etc. from the description
6. Use natural language descriptions that conform to engineers' expression habits
7. **MANDATORY**: When mentioning the assembly name or any component names, use the EXACT names from the database
8. **CONSTRAINT**: Ensure the query uses exactly {max_conditions} condition(s) or fewer, never more

Specific Guidelines:
- Function queries: Analyze the assembly's functional role and working principles from the description (max {max_conditions} conditions)
- Shape queries: Analyze the assembly's shape features and geometric structure from the description (max {max_conditions} conditions)
- Application queries: Analyze the assembly's usage scenarios, application fields, and industry characteristics from the description (max {max_conditions} conditions)
- **CONSTRAINT**: Each functional requirement, shape characteristic, or application constraint counts as one condition. Do not exceed {max_conditions} conditions.

Please carefully analyze the assembly description, extract key semantic information, and generate a natural language query that can be used to search for assemblies with similar semantic features in the assembly database.

Output Format:
- Output the natural language query statement directly
- Do not include any explanations or additional information
- The statement should be concise and clear, suitable for direct use in query systems
- **ENSURE**: All names used in the query match EXACTLY with the database names

Example References:
- Single condition: "I need a rotational mechanism for power transmission"
- Two conditions: "Find assemblies with cylindrical structure for automotive applications"
- Three conditions: "I need a fastening device suitable for the automotive industry with corrosion resistance"
- "Find assemblies similar to '{name}' with rotational functionality" (using exact assembly name)

**IMPORTANT**: Your query must not exceed {max_conditions} condition(s). Count each functional requirement, shape characteristic, or application constraint as one condition.
"""
    
    return prompt.strip()


def get_semantic_task_params(query_text: str) -> dict:
    """
    Generate task_params for semantic queries
    
    Args:
        query_text: Query text
        
    Returns:
        task_params dictionary
    """
    return {
        "query_text": query_text
    }
