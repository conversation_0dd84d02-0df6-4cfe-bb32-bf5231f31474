#!/usr/bin/env python3
"""
CAD-RAG FastAPI 接口
支持文字和图片输入的多模态检索系统
"""

import asyncio
import io
import sys
import os
from typing import Optional, List, Union, Dict, Any
import tempfile
import uuid
import base64

from fastapi import FastAPI, File, UploadFile, Form, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.responses import JSONResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from PIL import Image
import torch

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.models.clip import CLIPFeatureExtractor
from src.agents.multi_agent_coordinator import MultiAgentCoordinator


# Pydantic 模型
class MultiModalQueryRequest(BaseModel):
    """多模态查询请求模型，支持纯文本、纯图像或文本+图像查询"""
    query_text: Optional[str] = None
    image_data: Optional[str] = None  # Base64编码的图像数据
    top_k: Optional[int] = 10
    stream: Optional[bool] = False  # 添加流式处理选项


class ResultItem(BaseModel):
    uuid: str
    name: str
    description: str
    similarity_score: Optional[float] = None
    search_type: Optional[str] = None
    metadata: Optional[dict] = None
    model_url: Optional[str] = None


class QueryResponse(BaseModel):
    status: str
    message: Optional[str] = None
    execution_time: Optional[float] = None
    total_results: Optional[int] = None
    results: Optional[List[ResultItem]] = None
    error_message: Optional[str] = None


class StreamUpdateMessage(BaseModel):
    """用于流式更新的消息模型"""
    event_type: str  # 事件类型: query_start, step_start, step_complete, query_complete, error
    message: str  # 消息内容
    data: Optional[Dict[str, Any]] = None  # 附加数据
    timestamp: Optional[float] = None  # 时间戳


# 初始化FastAPI应用
app = FastAPI(
    title="CAD-RAG API",
    description="CAD装配体检索和问答系统API",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该设置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 挂载静态文件
app.mount("/static", StaticFiles(directory=os.path.dirname(os.path.abspath(__file__))), name="static")

# 全局变量
clip_extractor = None
coordinator = None
active_connections: Dict[str, WebSocket] = {}  # 存储活动的WebSocket连接


@app.on_event("startup")
async def startup_event():
    """应用启动时初始化模型和协调器"""
    global clip_extractor, coordinator
    
    print("🚀 正在初始化 CAD-RAG API...")
    
    try:
        # 初始化CLIP特征提取器
        print("📸 正在加载CLIP模型...")
        clip_extractor = CLIPFeatureExtractor()
        print(f"✅ CLIP模型加载完成，使用设备: {clip_extractor.device}")
        
        # 初始化多智能体协调器
        print("🤖 正在初始化多智能体协调器...")
        coordinator = MultiAgentCoordinator()
        
        # 预加载所有数据库和嵌入模型
        print("📡 正在预加载数据库和嵌入模型...")
        await coordinator.initialize()
        print("✅ 数据库和嵌入模型预加载完成")
        
        print("🎉 CAD-RAG API 初始化完成！")
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        raise e


@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时清理资源"""
    global coordinator
    
    print("🛑 正在关闭 CAD-RAG API...")
    
    if coordinator:
        try:
            await coordinator.disconnect()
            print("✅ 多智能体协调器已断开连接")
        except Exception as e:
            print(f"⚠️ 协调器断开连接时出错: {e}")
    
    print("👋 CAD-RAG API 已关闭")


@app.get("/")
async def root():
    """根路径 - 返回Web界面"""
    web_interface_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "web_interface.html")
    if os.path.exists(web_interface_path):
        return FileResponse(web_interface_path)
    else:
        return {
            "message": "CAD-RAG API",
            "description": "CAD装配体检索和问答系统",
            "version": "1.0.0",
            "endpoints": {
                "query": "/query - 支持文本、图片或文本+图片的多模态查询",
                "health": "/health",
                "ws": "/ws - 支持文本、图片或文本+图片的多模态查询"
            }
        }


@app.get("/api")
async def api_info():
    """API信息"""
    return {
        "message": "CAD-RAG API",
        "description": "CAD装配体检索和问答系统",
        "version": "1.0.0",
        "endpoints": {
            "query": "/query - 支持文本、图片或文本+图片的多模态查询",
            "health": "/health",
            "ws": "/ws - 支持文本、图片或文本+图片的多模态查询"
        }
    }


@app.get("/health")
async def health_check():
    """健康检查"""
    global clip_extractor, coordinator
    
    status = {
        "status": "healthy",
        "clip_model": "loaded" if clip_extractor is not None else "not_loaded",
        "coordinator": "ready" if coordinator is not None else "not_ready"
    }
    
    if clip_extractor:
        status["clip_device"] = str(clip_extractor.device)
    
    return status


@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket连接端点，用于流式推送查询过程"""
    # 生成连接ID
    connection_id = str(uuid.uuid4())
    
    # 接受连接
    await websocket.accept()
    
    # 存储连接
    active_connections[connection_id] = websocket
    
    try:
        # 向客户端发送连接成功消息
        await websocket.send_json({
            "event_type": "connection_established",
            "message": "WebSocket连接已建立",
            "connection_id": connection_id,
            "capabilities": {
                "query": True  # 统一查询能力
            }
        })
        
        # 监听消息
        while True:
            # 接收客户端消息
            data = await websocket.receive_json()
            
            # 处理不同类型的消息
            if data.get("action") == "query":
                # 启动一个新的任务来处理查询
                asyncio.create_task(
                    handle_query_ws(
                        connection_id=connection_id,
                        query_text=data.get("query_text"),
                        image_data=data.get("image_data"),
                        top_k=data.get("top_k", 10)
                    )
                )
            
            elif data.get("action") == "ping":
                # 心跳检测
                await websocket.send_json({
                    "event_type": "pong",
                    "message": "pong"
                })
            
            else:
                # 未知操作
                await websocket.send_json({
                    "event_type": "error",
                    "message": f"未知操作: {data.get('action')}"
                })
    
    except WebSocketDisconnect:
        # 客户端断开连接
        print(f"客户端断开连接: {connection_id}")
    except Exception as e:
        # 发生错误
        print(f"WebSocket错误: {e}")
        try:
            await websocket.send_json({
                "event_type": "error",
                "message": f"服务器错误: {str(e)}"
            })
        except:
            pass
    finally:
        # 清理连接
        if connection_id in active_connections:
            del active_connections[connection_id]


async def handle_query_ws(connection_id: str, query_text: Optional[str] = None, image_data: Optional[str] = None, top_k: int = 10):
    """统一处理WebSocket查询请求，支持纯文本、纯图片或文本+图片查询"""
    global clip_extractor, coordinator
    websocket = active_connections.get(connection_id)
    
    if not websocket:
        return
    
    if not coordinator:
        await websocket.send_json({
            "event_type": "error",
            "message": "协调器未初始化"
        })
        return
    
    # 验证至少有一个输入
    if not query_text and not image_data:
        await websocket.send_json({
            "event_type": "error",
            "message": "请提供文字查询或图片"
        })
        return
    
    try:
        # 确定查询类型并发送查询开始事件
        query_type = "文字"
        if query_text and image_data:
            query_type = "多模态"
            await websocket.send_json({
                "event_type": "query_start",
                "message": f"🔍 收到多模态查询: {query_text}"
            })
        elif query_text:
            await websocket.send_json({
                "event_type": "query_start",
                "message": f"📝 收到文字查询: {query_text}"
            })
        elif image_data:
            query_type = "图片"
            await websocket.send_json({
                "event_type": "query_start",
                "message": f"🖼️ 收到图片查询"
            })
        
        shape_vector = None
        
        # 处理图片数据（如果有）
        if image_data:
            if not clip_extractor:
                await websocket.send_json({
                    "event_type": "error",
                    "message": "CLIP模型未初始化"
                })
                return
                
            try:
                # 从Base64解码图片数据
                # 格式通常为: data:image/jpeg;base64,/9j/4AAQSkZJRg...
                await websocket.send_json({
                    "event_type": "step_start",
                    "message": "🖼️ 开始处理图片数据..."
                })
                
                image_data = image_data.split(',', 1)[1]
                image_bytes = io.BytesIO(base64.b64decode(image_data))
                
                await websocket.send_json({
                    "event_type": "step_info",
                    "message": "图片数据解码成功"
                })
                
                image = Image.open(image_bytes)
                
                # 确保图片是RGB格式
                if image.mode != 'RGB':
                    image = image.convert('RGB')
                    await websocket.send_json({
                        "event_type": "step_info",
                        "message": "图片已转换为RGB格式"
                    })
                
                # 提取形状向量
                await websocket.send_json({
                    "event_type": "step_start",
                    "message": "🔍 正在提取图片特征..."
                })
                
                preprocessed_image = clip_extractor.preprocess(image).unsqueeze(0)
                
                with torch.no_grad():
                    image_features = clip_extractor(preprocessed_image, normalize=True)
                    shape_vector = image_features[0].cpu().numpy().tolist()
                
                await websocket.send_json({
                    "event_type": "step_complete",
                    "message": f"✅ 图片特征提取完成，特征维度: {len(shape_vector)}"
                })
            except Exception as e:
                await websocket.send_json({
                    "event_type": "error",
                    "message": f"❌ 图片处理失败: {str(e)}"
                })
                return
        
        # 如果只有图片没有文字，设置默认文字查询
        if not query_text and shape_vector:
            query_text = "相似的CAD模型"
        
        # 自定义事件处理回调函数，添加额外的详细信息
        async def event_callback(event_data):
            if not websocket or connection_id not in active_connections:
                return
                
            event_type = event_data.get("event_type", "")
            message = event_data.get("message", "")
            data = event_data.get("data", {})
            
            # 添加更多实时反馈和解释
            if event_type == "query_start":
                # 为查询开始添加单独消息
                await websocket.send_json({
                    "event_type": "query_start",
                    "message": "🧠 开始查询规划..."
                })
                
            elif event_type == "plan_created":
                # 查询计划生成后发送详细的子查询描述
                sub_queries = data.get("sub_queries", [])
                
                # 先发送计划概要
                await websocket.send_json({
                    "event_type": "plan_created",
                    "message": message
                })
                
                # 然后为每个子查询发送详细描述
                for i, sub_query in enumerate(sub_queries):
                    agent_name = sub_query.get("agent", "")
                    task_params = sub_query.get("task_params", {})
                    
                    # 翻译agent为中文
                    agent_name_cn = {
                        "AttributeFilteringAgent": "属性筛选智能体",
                        "StructuralTopologyAgent": "结构拓扑智能体",
                        "GeometrySemanticAgent": "几何语义智能体"
                    }.get(agent_name, agent_name)
                    
                    # 构建子查询详情，包含查询文本
                    sub_query_detail = f"子查询{i+1}: {agent_name_cn}"
                    if task_params.get("query_text"):
                        sub_query_detail += f"\n📝 查询文本: {task_params['query_text']}"
                    if task_params.get("top_k"):
                        sub_query_detail += f"\n📊 返回数量: {task_params['top_k']}"
                    if task_params.get("database"):
                        sub_query_detail += f"\n🗄️ 数据库: {task_params['database']}"
                    
                    await websocket.send_json({
                        "event_type": "plan_step_detail",
                        "message": sub_query_detail,
                        "data": {
                            **sub_query,
                            "sub_query_index": i + 1
                        }
                    })
                
            elif event_type == "step_start":
                # 为子查询开始提供更详细的信息
                sub_query_id = data.get("sub_query_id", data.get("step_id", ""))
                agent_name = data.get("agent", data.get("agent_type", ""))
                
                # 翻译agent为中文
                agent_name_cn = {
                    "AttributeFilteringAgent": "属性筛选智能体",
                    "StructuralTopologyAgent": "结构拓扑智能体", 
                    "GeometrySemanticAgent": "几何语义智能体",
                    # 向下兼容旧格式
                    "structured_data": "结构化数据查询",
                    "structural_relationship": "结构关系查询",
                    "geometry_semantic": "几何语义查询"
                }.get(agent_name, agent_name)
                
                await websocket.send_json({
                    "event_type": "step_start",
                    "message": f"🔍 执行 {sub_query_id}: {agent_name_cn}"
                })
                
            elif event_type == "step_info" and ("Text2SQL:" in message or "Text2Cypher:" in message):
                # 处理转换结果事件
                if "Text2SQL:" in message:
                    query_text, sql_query = message.split(" -> ", 1) if " -> " in message else (message, "")
                    query_text = query_text.replace("Text2SQL: ", "").strip()
                    await websocket.send_json({
                        "event_type": "text2sql_result",
                        "message": f"📊 Text2SQL转换完成: {query_text}",
                        "data": {
                            "sql_query": sql_query,
                            "conversion_type": "Text2SQL",
                            "query_text": query_text
                        }
                    })
                elif "Text2Cypher:" in message:
                    query_text, cypher_query = message.split(" -> ", 1) if " -> " in message else (message, "")
                    query_text = query_text.replace("Text2Cypher: ", "").strip()
                    await websocket.send_json({
                        "event_type": "text2cypher_result",
                        "message": f"🔗 Text2Cypher转换完成: {query_text}",
                        "data": {
                            "cypher_query": cypher_query,
                            "conversion_type": "Text2Cypher",
                            "query_text": query_text
                        }
                    })
                
            elif event_type == "step_info" and "SQL查询成功" in message:
                # SQL查询结果信息
                await websocket.send_json({
                    "event_type": "sql_execution_result",
                    "message": message
                })
                
            elif event_type == "step_info" and "Cypher查询成功" in message:
                # Cypher查询结果信息
                await websocket.send_json({
                    "event_type": "cypher_execution_result",
                    "message": message
                })
                
            elif event_type == "step_info" and "search_cad_by_text" in message:
                # 文本搜索完成
                await websocket.send_json({
                    "event_type": "step_info",
                    "message": f"基于语义的相似度检索完成，返回{message.split('返回')[1].split('条')[0]}条记录"
                })
                
            elif event_type == "step_info" and "search_cad_by_shape" in message:
                # 形状搜索完成
                await websocket.send_json({
                    "event_type": "step_info",
                    "message": f"基于几何的相似度检索完成，返回{message.split('返回')[1].split('条')[0]}条记录"
                })
                
            elif event_type == "step_info" and "search_cad_hybrid" in message:
                # 混合搜索完成
                await websocket.send_json({
                    "event_type": "step_info",
                    "message": f"混合相似度检索完成，返回{message.split('返回')[1].split('条')[0]}条记录"
                })
                
            elif event_type == "step_complete":
                # 步骤完成信息
                await websocket.send_json({
                    "event_type": "step_complete",
                    "message": message
                })
                
            elif event_type == "fusion_start":
                # 结果融合开始
                await websocket.send_json({
                    "event_type": "fusion_start",
                    "message": message
                })
                
            elif event_type == "fusion_info":
                # 结果融合信息
                await websocket.send_json({
                    "event_type": "fusion_info",
                    "message": message
                })
                
            elif event_type == "query_complete":
                # 查询完成消息
                result_count = data.get("result_count", 0)
                await websocket.send_json({
                    "event_type": "query_complete",
                    "message": f"✅ 查询完成，返回 {result_count} 个结果"
                })
            else:
                # 将原始事件转发给客户端
                await websocket.send_json(event_data)
        
        # 执行查询
        coordinator.register_event_callback(event_callback)
        try:
            result = await coordinator.execute_multi_modal_query(
                query_text=query_text,
                shape_vector=shape_vector,
                top_k=top_k
            )
        finally:
            coordinator.unregister_event_callback(event_callback)
        
        # 发送查询完成事件
        response_data = {
            "status": result.status,
            "execution_time": result.execution_time,
            "total_results": result.total_results,
            "error_message": result.error_message
        }
        
        if result.results:
            response_data["results"] = [
                {
                    "uuid": item.uuid,
                    "name": item.name,
                    "description": item.description,
                    "similarity_score": item.similarity_score,
                    "model_url": item.model_url
                }
                for item in result.results
            ]
        
        await websocket.send_json({
            "event_type": "query_complete",
            "message": f"✅ {query_type}查询完成，返回 {result.total_results} 个结果",
            "data": response_data
        })
        
    except Exception as e:
        # 发送错误事件
        await websocket.send_json({
            "event_type": "error",
            "message": f"❌ 查询失败: {str(e)}"
        })


@app.post("/query", response_model=QueryResponse)
async def query(
    query_text: Optional[str] = Form(None),
    file: Optional[UploadFile] = File(None),
    top_k: Optional[int] = Form(10),
    stream: Optional[bool] = Form(False)
):
    """统一的多模态查询接口，支持纯文本、纯图片或文本+图片查询"""
    global clip_extractor, coordinator
    
    if not coordinator:
        raise HTTPException(status_code=500, detail="协调器未初始化")
    
    # 验证至少有一个输入
    if not query_text and not file:
        raise HTTPException(status_code=400, detail="请提供文字查询或上传图片")
    
    try:
        print(f"🔍 收到查询请求")
        if query_text:
            print(f"📝 文字查询: {query_text}")
        if file:
            print(f"🖼️ 图片查询: {file.filename}")
        
        # 检查是否需要流式处理
        if stream:
            # 返回一个唯一标识符，前端可以使用此ID通过WebSocket连接获取实时更新
            return {
                "status": "streaming",
                "message": "查询正在处理中，请通过WebSocket连接获取实时更新",
                "streaming_id": str(uuid.uuid4())
            }
        
        shape_vector = None
        
        # 如果有图片，提取形状向量
        if file and file.filename:
            if not clip_extractor:
                raise HTTPException(status_code=500, detail="CLIP模型未初始化")
            
            # 验证文件类型
            if not file.content_type.startswith('image/'):
                raise HTTPException(status_code=400, detail="请上传图片文件")
            
            # 读取和处理图片
            contents = await file.read()
            image = Image.open(io.BytesIO(contents))
            
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # 提取形状向量
            print("🔍 正在提取图片特征...")
            preprocessed_image = clip_extractor.preprocess(image).unsqueeze(0)
            
            with torch.no_grad():
                image_features = clip_extractor(preprocessed_image, normalize=True)
                shape_vector = image_features[0].cpu().numpy().tolist()
            
            print(f"✅ 图片特征提取完成，特征维度: {len(shape_vector)}")
        
        # 如果只有图片没有文字，设置默认文字查询
        if not query_text and shape_vector:
            query_text = "相似的CAD模型"
        
        # 执行多模态查询
        result = await coordinator.execute_multi_modal_query(
            query_text=query_text,
            shape_vector=shape_vector,
            top_k=top_k
        )
        
        # 转换结果格式
        response_data = {
            "status": result.status,
            "execution_time": result.execution_time,
            "total_results": result.total_results,
            "error_message": result.error_message
        }
        
        if result.results:
            response_data["results"] = [
                ResultItem(
                    uuid=item.uuid,
                    name=item.name,
                    description=item.description,
                    similarity_score=item.similarity_score,
                    model_url=item.model_url
                )
                for item in result.results
            ]
        
        # 确定查询类型
        query_type = "文字"
        if shape_vector:
            query_type = "多模态" if query_text and query_text != "相似的CAD模型" else "图片"
            
        print(f"✅ {query_type}查询完成，返回 {result.total_results} 个结果")
        return QueryResponse(**response_data)
        
    except Exception as e:
        print(f"❌ 查询失败: {e}")
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")


if __name__ == "__main__":
    import uvicorn
    
    # 运行FastAPI服务器
    print("🚀 启动 CAD-RAG API 服务器...")
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
