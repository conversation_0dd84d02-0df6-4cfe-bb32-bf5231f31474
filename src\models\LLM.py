import base64
import json
from typing import List, Dict, Any, Optional
from openai import OpenAI
from src.config import Config


class MultiProviderLLM:
    """
    统一封装 OpenAI / Azure OpenAI / Ollama / Gemini / GitHub Models 等多供应商 LLM。
    """

    def __init__(
        self,
        provider: Optional[str] = None,
        api_url: Optional[str] = None,
        api_key: Optional[str] = None,
        model: Optional[str] = None,
        **kwargs,
    ):
        """
        Parameters
        ----------
        provider : 供应商名称（大小写均可，如 'openai'、'ollama'）
                  如果为 None，使用 Config.PROVIDER
        api_url  : 基础 URL（到组织资源 / 本地端口为止，末尾不带斜杠）
                  如果为 None，使用 Config.API_URL
        api_key  : API 密钥或 Token
                  如果为 None，使用 Config.API_KEY
        model    : 模型名称
                  如果为 None，使用 Config.DEFAULT_LLM_MODEL
        kwargs   : 供应商专属可选参数，例如
                   - deployment (Azure OpenAI)
                   - api_version (Azure OpenAI，默认 2024-02-15-preview)
        """
        # 如果参数为 None，使用 Config 中的默认配置
        self.provider = (provider or Config.PROVIDER).lower()
        self.base_url = (api_url or Config.API_URL).rstrip("/")
        self.api_key = api_key or Config.API_KEY
        # 设置默认模型
        if model:
            self.opts = {"model": model, **kwargs}
        else:
            # 根据供应商设置默认模型
            default_model = Config.DEFAULT_LLM_MODEL
            self.opts = {"model": default_model, **kwargs}

        # 初始化 OpenAI 客户端
        self.client = OpenAI(
                api_key=self.api_key,
                base_url=self.base_url
            )

    # ------------------------------------------------------------------ #
    # 对外方法
    # ------------------------------------------------------------------ #
    def chat(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """纯文本对话（messages 与 OpenAI 格式兼容）。"""
        model = kwargs.get("model", self.opts.get("model"))
        timeout = kwargs.get("timeout", 60)
        
        # 过滤掉 OpenAI API 不支持的参数
        filtered_kwargs = {k: v for k, v in kwargs.items() if k not in ["timeout", "model"]}
        
        response = self.client.chat.completions.create(
            model=model,
            messages=messages,
            timeout=timeout,
            **filtered_kwargs
        )
        
        return response.choices[0].message.content

    def chat_with_images(
        self,
        messages: List[Dict[str, str]],
        image_paths: List[str],
        **kwargs,
    ) -> str:
        """
        多模态对话（目前主要支持 Gemini）。
        `image_paths` 里放本地图片路径；函数会自动转 base64。
        """
        model = kwargs.get("model", self.opts.get("model"))
        timeout = kwargs.get("timeout", 90)
        
        # 过滤掉 OpenAI API 不支持的参数
        filtered_kwargs = {k: v for k, v in kwargs.items() if k not in ["timeout", "model"]}
        
        # 构建多模态消息
        multimodal_messages = self._build_multimodal_messages(messages, image_paths)
        
        response = self.client.chat.completions.create(
            model=model,
            messages=multimodal_messages,
            timeout=timeout,
            **filtered_kwargs
        )
        
        return response.choices[0].message.content

    # ------------------------------------------------------------------ #
    # 内部辅助
    # ------------------------------------------------------------------ #
    def _build_openai_client(self) -> OpenAI:
        """构建 OpenAI 客户端。"""
        if self.provider == "openai":
            return OpenAI(
                api_key=self.api_key,
                base_url=f"{self.base_url}/v1"
            )
        elif self.provider == "ollama":
            return OpenAI(
                api_key="ollama",  # Ollama 不需要真实的 API key
                base_url=f"{self.base_url}/v1"
            )
        elif self.provider == "gemini":
            return OpenAI(
                api_key=self.api_key,
                base_url="https://generativelanguage.googleapis.com/v1beta/openai/"
            )
        else:
            # 默认：直接用传入的 base_url
            return OpenAI(
                api_key=self.api_key,
                base_url=self.base_url
            )

    def _build_multimodal_messages(
        self, 
        messages: List[Dict[str, str]], 
        image_paths: List[str]
    ) -> List[Dict[str, Any]]:
        """构建多模态消息格式。"""
        # 复制原始消息
        multimodal_messages = messages.copy()
        
        # 如果有图片，在最后添加一个包含图片的用户消息
        if image_paths:
            content = []
            
            # 如果最后一条消息是用户消息，将其文本内容加入
            if multimodal_messages and multimodal_messages[-1].get("role") == "user":
                last_message = multimodal_messages.pop()
                content.append({
                    "type": "text",
                    "text": last_message["content"]
                })
            
            # 添加图片
            for image_path in image_paths:
                base64_image = self._img2b64(image_path)
                content.append({
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpeg;base64,{base64_image}"
                    }
                })
            
            # 添加多模态消息
            multimodal_messages.append({
                "role": "user",
                "content": content
            })
        
        return multimodal_messages

    @staticmethod
    def _img2b64(path: str) -> str:
        with open(path, "rb") as f:
            return base64.b64encode(f.read()).decode("ascii")
