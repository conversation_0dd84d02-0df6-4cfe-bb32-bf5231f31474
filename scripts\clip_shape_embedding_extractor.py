"""
# 零件形状特征提取器
# ---------------------------------------
# 功能说明:
#   该脚本用于提取Fusion360测试集中所有零件图像的CLIP视觉特征，并将结果保存为pickle文件。
#   它会遍历所有测试集装配体文件夹，处理每个文件夹中的零件PNG图像，提取其CLIP特征向量。
#
# 使用方法:
#   python scripts/part_shape_embedding_extractor.py
#
# 输入数据:
#   需要Fusion360数据集的测试集，每个装配体文件夹中应包含多个组件的PNG图像
#
# 输出文件:
#   默认输出路径: dataset/clip_features.pkl
#   装配体特征输出路径: dataset/assembly_clip_features.pkl
#
# 输出文件结构:
#   零件特征文件结构:
#   {
#       "装配体名称1": {
#           "组件名称1": numpy.ndarray特征向量,
#           "组件名称2": numpy.ndarray特征向量,
#           ...
#       },
#       "装配体名称2": {
#           ...
#       },
#       ...
#   }
#
#   装配体特征文件结构:
#   {
#       "装配体名称1": numpy.ndarray特征向量,
#       "装配体名称2": numpy.ndarray特征向量,
#       ...
#   }
#
# 参数说明:
#   - output_path: 特征文件保存路径
#   - model_name: CLIP模型名称
#   - pretrained: CLIP预训练模型名称
#   - device: 计算设备(cuda/cpu)
#
"""
import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import torch
import numpy as np
import pickle
from PIL import Image
from tqdm import tqdm
from pathlib import Path

from src.utils.file_utils import get_fusion360_test_paths
from src.models.clip import CLIPFeatureExtractor

def extract_and_save_component_features(
    output_path="dataset/clip_features.pkl", 
    model_name="ViT-L-14", 
    pretrained="laion2b_s32b_b82k",
    device=None
):
    """
    提取Fusion360测试集中所有组件图片的CLIP特征并保存到本地
    
    参数:
        output_path (str): 输出文件路径
        model_name (str): CLIP模型名称
        pretrained (str): CLIP预训练模型名称
        device (str, optional): 计算设备 ('cuda', 'cuda:0', 'cpu' 等)，如果为None则自动选择
    """
    # 创建CLIP特征提取器
    clip_feature_extractor = CLIPFeatureExtractor(model_name=model_name, pretrained=pretrained, device=device)
    print(f"使用设备: {clip_feature_extractor.device}")
    
    # 获取测试集文件夹路径
    test_paths = get_fusion360_test_paths()
    print(f"找到 {len(test_paths)} 个测试集装配体文件夹")
    
    # 创建结果字典
    results = {}
    
    # 对每个装配体文件夹处理
    for assembly_path in tqdm(test_paths, desc="处理装配体"):
        # 获取装配体文件夹名称
        assembly_name = os.path.basename(assembly_path)
        results[assembly_name] = {}
        
        # 查找所有PNG图片
        png_files = [f for f in os.listdir(assembly_path) if f.lower().endswith('.png') and f != "assembly.png"]
        
        if not png_files:
            print(f"警告: 在 {assembly_path} 中没有找到组件图片")
            continue
        
        # 处理每个组件图片
        for png_file in tqdm(png_files, desc=f"处理 {assembly_name} 中的组件", leave=False):
            image_path = os.path.join(assembly_path, png_file)
            
            try:
                # 加载和处理图片
                image = clip_feature_extractor.preprocess(Image.open(image_path)).unsqueeze(0)
                
                # 提取特征向量 (不需要手动将图像移至设备，forward方法会处理)
                feature = clip_feature_extractor(image)
                
                # 将特征向量转换为numpy数组并保存到结果字典中
                feature_np = feature.cpu().numpy().squeeze(0)  # 移除第一个维度，从1x768变为768
                
                # 使用组件文件名作为键
                component_name = os.path.splitext(png_file)[0]
                results[assembly_name][component_name] = feature_np
                
            except Exception as e:
                print(f"处理图片 {image_path} 时出错: {e}")
    
    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # 保存结果到文件
    with open(output_path, 'wb') as f:
        pickle.dump(results, f)
    
    print(f"特征向量已保存到 {output_path}")
    
    # 输出一些统计信息
    total_assemblies = len(results)
    total_components = sum(len(components) for components in results.values())
    print(f"共处理了 {total_assemblies} 个装配体, {total_components} 个组件")
    
    return results

def extract_and_save_assembly_features(
    output_path="dataset/assembly_clip_features.pkl", 
    model_name="ViT-L-14", 
    pretrained="laion2b_s32b_b82k",
    device=None
):
    """
    提取Fusion360测试集中所有装配体图片的CLIP特征并保存到本地
    
    参数:
        output_path (str): 输出文件路径
        model_name (str): CLIP模型名称
        pretrained (str): CLIP预训练模型名称
        device (str, optional): 计算设备 ('cuda', 'cuda:0', 'cpu' 等)，如果为None则自动选择
    """
    # 创建CLIP特征提取器
    clip_feature_extractor = CLIPFeatureExtractor(model_name=model_name, pretrained=pretrained, device=device)
    print(f"使用设备: {clip_feature_extractor.device}")
    
    # 获取测试集文件夹路径
    test_paths = get_fusion360_test_paths()
    print(f"找到 {len(test_paths)} 个测试集装配体文件夹")
    
    # 创建结果字典
    results = {}
    
    # 对每个装配体文件夹处理
    for assembly_path in tqdm(test_paths, desc="处理装配体"):
        # 获取装配体文件夹名称
        assembly_name = os.path.basename(assembly_path)
        
        # 构建装配体图片路径
        assembly_image_path = os.path.join(assembly_path, "assembly.png")
        
        if not os.path.exists(assembly_image_path):
            print(f"警告: 在 {assembly_path} 中没有找到装配体图片")
            continue
        
        try:
            # 加载和处理图片
            image = clip_feature_extractor.preprocess(Image.open(assembly_image_path)).unsqueeze(0)
            
            # 提取特征向量
            feature = clip_feature_extractor(image, normalize=True)
            
            # 将特征向量转换为numpy数组并保存到结果字典中
            feature_np = feature.cpu().numpy().squeeze(0)  # 移除第一个维度，从1x768变为768
            
            # 使用装配体名称作为键
            results[assembly_name] = feature_np
            
        except Exception as e:
            print(f"处理装配体图片 {assembly_image_path} 时出错: {e}")
    
    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # 保存结果到文件
    with open(output_path, 'wb') as f:
        pickle.dump(results, f)
    
    print(f"装配体特征向量已保存到 {output_path}")
    
    # 输出一些统计信息
    total_assemblies = len(results)
    print(f"共处理了 {total_assemblies} 个装配体")
    
    return results

if __name__ == "__main__":
    # 检测是否有CUDA可用
    cuda_available = torch.cuda.is_available()
    if cuda_available:
        device_count = torch.cuda.device_count()
        device_name = torch.cuda.get_device_name(0) if device_count > 0 else "未知"
        print(f"CUDA可用，检测到{device_count}个GPU设备")
        print(f"设备名称: {device_name}")
        device = "cuda"
    else:
        print("未检测到CUDA，将使用CPU计算")
        device = "cpu"
        
    # # 提取零件特征
    # extract_and_save_component_features(device=device)
    
    # 提取装配体特征
    # extract_and_save_assembly_features(device=device)