import argparse
import torch
import os
import sys
# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from src.models.clip import CLIPFeatureExtractor
from PIL import Image
import numpy as np
from src.knowledge_graph import CADKnowledgeGraph
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class ImageSimilaritySearch:
    """
    基于图像相似度的CAD模型检索类
    
    使用CLIP模型提取图像特征，通过余弦相似度计算与数据库中节点的相似度
    """
    
    def __init__(self, clip_extractor):
        """
        初始化搜索类
        
        Args:
            clip_model_name: CLIP模型名称
        """
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        logging.info(f"使用设备: {self.device}")
        
        # 加载CLIP模型
        self.clip_extractor = clip_extractor
        
        # 初始化知识图谱连接
        self.kg = CADKnowledgeGraph.from_config()
        
    def extract_image_embedding(self, image_path):
        """
        提取图像的CLIP嵌入向量
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            numpy.ndarray: 图像的CLIP嵌入向量
        """
        try:
            # 加载并预处理图像
            image = Image.open(image_path).convert("RGB")
            image_input = self.preprocess(image).unsqueeze(0).to(self.device)
            
            # 提取特征
            with torch.no_grad():
                image_features = self.model.encode_image(image_input)
                
            # 归一化并转换为numpy数组
            image_features = image_features / image_features.norm(dim=-1, keepdim=True)
            return image_features.cpu().numpy()[0]
            
        except Exception as e:
            logging.error(f"提取图像特征失败: {e}")
            raise
            
    def search_similar_nodes(self, image_embedding, top_k=5):
        """
        在知识图谱中搜索与图像最相似的节点
        
        Args:
            image_embedding: 图像嵌入向量
            top_k: 返回的最相似结果数量
            
        Returns:
            list: 最相似节点列表，包含节点信息和相似度分数
        """
        # Neo4j Cypher查询，计算余弦相似度并返回top_k个结果
        query = """
        MATCH (n)
        WHERE n.shape_embedding IS NOT NULL
        WITH n, gds.similarity.cosine($image_embedding, n.shape_embedding) AS similarity
        WHERE similarity > 0.5
        RETURN n.id AS id, labels(n)[0] AS type, n.name AS name, similarity
        ORDER BY similarity DESC
        LIMIT $top_k
        """
        
        params = {
            "image_embedding": image_embedding.tolist(),
            "top_k": top_k
        }
        
        # 执行查询
        with self.kg.driver.session() as session:
            result = session.run(query, params)
            similar_nodes = [dict(record) for record in result]
            
        return similar_nodes
    
    def close(self):
        """释放资源"""
        if self.kg:
            self.kg.close()
        
def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="基于图像相似度的CAD模型检索")
    parser.add_argument("--image", "-i", type=str, default=r"test\41032_ed481084\assembly.png", help="输入图像路径")
    parser.add_argument("--top_k", "-k", type=int, default=5, help="返回的最相似结果数量")
    args = parser.parse_args()
    
    # 检查图像文件是否存在
    if not os.path.exists(args.image):
        logging.error(f"图像文件不存在: {args.image}")
        return
    
    try:
        # 创建CLIP特征提取器实例，只初始化一次
        clip_extractor = CLIPFeatureExtractor()

        # 初始化搜索器
        searcher = ImageSimilaritySearch(clip_extractor)
        
        # 提取图像特征
        logging.info(f"正在提取图像特征: {args.image}")
        image_embedding = searcher.extract_image_embedding(args.image)
        
        # 搜索相似节点
        logging.info("正在搜索相似CAD模型...")
        similar_nodes = searcher.search_similar_nodes(image_embedding, args.top_k)
        
        # 打印结果
        if similar_nodes:
            logging.info(f"找到 {len(similar_nodes)} 个相似模型:")
            for i, node in enumerate(similar_nodes):
                print(f"{i+1}. ID: {node['id']}, 类型: {node['type']}, 名称: {node['name']}, 相似度: {node['similarity']:.4f}")
        else:
            logging.info("未找到相似模型")
            
    except Exception as e:
        logging.error(f"搜索失败: {e}")
        
    finally:
        searcher.close()
        
if __name__ == "__main__":
    main()