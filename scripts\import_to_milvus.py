#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CAD数据导入到Milvus脚本

此脚本将零件和装配体的文本嵌入和形状嵌入导入到Milvus数据库中。
主要功能：
1. 从文本嵌入文件加载文本描述及其嵌入向量
2. 从形状嵌入文件加载形状特征向量，并进行L2归一化
3. 将零件和装配体数据统一存储到Milvus数据库中，通过type字段区分

注意：形状向量在加载时会自动进行L2归一化处理

使用方法:
python scripts/import_to_milvus.py --text_embeddings dataset/text_embeddings.pkl --shape_embeddings dataset/clip_features.pkl,dataset/assembly_clip_features.pkl --collection cad_collection_en

参数说明:
--text_embeddings: 文本嵌入向量文件路径
--shape_embeddings: 形状嵌入向量文件路径，多个文件用逗号分隔
--collection: Milvus集合名称
--recreate: 是否重新创建集合
--batch_size: 批处理大小
--device: 计算设备(cuda/cpu)
"""

import os
import sys
import logging
import pickle
import argparse
from tqdm import tqdm
import numpy as np
from pathlib import Path

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.database.milvus_utils import MilvusManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def load_text_embeddings(file_path):
    """
    加载文本嵌入向量
    
    参数:
        file_path: 文本嵌入向量文件路径
        
    返回:
        文本嵌入字典 {id: {type, description, dense, sparse}}
    """
    logger.info(f"正在加载文本嵌入向量: {file_path}")
    
    try:
        with open(file_path, 'rb') as f:
            embeddings = pickle.load(f)
        
        logger.info(f"成功加载文本嵌入向量，包含 {len(embeddings)} 个项目")
        
        # 统计装配体和零件的数量
        assembly_count = sum(1 for item in embeddings.values() if item["type"] == "assembly")
        part_count = sum(1 for item in embeddings.values() if item["type"] == "part")
        logger.info(f"其中装配体 {assembly_count} 个，零件 {part_count} 个")
        
        return embeddings
    except Exception as e:
        logger.error(f"加载文本嵌入向量失败: {e}")
        raise


def load_shape_embeddings(file_paths):
    """
    加载形状嵌入向量，支持多个文件
    
    参数:
        file_paths: 形状嵌入向量文件路径，多个文件用逗号分隔
        
    返回:
        形状嵌入字典 {id: feature_vector}
    """
    shape_embeddings = {}
    
    for file_path in file_paths.split(','):
        file_path = file_path.strip()
        if not file_path:
            continue
            
        logger.info(f"正在加载形状嵌入向量: {file_path}")
        
        try:
            with open(file_path, 'rb') as f:
                features = pickle.load(f)
            
            # 根据文件名判断是零件特征还是装配体特征
            file_name = os.path.basename(file_path)
              # 处理不同格式的形状嵌入文件
            if "assembly" in file_name:
                # 装配体特征格式: {assembly_id: feature_vector}
                for assembly_id, feature_vector in features.items():
                    # 对形状向量进行L2归一化
                    feature_vector = np.asarray(feature_vector, dtype=np.float32)
                    norm = np.linalg.norm(feature_vector)
                    if norm > 0:
                        feature_vector = feature_vector / norm
                    else:
                        logger.warning(f"装配体 {assembly_id} 的形状向量范数为0，跳过归一化")
                    shape_embeddings[assembly_id] = feature_vector
                logger.info(f"已加载并归一化 {len(features)} 个装配体形状向量")
            else:
                # 零件特征格式: {assembly_id: {component_id: feature_vector}}
                for assembly_id, components in features.items():
                    for component_id, feature_vector in components.items():
                        # 对形状向量进行L2归一化
                        feature_vector = np.asarray(feature_vector, dtype=np.float32)
                        norm = np.linalg.norm(feature_vector)
                        if norm > 0:
                            feature_vector = feature_vector / norm
                        else:
                            logger.warning(f"零件 {component_id} 的形状向量范数为0，跳过归一化")
                        shape_embeddings[component_id] = feature_vector
                logger.info(f"已加载并归一化零件形状向量")
            
        except Exception as e:
            logger.error(f"加载形状嵌入向量文件 {file_path} 失败: {e}")
    
    logger.info(f"共加载 {len(shape_embeddings)} 个形状向量")
    return shape_embeddings


def import_to_milvus(text_embeddings, shape_embeddings, collection_name, recreate=False, batch_size=100, device="cpu", use_text_vectors=False):
    """
    将文本嵌入和形状嵌入导入到Milvus
    
    参数:
        text_embeddings: 文本嵌入字典
        shape_embeddings: 形状嵌入字典
        collection_name: Milvus集合名称
        recreate: 是否重新创建集合
        batch_size: 批处理大小
        device: 计算设备
        use_text_vectors: 是否使用文本向量，如果为False，则dense_vectors和sparse_vectors为None
        
    返回:
        导入的数据数量
    """
    logger.info(f"开始导入数据到Milvus集合 {collection_name}")
    
    # 初始化Milvus管理器
    milvus_manager = MilvusManager(device=device)
    
    # 创建集合
    collection = milvus_manager.create_cad_collection(
        collection_name=collection_name,
        recreate=recreate
    )
    
    # 准备要导入的数据
    ids = []
    types = []
    descriptions = []
    dense_vectors = [] if use_text_vectors else None
    sparse_vectors = [] if use_text_vectors else None
    shape_vectors = []
    
    # 导入进度条
    pbar = tqdm(text_embeddings.items(), desc="准备数据")
    
    # 导入计数
    imported_count = 0
    
    # 处理每个文本嵌入
    for item_id, item_data in pbar:
        try:
            # 获取类型和描述
            item_type = item_data["type"]
            description = item_data["description"]
            
            # 如果使用文本向量，则处理文本嵌入
            if use_text_vectors:
                # 获取文本嵌入并确保类型正确
                dense_vector = np.asarray(item_data["dense"], dtype=np.float32)
                sparse_vector = item_data["sparse"]
                
                # 添加到批次
                dense_vectors.append(dense_vector)
                sparse_vectors.append(sparse_vector)
            
            # 获取形状嵌入（如果存在）
            shape_vector = None
            if item_id in shape_embeddings:
                shape_vector = shape_embeddings[item_id]
            else:
                # 对于零件，检查是否存在以下模式的ID
                if item_type == "part":
                    # 尝试查找组件ID（装配体ID_组件ID）
                    for potential_id in shape_embeddings.keys():
                        if potential_id.endswith(f"_{item_id}") or potential_id.startswith(f"{item_id}_"):
                            shape_vector = shape_embeddings[potential_id]
                            break
              # 如果没有找到形状向量，使用零向量
            if shape_vector is None:
                shape_vector = np.zeros(768, dtype=np.float32)
            else:
                # 确保形状向量也是float32类型
                shape_vector = np.asarray(shape_vector, dtype=np.float32)
                # 检查形状向量的维度，确保是一维的768维向量
                if shape_vector.ndim > 1:
                    # 如果是二维的(1,768)，转换为一维(768,)
                    shape_vector = shape_vector.flatten()                # 确保长度为768
                if shape_vector.shape[0] != 768:
                    logger.warning(f"项目 {item_id} 的形状向量维度不是768，而是 {shape_vector.shape}，将使用零向量替代")
                    shape_vector = np.zeros(768, dtype=np.float32)
                else:
                    # 对形状向量进行L2归一化（如果尚未归一化）
                    norm = np.linalg.norm(shape_vector)
                    if norm > 1.01 or norm < 0.99:  # 检查是否已经归一化（允许小的浮点误差）
                        if norm > 0:
                            shape_vector = shape_vector / norm
                        else:
                            # 如果向量的范数为0，使用零向量
                            logger.warning(f"项目 {item_id} 的形状向量范数为0，使用零向量替代")
                            shape_vector = np.zeros(768, dtype=np.float32)
            
            # 添加到批次
            ids.append(item_id)
            types.append(item_type)
            descriptions.append(description)
            shape_vectors.append(shape_vector)
            
            # 当批次达到指定大小时，执行批量导入
            if len(ids) >= batch_size:
                count = milvus_manager.insert_cad_data(
                    collection_name=collection_name,
                    ids=ids,
                    descriptions=descriptions,
                    cad_type=types,
                    dense_vectors=dense_vectors,
                    sparse_vectors=sparse_vectors,
                    shape_vectors=shape_vectors,
                    batch_size=batch_size
                )
                
                imported_count += count
                
                # 清空批次
                ids = []
                types = []
                descriptions = []
                if use_text_vectors:
                    dense_vectors = []
                    sparse_vectors = []
                shape_vectors = []
                
                pbar.set_postfix({"已导入": imported_count})
        
        except Exception as e:
            logger.error(f"处理项目 {item_id} 时出错: {e}")
    
    # 处理剩余的数据
    if ids:
        count = milvus_manager.insert_cad_data(
            collection_name=collection_name,
            ids=ids,
            descriptions=descriptions,
            cad_type=types,
            dense_vectors=dense_vectors,
            sparse_vectors=sparse_vectors,
            shape_vectors=shape_vectors,
            batch_size=batch_size
        )
        
        imported_count += count
    
    logger.info(f"导入完成，共导入 {imported_count} 个项目到Milvus")
    
    # 释放集合资源
    milvus_manager.release_collection(collection_name)
    
    return imported_count


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="将CAD数据导入到Milvus数据库")
    parser.add_argument("--text_embeddings", type=str, default="data/embeddings/text_embeddings_en.pkl", help="文本嵌入向量文件路径")
    parser.add_argument("--shape_embeddings", type=str, default="data/features/parts/clip_features.pkl,data/features/assemblies/assembly_clip_features.pkl", help="形状嵌入向量文件路径，多个文件用逗号分隔")
    parser.add_argument("--collection", type=str, default="cad_collection_en", help="Milvus集合名称")
    parser.add_argument("--recreate", default=True, help="如果集合已存在，是否重新创建")
    parser.add_argument("--batch_size", type=int, default=100, help="批处理大小")
    parser.add_argument("--device", choices=["cpu", "cuda"], default="cuda", help="计算设备")
    parser.add_argument("--use_text_vectors", default=False, help="是否使用文本向量")
    
    args = parser.parse_args()
    
    try:
        # 加载文本嵌入向量
        text_embeddings = load_text_embeddings(args.text_embeddings)
        
        # 加载形状嵌入向量
        shape_embeddings = load_shape_embeddings(args.shape_embeddings)
        
        # 导入数据到Milvus
        import_to_milvus(
            text_embeddings=text_embeddings,
            shape_embeddings=shape_embeddings,
            collection_name=args.collection,
            recreate=args.recreate,
            batch_size=args.batch_size,
            device=args.device,
            use_text_vectors=args.use_text_vectors
        )
        
        logger.info("导入过程成功完成")
        return 0
        
    except Exception as e:
        logger.error(f"导入过程中发生错误: {e}", exc_info=True)
        return 1


if __name__ == "__main__":
    sys.exit(main())
