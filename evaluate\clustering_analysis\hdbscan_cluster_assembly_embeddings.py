#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
使用 HDBSCAN 对装配体特征进行无监督聚类，并给出常见内部评估指标。

支持分析不同类型的装配体特征：
- structural: 结构化特征
- shape: 形状特征  
- semantic: 语义特征
- fused_features: 融合特征
- all: 依次分析所有特征类型

用法示例：

# 分析形状特征，euclidean 距离 + L2 归一化
python evaluate_scripts/clustering_analysis/hdbscan_cluster_assembly_embeddings.py \
    --embed dataset/assembly_features.pkl \
    --feature-type shape

# 分析融合特征，指定 min_cluster_size、关闭归一化、自定义权重
python evaluate_scripts/clustering_analysis/hdbscan_cluster_assembly_embeddings.py \
    --embed dataset/assembly_features.pkl \
    --feature-type fused_features \
    --min_cluster_size 20 --norm 0 \
    --shape-weight 0.8 --semantic-weight 0.2

# 分析结构化特征，使用 PCA 降维到 32 维
python evaluate_scripts/clustering_analysis/hdbscan_cluster_assembly_embeddings.py \
    --embed dataset/assembly_features.pkl \
    --feature-type structural \
    --pca 1 --pca-components 32 --min_cluster_size 20

# 自定义输出文件名
python evaluate_scripts/clustering_analysis/hdbscan_cluster_assembly_embeddings.py \
    --embed dataset/assembly_features.pkl \
    --feature-type shape \
    --out dataset/my_custom_assembly_labels.pkl

# 分析所有特征类型
python evaluate_scripts/clustering_analysis/hdbscan_cluster_assembly_embeddings.py \
    --embed dataset/assembly_features.pkl \
    --feature-type all \
    --min_cluster_size 5

注意：
- 当选择 --feature-type all 时，会依次分析所有特征类型，并将结果保存为 Markdown 表格到 dataset/assembly_cluster_result.md
- 表格包含特征类型、特征维度、簇数、噪声比例和各种评估指标

指标：
• silhouette_score 通常范围在 [-1, 1] 之间，越接近 1 表示类内相似度越高、类间区分度越好。
• Davies-Bouldin 越小越好。
• Calinski-Harabasz 越大越好。
"""
from __future__ import annotations

import argparse
import pickle
from collections import Counter
from pathlib import Path
from typing import Tuple

import numpy as np
from sklearn.preprocessing import normalize
from sklearn import metrics
from sklearn.decomposition import PCA
import psycopg2
from psycopg2.extras import RealDictCursor
import sys
import os

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from src.config import Config

# 用于存储所有特征类型的结果
ASSEMBLY_CLUSTER_RESULTS = []

try:
    import hdbscan  # type: ignore
except ImportError as e:
    raise ImportError(
        "未安装 hdbscan。请先运行 `pip install hdbscan` 或 `conda install -c conda-forge hdbscan`"
    ) from e


def load_assembly_features(pkl_path: Path, feature_type: str, shape_weight: float = 0.7, semantic_weight: float = 0.3) -> Tuple[np.ndarray, list[str]]:
    """
    从 assembly_features.pkl 文件中读取指定类型的装配体特征
    
    Args:
        pkl_path: assembly_features.pkl 文件路径
        feature_type: 特征类型，可选 'structural', 'shape', 'semantic', 'fused_features'
        shape_weight: 形状向量权重（默认0.7）
        semantic_weight: 语义向量权重（默认0.3）
        
    Returns:
        features: 特征矩阵 (N, D)
        assembly_ids: 装配体ID列表
    """
    with pkl_path.open("rb") as f:
        data: dict = pickle.load(f)
    
    # 检查特征类型是否存在
    if feature_type == 'fused_features' and feature_type not in data:
        # 如果没有融合特征，则使用权重拼接形状和语义特征
        print("未找到融合特征，使用权重拼接形状和语义特征...")
        features = create_fused_assembly_features(data, shape_weight, semantic_weight)
    elif feature_type not in data:
        available_types = [k for k in data.keys() if isinstance(data[k], np.ndarray)]
        raise KeyError(f"特征类型 '{feature_type}' 不存在。可用类型: {available_types}")
    else:
        features = data[feature_type]
    
    assembly_ids = data.get('assembly_ids', [])
    
    # 验证特征和ID数量匹配
    if len(assembly_ids) != features.shape[0]:
        print(f"警告: 装配体ID数量 ({len(assembly_ids)}) 与特征数量 ({features.shape[0]}) 不匹配")
        # 如果没有assembly_ids，创建默认ID
        if not assembly_ids:
            assembly_ids = [f"assembly_{i}" for i in range(features.shape[0])]
        else:
            # 截断或扩展assembly_ids以匹配特征数量
            if len(assembly_ids) > features.shape[0]:
                assembly_ids = assembly_ids[:features.shape[0]]
            else:
                assembly_ids.extend([f"assembly_{i}" for i in range(len(assembly_ids), features.shape[0])])
    
    return features, assembly_ids


def create_fused_assembly_features(data: dict, shape_weight: float, semantic_weight: float) -> np.ndarray:
    """
    使用权重拼接形状和语义特征创建装配体融合特征
    
    Args:
        data: 包含各种特征的字典
        shape_weight: 形状特征权重
        semantic_weight: 语义特征权重
        
    Returns:
        融合特征矩阵
    """
    features_list = []
    feature_names = []
    
    # 检查并添加形状特征
    if 'shape' in data and isinstance(data['shape'], np.ndarray):
        shape_features = data['shape'] * shape_weight
        features_list.append(shape_features)
        feature_names.append(f"shape(w={shape_weight})")
    
    # 检查并添加语义特征
    if 'semantic' in data and isinstance(data['semantic'], np.ndarray):
        semantic_features = data['semantic'] * semantic_weight
        features_list.append(semantic_features)
        feature_names.append(f"semantic(w={semantic_weight})")
    
    if not features_list:
        raise ValueError("没有找到可用的形状或语义特征来创建装配体融合特征")
    
    # 拼接特征
    fused_features = np.concatenate(features_list, axis=1)
    print(f"装配体融合特征创建完成，包含: {', '.join(feature_names)}")
    print(f"装配体融合特征维度: {fused_features.shape}")
    
    return fused_features


def apply_pca(X: np.ndarray, n_components: int = 64) -> Tuple[np.ndarray, PCA]:
    """应用 PCA 降维"""
    # 确保降维后的维度不超过原始维度
    n_components = min(n_components, X.shape[1], X.shape[0])
    pca = PCA(n_components=n_components, random_state=42)
    X_reduced = pca.fit_transform(X)
    return X_reduced, pca


def run_hdbscan(
    X: np.ndarray,
    min_cluster_size: int = 5,
    min_samples: int | None = None,
    metric: str = "cosine",
    **kwargs,
) -> hdbscan.HDBSCAN:
    clusterer = hdbscan.HDBSCAN(
        min_cluster_size=min_cluster_size,
        min_samples=min_samples,
        metric=metric,
        cluster_selection_method="eom",
        **kwargs,
    )
    clusterer.fit(X)
    return clusterer


def eval_internal_metrics(X: np.ndarray, labels: np.ndarray) -> dict[str, float | str]:
    """计算 silhouette、DBI、CHI；若簇数不足 2，则返回 N/A"""
    # 过滤掉噪声标签 -1
    mask = labels != -1
    if mask.sum() < 2 or len(set(labels[mask])) < 2:
        return {"silhouette": "N/A", "dbi": "N/A", "chi": "N/A"}

    try:
        sil = metrics.silhouette_score(X[mask], labels[mask], metric="cosine")
    except Exception:
        sil = "err"
    try:
        dbi = metrics.davies_bouldin_score(X[mask], labels[mask])
    except Exception:
        dbi = "err"
    try:
        chi = metrics.calinski_harabasz_score(X[mask], labels[mask])
    except Exception:
        chi = "err"
    return {"silhouette": sil, "dbi": dbi, "chi": chi}


def get_assembly_additional_info_from_db(assembly_ids):
    """从PostgreSQL数据库中获取装配体的额外信息"""
    try:
        # 连接数据库
        conn = psycopg2.connect(**Config.POSTGRES_CONFIG)
        
        with conn.cursor(cursor_factory=RealDictCursor) as cursor:
            # 查询装配体的基本信息
            placeholders = ','.join(['%s'] * len(assembly_ids))
            query = f"""
                SELECT uuid, name, description, length, width, height, 
                       volume, area, part_count, mass, density
                FROM assemblies 
                WHERE uuid IN ({placeholders})
            """
            cursor.execute(query, assembly_ids)
            results = cursor.fetchall()
            
            # 构建字典映射
            assembly_info = {row['uuid']: row for row in results}
            
            print(f"从数据库获取了 {len(assembly_info)} 个装配体的详细信息")
            return assembly_info
            
    except Exception as e:
        print(f"从数据库获取装配体信息失败: {e}")
        return {}
    finally:
        if 'conn' in locals():
            conn.close()


def save_assembly_results_to_markdown(results, output_file="dataset/assembly_cluster_result.md"):
    """将装配体聚类结果保存为Markdown表格格式"""
    if not results:
        print("没有结果可保存")
        return
    
    # 确保输出目录存在
    output_path = Path(output_file)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    # 创建Markdown表格
    markdown_content = "# 装配体HDBSCAN聚类结果\n\n"
    markdown_content += "| 特征类型 | 特征维度 | 簇数 | 噪声比例 (%) | silhouette | DBI | CHI |\n"
    markdown_content += "| --- | --- | --- | --- | --- | --- | --- |\n"
    
    for result in results:
        feature_type = result['feature_type']
        feature_dim = result['feature_dim']
        n_clusters = result['n_clusters']
        noise_ratio = result['noise_ratio']
        silhouette = result['silhouette']
        dbi = result['dbi']
        chi = result['chi']
        
        # 格式化数值
        if isinstance(silhouette, (int, float)):
            silhouette_str = f"{silhouette:.6f}"
        else:
            silhouette_str = str(silhouette)
        
        if isinstance(dbi, (int, float)):
            dbi_str = f"{dbi:.4f}"
        else:
            dbi_str = str(dbi)
        
        if isinstance(chi, (int, float)):
            chi_str = f"{chi:.2f}"
        else:
            chi_str = str(chi)
        
        markdown_content += f"| {feature_type} | {feature_dim} | {n_clusters} | {noise_ratio:.2f} | {silhouette_str} | {dbi_str} | {chi_str} |\n"
    
    # 保存到文件
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(markdown_content)
    
    print(f"装配体聚类结果已保存到: {output_path}")


def main():
    parser = argparse.ArgumentParser(description="使用 HDBSCAN 对装配体特征进行聚类")
    parser.add_argument("--embed", default="data/features/assemblies/assembly_features.pkl", help="装配体特征 pickle 文件路径")
    parser.add_argument("--feature-type", default="all", 
                       choices=['structural', 'shape', 'semantic', 'fused_features', 'all'],
                       help="要分析的特征类型，选择all时会依次分析所有特征类型")
    parser.add_argument("--out", default=None, help="输出标签 pickle 文件，如果不指定则根据特征类型自动命名")
    parser.add_argument("--min_cluster_size", type=int, default=3, help="HDBSCAN 的 min_cluster_size (装配体数量较少，默认5)")
    parser.add_argument("--min_samples", type=int, default=None, help="HDBSCAN 的 min_samples")
    parser.add_argument("--metric", default="euclidean", help="距离度量，默认 euclidean")
    parser.add_argument("--norm", type=int, choices=[0, 1], default=1, help="是否做 L2 归一化 (1/0)")
    parser.add_argument("--pca", type=int, choices=[0, 1], default=1, help="是否使用 PCA 降维 (1/0)")
    parser.add_argument("--pca-components", type=int, default=64, help="PCA 降维后的维度")
    
    # 融合特征权重参数
    parser.add_argument("--shape-weight", type=float, default=0.7, help="形状向量权重（默认0.7）")
    parser.add_argument("--semantic-weight", type=float, default=0.3, help="语义向量权重（默认0.3）")
    
    args = parser.parse_args()

    embed_path = Path(args.embed)
    if not embed_path.exists():
        raise FileNotFoundError(f"未找到装配体特征文件: {embed_path}")

    # 如果选择了all，则依次分析所有特征类型
    if args.feature_type == 'all':
        feature_types = ['structural', 'shape', 'semantic', 'fused_features']
        print(f"选择了'all'特征类型，将依次分析: {', '.join(feature_types)}")
        
        # 清空之前的结果
        global ASSEMBLY_CLUSTER_RESULTS
        ASSEMBLY_CLUSTER_RESULTS = []
        
        for feature_type in feature_types:
            print(f"\n{'='*50}")
            print(f"开始分析装配体特征类型: {feature_type}")
            print(f"{'='*50}")
            
            # 递归调用，但用单个特征类型
            process_single_assembly_feature_type(args, embed_path, feature_type)
        
        # 保存所有结果到Markdown表格
        save_assembly_results_to_markdown(ASSEMBLY_CLUSTER_RESULTS)
    else:
        # 处理单个特征类型
        process_single_assembly_feature_type(args, embed_path, args.feature_type)


def process_single_assembly_feature_type(args, embed_path, feature_type):
    """处理单个装配体特征类型的聚类分析"""

    # 载入装配体特征
    X, assembly_ids = load_assembly_features(
        embed_path, 
        feature_type, 
        args.shape_weight, 
        args.semantic_weight
    )
    
    # 从数据库获取装配体额外信息
    print("从数据库获取装配体详细信息...")
    assembly_info = get_assembly_additional_info_from_db(assembly_ids)
    print(f"载入 {X.shape[0]} 个 {feature_type} 装配体特征，维度 {X.shape[1]}")

    # 可选归一化
    if args.norm:
        X = normalize(X)
        print("已做 L2 归一化")

    # 可选 PCA 降维
    pca_model = None
    if args.pca:
        original_dim = X.shape[1]
        X, pca_model = apply_pca(X, n_components=args.pca_components)
        print(f"已使用 PCA 从 {original_dim} 维降到 {X.shape[1]} 维，保留方差比例: {pca_model.explained_variance_ratio_.sum():.4f}")

    # 聚类
    print("开始装配体 HDBSCAN 聚类...")
    clusterer = run_hdbscan(
        X,
        min_cluster_size=args.min_cluster_size,
        min_samples=args.min_samples,
        metric=args.metric,
    )
    labels = clusterer.labels_
    n_clusters = len(set(labels)) - (1 if -1 in labels else 0)
    noise_ratio = (labels == -1).mean()
    print(f"装配体聚类完成！簇数: {n_clusters} | 噪声比例: {noise_ratio:.2%}")

    # 评估指标
    metrics_dict = eval_internal_metrics(X, labels)
    print("内部指标：")
    for k, v in metrics_dict.items():
        print(f"  {k}: {v}")

    # 簇大小分布
    cnt = Counter(labels)
    if -1 in cnt:
        cnt.pop(-1)
    print("前 10 大簇 (簇ID:数量):", cnt.most_common(10))

    # 如果是all模式，收集结果数据
    if args.feature_type == 'all':
        result_data = {
            'feature_type': feature_type,
            'feature_dim': X.shape[1],
            'n_clusters': n_clusters,
            'noise_ratio': noise_ratio * 100,  # 转换为百分比
            'silhouette': metrics_dict['silhouette'],
            'dbi': metrics_dict['dbi'],
            'chi': metrics_dict['chi']
        }
        ASSEMBLY_CLUSTER_RESULTS.append(result_data)

    # 确定输出文件名
    if args.out is None:
        out_file = f"dataset/hdbscan_assembly_labels_{feature_type}.pkl"
    else:
        # 如果用户指定了输出文件且是all模式，需要为每个特征类型创建不同的文件名
        if args.feature_type == 'all':
            out_path = Path(args.out)
            out_file = str(out_path.parent / f"{out_path.stem}_assembly_{feature_type}{out_path.suffix}")
        else:
            out_file = args.out
    
    # 保存结果
    out_path = Path(out_file)
    with out_path.open("wb") as f:
        pickle.dump({
            "labels": labels,
            "assembly_ids": assembly_ids,
            "assembly_info": assembly_info,
            "feature_type": feature_type,
            "params": {
                "min_cluster_size": args.min_cluster_size,
                "min_samples": args.min_samples,
                "metric": args.metric,
                "norm": bool(args.norm),
                "pca": bool(args.pca),
                "pca_components": args.pca_components,
                "feature_type": feature_type,
                "shape_weight": args.shape_weight,
                "semantic_weight": args.semantic_weight,
            },
            "cluster_persistence": getattr(clusterer, "cluster_persistence_", None),
            "pca_model": pca_model,
        }, f)
    print(f"已将装配体聚类标签保存到 {out_path}")


if __name__ == "__main__":
    main()
