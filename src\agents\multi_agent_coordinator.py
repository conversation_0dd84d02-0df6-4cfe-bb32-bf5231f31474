#!/usr/bin/env python3
"""
多智能体协调器
负责协调查询规划智能体和各个专业查询智能体，实现完整的多模态查询流程
"""

import asyncio
import time
import uuid
from typing import List, Dict, Any, Optional

from .query_planner_agent import QueryPlannerAgent
from .attribute_filtering_agent import AttributeFilteringAgent
from .structural_topology_agent import StructuralTopologyAgent
from .geometry_semantic_agent import GeometrySemanticAgent
from .data_models import (
    BaseTask, QueryResult, SearchResultItem,
    QueryPlannerTask, QueryPlan,
    UnifiedStructuredDataTask,
    UnifiedStructuralQueryTask,
    UnifiedGeometrySemanticTask
)


class MultiAgentCoordinator:
    """多智能体协调器"""
    
    def __init__(self):
        """初始化协调器和各个智能体"""
        self.query_planner = QueryPlannerAgent()
        self.attribute_filtering_agent = AttributeFilteringAgent()
        self.structural_topology_agent = StructuralTopologyAgent()
        self.geometry_semantic_agent = GeometrySemanticAgent()
        
        # 智能体映射
        self.agents = {
            "structured_data": self.attribute_filtering_agent,
            "structural_relationship": self.structural_topology_agent,
            "geometry_semantic": self.geometry_semantic_agent
        }
        
        # 标记是否已初始化
        self.initialized = False
    
    async def initialize(self):
        """初始化所有智能体，预先连接数据库并加载模型"""
        if self.initialized:
            return
            
        print("🚀 正在初始化多智能体系统...")
        print("📡 连接数据库并加载模型...")
        
        # 并行初始化所有智能体
        await asyncio.gather(
            self.attribute_filtering_agent.connect(),
            self.structural_topology_agent.connect(),
            self.geometry_semantic_agent.connect()
        )
        
        print("✅ 多智能体系统初始化完成！")
        self.initialized = True
    
    async def execute_multi_modal_query(
        self, 
        query_text: str, 
        shape_vector: Optional[List[float]] = None,
        top_k: int = 10
    ) -> QueryResult:
        """
        执行多模态查询
        
        Args:
            query_text: 用户查询文本
            shape_vector: 形状特征向量（可选）
            top_k: 最终返回结果数量
            
        Returns:
            QueryResult: 最终融合后的查询结果
        """
        # 确保系统已初始化
        if not self.initialized:
            await self.initialize()
            
        start_time = time.time()
        task_id = str(uuid.uuid4())
        
        try:
            # 第一步：生成查询计划
            print("🧠 开始查询规划...")
            
            planner_task = QueryPlannerTask(
                task_id=str(uuid.uuid4()),
                query_text=query_text,
                shape_vector=shape_vector,
                top_k=top_k
            )
            
            planner_result = await self.query_planner.execute_task(planner_task)
            
            if planner_result.status != 'success':
                error_msg = f"查询规划失败: {planner_result.error_message}"
                print(f"❌ {error_msg}")
                return QueryResult(
                    task_id=task_id,
                    status='failure',
                    error_message=error_msg,
                    execution_time=time.time() - start_time
                )
            
            # 解析查询计划
            query_plan = self.query_planner.parse_query_plan_from_result(planner_result)
            if not query_plan:
                error_msg = "无法解析查询计划"
                print(f"❌ {error_msg}")
                return QueryResult(
                    task_id=task_id,
                    status='failure',
                    error_message=error_msg,
                    execution_time=time.time() - start_time
                )
            
            # 输出查询计划详情
            print(f"📋 查询计划生成成功，包含 {len(query_plan.sub_queries)} 个子查询")
            print(f"   查询类型: {query_plan.query_type}")
            print(f"   查询计划详情:")
            for i, sub_query in enumerate(query_plan.sub_queries, 1):
                print(f"      子查询{i}: {sub_query['agent']}")
                print(f"         参数: {sub_query['task_params']}")
            print(f"   融合策略: {query_plan.fusion_strategy}")
            print("\n")
            
            # 第二步：并行执行查询子查询
            parallel_start_time = time.time()
            print(f"🚀 [{time.strftime('%H:%M:%S')}] 开始并行执行 {len(query_plan.sub_queries)} 个子查询...")
            
            # 创建并行执行任务
            async def execute_sub_query(sub_query, index):
                """执行单个子查询的异步函数"""
                agent_name = sub_query["agent"]
                task_params = sub_query["task_params"]
                sub_query_id = f"sub_query_{index+1}"
                
                # 记录开始时间
                start_time = time.time()
                
                try:
                    print(f"🔍 [{time.strftime('%H:%M:%S')}] 开始执行子查询 {sub_query_id} - {agent_name}")
                    
                    # 根据智能体名称选择合适的智能体类型
                    agent_type = self._map_agent_name_to_type(agent_name)
                    
                    # 创建具体的任务对象
                    agent_task = await self._create_agent_task(
                        agent_type, task_params, shape_vector, None
                    )
                    
                    # 执行查询
                    agent = self.agents[agent_type]
                    result = await agent.execute_task(agent_task)
                    
                    # 计算执行时间
                    execution_time = time.time() - start_time
                    
                    print(f"   [{time.strftime('%H:%M:%S')}] 子查询 {sub_query_id} 完成，状态: {result.status}, 结果数: {result.total_results}, 耗时: {execution_time:.2f}s")
                    
                    if result.status != 'success':
                        print(f"   ⚠️ 子查询失败: {result.error_message}")
                    
                    return sub_query_id, result
                    
                except Exception as e:
                    execution_time = time.time() - start_time
                    error_msg = f"子查询 {sub_query_id} 执行异常: {str(e)}"
                    print(f"   ❌ [{time.strftime('%H:%M:%S')}] 子查询 {sub_query_id} 执行异常: {str(e)}, 耗时: {execution_time:.2f}s")
                    
                    # 返回失败结果
                    failed_result = QueryResult(
                        task_id=f"failed_{sub_query_id}",
                        status='failure',
                        error_message=str(e),
                        total_results=0
                    )
                    return sub_query_id, failed_result
            
            # 并行执行所有子查询
            sub_query_tasks = [
                execute_sub_query(sub_query, i) 
                for i, sub_query in enumerate(query_plan.sub_queries)
            ]
            
            print(f"🔄 [{time.strftime('%H:%M:%S')}] 已创建 {len(sub_query_tasks)} 个并行任务，等待执行完成...")
            
            # 等待所有子查询完成
            sub_query_results_list = await asyncio.gather(*sub_query_tasks, return_exceptions=True)
            
            parallel_end_time = time.time()
            parallel_execution_time = parallel_end_time - parallel_start_time
            
            # 处理结果
            sub_query_results = {}
            successful_count = 0
            failed_count = 0
            
            for result_item in sub_query_results_list:
                if isinstance(result_item, Exception):
                    # 处理异常情况
                    print(f"   ❌ 子查询执行出现异常: {str(result_item)}")
                    failed_count += 1
                else:
                    sub_query_id, result = result_item
                    sub_query_results[sub_query_id] = result
                    
                    if result.status == 'success':
                        successful_count += 1
                    else:
                        failed_count += 1
            
            # 输出并行执行完成信息
            parallel_complete_msg = f"📊 [{time.strftime('%H:%M:%S')}] 并行执行完成: 成功 {successful_count} 个，失败 {failed_count} 个，总耗时: {parallel_execution_time:.2f}s"
            print(parallel_complete_msg)
            
            # 第三步：等待所有子查询完成后进行结果融合
            print(f"🔄 所有子查询已完成，开始结果融合...")
            print(f"   融合策略: {query_plan.fusion_strategy}")
            print(f"   成功的子查询: {successful_count}/{len(query_plan.sub_queries)}")
            
            final_results = await self._fuse_results_by_strategy(sub_query_results, query_plan.fusion_strategy, top_k)
            
            execution_time = time.time() - start_time            
            return QueryResult(
                task_id=task_id,
                status='success',
                results=final_results,
                execution_time=execution_time,
                total_results=len(final_results),
                execution_plan=query_plan
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = str(e)
            print(f"❌ 查询失败: {error_msg}")
            return QueryResult(
                task_id=task_id,
                status='failure',
                error_message=error_msg,
                execution_time=execution_time
            )
    
    def _map_agent_name_to_type(self, agent_name: str) -> str:
        """将智能体名称映射到智能体类型"""
        name_mapping = {
            "AttributeFilteringAgent": "structured_data",
            "GeometrySemanticAgent": "geometry_semantic", 
            "StructuralTopologyAgent": "structural_relationship"
        }
        
        if agent_name not in name_mapping:
            raise ValueError(f"未知的智能体名称: {agent_name}")
        
        return name_mapping[agent_name]
    
    async def _create_agent_task(
        self, 
        agent_type: str, 
        task_params: Dict[str, Any], 
        shape_vector: Optional[List[float]],
        id_list: Optional[List[str]]
    ) -> BaseTask:
        """创建特定智能体的任务对象"""
        
        task_id = str(uuid.uuid4())
        
        # 复制参数以避免修改原始参数
        params = task_params.copy()
        
        # 添加id_list到参数中
        if id_list:
            params["id_list"] = id_list
        
        if agent_type == "structured_data":
            return UnifiedStructuredDataTask(
                task_id=task_id,
                **params
            )
        elif agent_type == "structural_relationship":
            return UnifiedStructuralQueryTask(
                task_id=task_id,
                **params
            )
        elif agent_type == "geometry_semantic":
            # 如果有形状向量，添加到参数中
            if shape_vector:
                params["shape_vector"] = shape_vector
                print(f"   添加形状向量到geometry_semantic任务，向量维度: {len(shape_vector)}")
            
            # 移除可能存在的字符串占位符
            if "shape_vector" in params and isinstance(params["shape_vector"], str):
                if shape_vector:
                    params["shape_vector"] = shape_vector
                else:
                    del params["shape_vector"]
                    
            return UnifiedGeometrySemanticTask(
                task_id=task_id,
                **params
            )
        else:
            raise ValueError(f"不支持的智能体类型: {agent_type}")
    
    async def _fuse_results(
        self, 
        step_results: Dict[str, QueryResult], 
        fusion_config: Dict[str, Any],
        top_k: int
    ) -> List[SearchResultItem]:
        """融合多个步骤的查询结果"""
        
        strategy = fusion_config.get("strategy", "union")
        weights = fusion_config.get("weights", {})
        
        # 收集所有成功的结果
        all_results = []
        successful_results = {}
        
        for step_id, result in step_results.items():
            if result.status == 'success' and result.results:
                successful_results[step_id] = result.results
                all_results.extend(result.results)
        
        # 根据融合策略处理结果
        if strategy == "intersection" and len(successful_results) > 1:
            # 交集: 只保留在所有步骤中都出现的结果
            results_lists = list(successful_results.values())
            fused_results = self._intersect_results(results_lists, top_k)
            print(f"使用交集策略融合结果，共{len(fused_results)}项")
            return fused_results
            
        elif strategy == "weighted" and len(successful_results) > 1:
            # 加权融合
            fused_results = await self._weighted_fusion(successful_results, weights, top_k)
            print(f"使用加权策略融合结果，共{len(fused_results)}项")
            return fused_results
            
        else:
            # 默认使用并集
            fused_results = self._union_results(all_results, top_k)
            print(f"使用并集策略融合结果，共{len(fused_results)}项")
            return fused_results
    
    def _intersect_results(self, results_lists: List[List[SearchResultItem]], top_k: int) -> List[SearchResultItem]:
        """计算结果交集"""
        if not results_lists:
            return []
        
        # 以第一个结果列表为基准
        base_results = results_lists[0]
        base_uuids = {item.uuid for item in base_results}
        
        # 找出在所有列表中都出现的UUID
        common_uuids = base_uuids
        for results in results_lists[1:]:
            current_uuids = {item.uuid for item in results}
            common_uuids = common_uuids.intersection(current_uuids)
        
        # 构建交集结果
        intersection_results = []
        for item in base_results:
            if item.uuid in common_uuids:
                intersection_results.append(item)
        
        return intersection_results[:top_k]
    
    def _union_results(self, all_results: List[SearchResultItem], top_k: int) -> List[SearchResultItem]:
        """计算结果并集并去重"""
        seen_uuids = set()
        union_results = []
        
        for item in all_results:
            if item.uuid not in seen_uuids:
                seen_uuids.add(item.uuid)
                union_results.append(item)
        
        # 按相似度分数排序（如果有的话）
        union_results.sort(key=lambda x: x.similarity_score or 0, reverse=True)
        
        return union_results[:top_k]
    
    async def _weighted_fusion(
        self, 
        step_results: Dict[str, List[SearchResultItem]], 
        weights: Dict[str, float], 
        top_k: int
    ) -> List[SearchResultItem]:
        """加权融合多个步骤的查询结果"""
        
        # 如果没有提供权重，使用默认权重
        if not weights:
            weights = {step_id: 1.0 for step_id in step_results.keys()}
        
        # 对于没有在权重中指定的步骤，使用默认权重1.0
        for step_id in step_results.keys():
            if step_id not in weights:
                weights[step_id] = 1.0
        
        # 收集所有结果并计算加权分数
        all_items = {}
        
        for step_id, results in step_results.items():
            weight = weights.get(step_id, 1.0)
            
            for item in results:
                if item.uuid not in all_items:
                    # 第一次遇到该结果，初始化
                    all_items[item.uuid] = {
                        "item": item,
                        "weighted_score": item.similarity_score * weight,
                        "count": 1,
                        "sources": [step_id]
                    }
                else:
                    # 已存在该结果，累加权重分数
                    all_items[item.uuid]["weighted_score"] += item.similarity_score * weight
                    all_items[item.uuid]["count"] += 1
                    all_items[item.uuid]["sources"].append(step_id)
        
        # 计算最终分数并排序
        final_items = []
        for uuid, data in all_items.items():
            item = data["item"]
            # 使用加权平均作为最终分数
            item.similarity_score = data["weighted_score"] / data["count"]
            item.metadata = {
                "sources": data["sources"],
                "weights": [weights.get(s, 1.0) for s in data["sources"]]
            }
            final_items.append(item)
        
        # 按相似度分数降序排序
        final_items.sort(key=lambda x: x.similarity_score, reverse=True)
        
        # 限制返回数量
        return final_items[:top_k]
    
    async def _fuse_results_by_strategy(
        self, 
        sub_query_results: Dict[str, QueryResult], 
        fusion_strategy: str,
        top_k: int
    ) -> List[SearchResultItem]:
        """根据策略融合子查询结果"""
        
        # 收集所有成功的结果
        all_results = []
        successful_results = []
        
        for sub_query_id, result in sub_query_results.items():
            if result.status == 'success' and result.results:
                successful_results.append(result.results)
                all_results.extend(result.results)
        
        if not successful_results:
            return []
        
        # 根据融合策略处理结果
        if fusion_strategy == "INTERSECT" and len(successful_results) > 1:
            # 交集: 只保留在所有子查询中都出现的结果
            fused_results = self._intersect_results(successful_results, top_k)
            print(f"使用交集策略融合结果，共{len(fused_results)}项")
            return fused_results
            
        elif fusion_strategy == "UNION":
            # 并集: 合并所有结果并去重
            fused_results = self._union_results(all_results, top_k)
            print(f"使用并集策略融合结果，共{len(fused_results)}项")
            return fused_results
            
        elif fusion_strategy == "WEIGHTED":
            # 加权融合 - 为简化起见，给每个子查询相等权重
            weights = {f"sub_query_{i+1}": 1.0 for i in range(len(successful_results))}
            result_dict = {f"sub_query_{i+1}": results for i, results in enumerate(successful_results)}
            fused_results = await self._weighted_fusion(result_dict, weights, top_k)
            print(f"使用加权策略融合结果，共{len(fused_results)}项")
            return fused_results
            
        else:
            # 默认使用并集
            fused_results = self._union_results(all_results, top_k)
            print(f"使用默认并集策略融合结果，共{len(fused_results)}项")
            return fused_results
    
    async def disconnect(self):
        """断开所有智能体连接"""
        if self.initialized:
            print("🔌 断开所有智能体连接...")
            await asyncio.gather(
                self.attribute_filtering_agent.disconnect(),
                self.structural_topology_agent.disconnect(),
                self.geometry_semantic_agent.disconnect()
            )
            self.initialized = False
            print("👋 所有连接已断开")
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.initialize()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出"""
        await self.disconnect()
