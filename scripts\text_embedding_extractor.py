"""
# 文本特征提取器
# ---------------------------------------
# 功能说明:
#   该脚本用于提取Fusion360测试集中所有零件和装配体的文本描述，使用BGEM3模型生成文本嵌入向量，
#   并将结果保存为pickle文件。它会遍历所有测试集装配体文件夹，处理每个装配体及其零件的描述。
#   TODO: 文本描述的稀疏向量目前处理有问题，无法正确导入到milvus中
#
# 使用方法:
#   python scripts/text_embedding_extractor.py
#
# 输入数据:
#   需要Fusion360数据集的测试集，每个装配体文件夹中应包含JSON文件
#
# 输出文件:
#   默认输出路径: dataset/text_embeddings.pkl
#   描述缓存路径: dataset/descriptions_cache.pkl
#
# 输出文件结构:
#   {
#       "装配体1的id": {
#           "type": "assembly"
#           "description": "装配体的文本描述",
#           "dense": numpy.ndarray密集特征向量,
#           "sparse": numpy.ndarray特征向量,
#       },
#       "零件1的id": {
#           "type": "part"
#           "description": "零件的文本描述",
#           "dense": numpy.ndarray密集特征向量,
#           "sparse": numpy.ndarray特征向量,
#       },
#       ...
#   }
#
# 参数说明:
#   - output_path: 特征文件保存路径
#   - cache_path: 描述缓存文件保存路径
#   - device: 计算设备(cuda/cpu)
#
"""
import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import torch
import pickle
from tqdm import tqdm
import logging
import numpy as np
import glob
from collections import defaultdict

from pymilvus.model.hybrid import BGEM3EmbeddingFunction
from src.utils.file_utils import get_fusion360_test_paths
from src.extractors.fusion360_extractor import Fusion360Extractor
from src.data_formats import PartData, AssemblyData, SubAssemblyData

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 设置环境变量，使用国内镜像
os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'

def collect_descriptions(test_paths, cache_path="data/cache/descriptions_cache.pkl"):
    """
    收集所有零件和装配体的描述，并缓存到本地文件
    
    参数:
        test_paths (list): 测试集装配体文件夹路径列表
        cache_path (str): 缓存文件路径
        
    返回:
        tuple: (descriptions列表, ids列表, types列表)
    """
    # 检查缓存文件是否存在
    if os.path.exists(cache_path):
        logger.info(f"从缓存文件加载描述数据: {cache_path}")
        try:
            with open(cache_path, 'rb') as f:
                cache_data = pickle.load(f)
                return cache_data['descriptions'], cache_data['ids'], cache_data['types']
        except Exception as e:
            logger.warning(f"加载缓存文件失败: {e}，将重新收集描述")
    
    descriptions = []
    ids = []
    types = []
    
    # 对每个装配体文件夹处理
    for assembly_path in tqdm(test_paths, desc="收集描述"):
        # 查找JSON文件
        json_files = glob.glob(os.path.join(assembly_path, "*.json"))
        json_file = None
        for f in json_files:
            # 排除description.json
            if os.path.basename(f) != "description_en.json":
                json_file = f
                break
        
        if not json_file:
            logger.warning(f"在 {assembly_path} 中没有找到JSON文件")
            continue
        
        try:
            # 使用Fusion360Extractor提取数据
            extractor = Fusion360Extractor(json_file, extract_shape_embedding=False)
            cad_model = extractor.convert()
            assembly = cad_model.assembly
            
            # 收集装配体描述
            if assembly.description:
                descriptions.append(assembly.description)
                ids.append(assembly.assembly_id)
                types.append("assembly")
            
            # 递归收集所有零件的描述
            def collect_part_descriptions(parts):
                for part in parts:
                    if part.description:
                        descriptions.append(part.description)
                        ids.append(part.part_id)
                        types.append("part")
            
            # 递归收集所有子装配体的描述和零件
            def collect_subassembly_descriptions(subassemblies):
                for subassembly in subassemblies:
                    collect_part_descriptions(subassembly.parts)
                    collect_subassembly_descriptions(subassembly.subassemblies)
            
            # 收集顶层零件的描述
            collect_part_descriptions(assembly.parts)
            # 收集子装配体中的零件描述
            collect_subassembly_descriptions(assembly.subassemblies)
            
        except Exception as e:
            logger.error(f"处理装配体 {assembly_path} 时出错: {e}")
    
    # 保存描述数据到缓存文件
    os.makedirs(os.path.dirname(cache_path), exist_ok=True)
    cache_data = {
        'descriptions': descriptions,
        'ids': ids,
        'types': types
    }
    with open(cache_path, 'wb') as f:
        pickle.dump(cache_data, f)
    logger.info(f"描述数据已缓存到: {cache_path}")
    
    return descriptions, ids, types

def extract_and_save_text_embeddings(
    output_path="dataset/text_embeddings_en.pkl", 
    cache_path="dataset/descriptions_cache_en.pkl",
    device=None
):
    """
    提取Fusion360测试集中所有零件和装配体的文本描述，使用BGEM3模型生成嵌入向量并保存到本地
    
    参数:
        output_path (str): 输出文件路径
        cache_path (str): 描述缓存文件路径
        device (str, optional): 计算设备 ('cuda', 'cuda:0', 'cpu' 等)，如果为None则自动选择
    """
    # 检测是否有CUDA可用
    if device is None:
        device = "cuda" if torch.cuda.is_available() else "cpu"
    
    # 如果指定了CUDA但不可用，回退到CPU
    if device.startswith("cuda") and not torch.cuda.is_available():
        logger.warning("未检测到CUDA设备，将使用CPU代替")
        device = "cpu"
    
    logger.info(f"使用设备: {device}")
    
    # 初始化模型路径
    model_path = os.path.expanduser("~/.cache/huggingface/hub/models--BAAI--bge-m3/snapshots/5617a9f61b028005a4858fdac845db406aefb181")
    if not os.path.exists(model_path):
        # 如果本地缓存不存在，使用模型名称让库自动下载
        model_path = "BAAI/bge-m3"
        logger.info(f"本地模型缓存不存在，将从Hugging Face下载模型: {model_path}")
    
    # 创建BGEM3嵌入函数
    try:
        # 根据设备类型确定是否使用半精度
        use_fp16 = device == "cuda"
        embedding_function = BGEM3EmbeddingFunction(
            model_name=model_path, 
            use_fp16=use_fp16, 
            device=device
        )
        logger.info("BGEM3嵌入模型初始化成功")
    except Exception as e:
        logger.error(f"初始化模型时出错: {e}")
        raise
    
    # 获取测试集文件夹路径
    test_paths = get_fusion360_test_paths()
    logger.info(f"找到 {len(test_paths)} 个测试集装配体文件夹")
    
    # 收集描述（可能从缓存加载）
    descriptions, ids, types = collect_descriptions(test_paths, cache_path)
    
    # 创建结果字典
    results = {}
    
    # 如果没有找到任何描述
    if not descriptions:
        logger.error("未找到任何有效的描述")
        return {}
    
    logger.info(f"共收集到 {len(descriptions)} 个描述 (装配体: {types.count('assembly')}, 零件: {types.count('part')})")
    
    # 批处理大小
    batch_size = 8
    
    # 分批处理嵌入生成
    for i in range(0, len(descriptions), batch_size):
        batch_descriptions = descriptions[i:i+batch_size]
        batch_ids = ids[i:i+batch_size]
        batch_types = types[i:i+batch_size]
        
        logger.info(f"处理批次 {i//batch_size + 1}/{(len(descriptions)-1)//batch_size + 1} ({len(batch_descriptions)}个描述)")
        
        try:
            # 获取当前批次的嵌入
            batch_embeddings = embedding_function(batch_descriptions)
            
            # 将结果添加到结果字典
            for j, item_id in enumerate(batch_ids):
                results[item_id] = {
                    "type": batch_types[j],
                    "description": batch_descriptions[j],
                    "dense": batch_embeddings["dense"][j],
                    "sparse": batch_embeddings["sparse"][j]
                }
                
        except Exception as e:
            logger.error(f"生成嵌入向量时出错: {e}")
    
    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # 保存结果到文件
    with open(output_path, 'wb') as f:
        pickle.dump(results, f)
    
    logger.info(f"文本嵌入向量已保存到 {output_path}")
    
    # 输出一些统计信息
    assembly_count = sum(1 for item in results.values() if item["type"] == "assembly")
    part_count = sum(1 for item in results.values() if item["type"] == "part")
    logger.info(f"共处理了 {assembly_count} 个装配体和 {part_count} 个零件的文本描述")
    
    return results

if __name__ == "__main__":
    # 检测是否有CUDA可用
    cuda_available = torch.cuda.is_available()
    if cuda_available:
        device_count = torch.cuda.device_count()
        device_name = torch.cuda.get_device_name(0) if device_count > 0 else "未知"
        print(f"CUDA可用，检测到{device_count}个GPU设备")
        print(f"设备名称: {device_name}")
        device = "cuda"
    else:
        print("未检测到CUDA，将使用CPU计算")
        device = "cpu"
    
    # 提取文本嵌入
    extract_and_save_text_embeddings(device=device) 