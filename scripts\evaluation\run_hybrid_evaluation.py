#!/usr/bin/env python3
"""
运行混合查询评估的简化脚本
"""

import os
import sys
import asyncio
import argparse
from pathlib import Path

# 添加项目根目录到系统路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.append(project_root)

from hybrid_query_evaluator import HybridQueryEvaluator


async def run_evaluation(max_assemblies: int = None, top_k: int = 10, benchmark_dir: str = "data/benchmark"):
    """
    运行混合查询评估
    
    Args:
        max_assemblies: 最大评估装配体数量
        top_k: 每个查询返回的结果数量
        benchmark_dir: 基准测试数据目录
    """
    print(f"开始混合查询评估...")
    print(f"基准测试目录: {benchmark_dir}")
    print(f"最大装配体数量: {max_assemblies if max_assemblies else '全部'}")
    print(f"每查询返回结果数: {top_k}")
    print("-" * 50)
    
    evaluator = HybridQueryEvaluator(benchmark_dir)
    
    try:
        # 初始化评估器
        await evaluator.initialize()
        
        # 执行评估
        summary = await evaluator.evaluate_all_hybrid_queries(
            max_assemblies=max_assemblies,
            top_k=top_k
        )
        
        # 打印总结
        evaluator.print_summary(summary)
        
        # 保存结果
        output_dir = Path("results/evaluation")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        import time
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        
        # 保存详细结果
        detailed_results_file = output_dir / f"hybrid_query_evaluation_{timestamp}.json"
        evaluator.save_detailed_results(str(detailed_results_file))
        
        # 保存总结
        summary_file = output_dir / f"hybrid_query_summary_{timestamp}.json"
        summary_data = {
            'total_queries': summary.total_queries,
            'correct_predictions': summary.correct_predictions,
            'accuracy': summary.accuracy,
            'avg_execution_time': summary.avg_execution_time,
            'planning_accuracy': summary.planning_accuracy,
            'avg_planning_time': summary.avg_planning_time,
            'avg_retrieval_time': summary.avg_retrieval_time,
            'avg_fusion_time': summary.avg_fusion_time,
            'overall_hit_rate_at_1': summary.overall_hit_rate_at_1,
            'overall_hit_rate_at_10': summary.overall_hit_rate_at_10,
            'overall_hit_rate_at_all': summary.overall_hit_rate_at_all,
            'query_type_accuracy': summary.query_type_accuracy,
            'query_type_hit_rates': summary.query_type_hit_rates,
            'query_type_avg_execution_time': summary.query_type_avg_execution_time,
            'failed_queries': summary.failed_queries,
            'error_rate': summary.error_rate
        }
        
        import json
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n✅ 评估完成！")
        print(f"📄 详细结果: {detailed_results_file}")
        print(f"📊 评估总结: {summary_file}")
        
        return summary
        
    except Exception as e:
        print(f"评估过程中发生错误: {e}")
        raise


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="运行混合查询评估")
    parser.add_argument("--max-assemblies", type=int, default=None,
                       help="最大评估装配体数量（默认：全部）")
    parser.add_argument("--top-k", type=int, default=100,
                       help="每个查询返回的结果数量（默认：10）")
    parser.add_argument("--benchmark-dir", type=str, default="data/benchmark",
                       help="基准测试数据目录（默认：data/benchmark）")
    
    args = parser.parse_args()
    
    # 运行评估
    asyncio.run(run_evaluation(
        max_assemblies=args.max_assemblies,
        top_k=args.top_k,
        benchmark_dir=args.benchmark_dir
    ))


if __name__ == "__main__":
    main()
