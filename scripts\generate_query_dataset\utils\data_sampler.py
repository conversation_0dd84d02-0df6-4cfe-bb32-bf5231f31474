#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据采样器

用于从数据库中随机抽取装配体数据
"""

import sys
import os
import random
import logging
from typing import Dict, List, Any, Optional

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

import psycopg2
from psycopg2.extras import RealDictCursor
from neo4j import GraphDatabase
from src.config import Config

logger = logging.getLogger(__name__)


class DataSampler:
    """数据采样器"""
    
    def __init__(self):
        """初始化数据采样器"""
        self.postgres_config = Config.POSTGRES_CONFIG
        self.neo4j_config = Config.get_neo4j_config()
        
    def _get_neo4j_driver(self):
        """获取Neo4j数据库驱动"""
        return GraphDatabase.driver(
            self.neo4j_config['uri'], 
            auth=(self.neo4j_config['user'], self.neo4j_config['password'])
        )
    
    def get_qualified_assemblies(self) -> List[str]:
        """
        获取符合条件的装配体UUID列表
        (所有子节点名称都不为Component/Untitled/Body)
        
        使用Neo4j数据库查询Assembly节点及其所有子节点
        
        Returns:
            符合条件的装配体UUID列表
        """
        qualified_uuids = []
        
        try:
            driver = self._get_neo4j_driver()
            
            with driver.session() as session:
                # 查询所有Assembly节点
                assembly_query = """
                MATCH (a:Assembly)
                RETURN a.uuid as uuid, a.name as name
                """
                
                assembly_result = session.run(assembly_query)
                assemblies = [record.data() for record in assembly_result]
                
                logger.info(f"找到{len(assemblies)}个装配体节点，开始检查子节点...")
                
                for assembly in assemblies:
                    assembly_uuid = assembly['uuid']
                    
                    # 查询该装配体的所有子节点（包括子装配体和零件）
                    children_query = """
                    MATCH (a:Assembly {uuid: $assembly_uuid})-[:hasComponent*]->(child)
                    WHERE child:Assembly OR child:Part OR child:SubAssembly
                    RETURN child.name as name, labels(child) as labels
                    """
                    
                    children_result = session.run(children_query, assembly_uuid=assembly_uuid)
                    children = [record.data() for record in children_result]
                    
                    # 检查是否所有子节点的名称都不包含无意义的名称
                    is_qualified = True
                    
                    for child in children:
                        child_name = child.get('name', '')
                        
                        if not child_name or child_name.strip() == '':
                            is_qualified = False
                            break
                            
                        # 检查是否包含无意义的名称
                        if (child_name.startswith('Component') or 
                            'Component' in child_name or
                            child_name.startswith('Untitled') or 
                            'Untitled' in child_name or
                            child_name.startswith('Body') or 
                            'Body' in child_name):
                            is_qualified = False
                            break
                    
                    # 如果该装配体没有子节点，也认为不符合条件
                    if len(children) == 0:
                        is_qualified = False
                    
                    if is_qualified:
                        qualified_uuids.append(assembly_uuid)
                        logger.debug(f"装配体 {assembly_uuid} 符合条件，有{len(children)}个有效子节点")
                    else:
                        logger.debug(f"装配体 {assembly_uuid} 不符合条件")
                
                logger.info(f"找到{len(qualified_uuids)}个符合条件的装配体")
                return qualified_uuids
                
        except Exception as e:
            logger.error(f"从Neo4j获取符合条件的装配体失败: {e}")
            return []
        finally:
            if 'driver' in locals():
                driver.close()

    def sample_assemblies(self, num_assemblies: int) -> List[Dict[str, Any]]:
        """
        从符合条件的装配体中随机抽取
        
        Args:
            num_assemblies: 抽取的装配体数量
            
        Returns:
            装配体列表
        """
        try:
            # 先从Neo4j获取符合条件的装配体UUID列表
            qualified_uuids = self.get_qualified_assemblies()
            
            if not qualified_uuids:
                logger.warning("没有找到符合条件的装配体")
                return []
            
            logger.info(f"符合条件的装配体总数: {len(qualified_uuids)}")
            
            # 如果符合条件的装配体数量少于需要抽取的数量，调整抽取数量
            actual_num = min(num_assemblies, len(qualified_uuids))
            if actual_num < num_assemblies:
                logger.warning(f"符合条件的装配体数量({len(qualified_uuids)})少于需要抽取的数量({num_assemblies})，将抽取{actual_num}个")
            
            # 随机选择装配体UUID
            selected_uuids = random.sample(qualified_uuids, actual_num)
            
            # 从PostgreSQL获取选中装配体的详细信息
            conn = psycopg2.connect(**self.postgres_config)
            
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                # 获取选中装配体的详细信息
                query = """
                SELECT 
                    uuid,
                    name,
                    length,
                    width,
                    height,
                    area,
                    volume,
                    density,
                    mass,
                    part_count,
                    joints_count,
                    contacts_count,
                    part_volume_std,
                    part_mass_std,
                    industry,
                    category,
                    description
                FROM assemblies
                WHERE uuid = ANY(%s)
                """
                
                cursor.execute(query, (selected_uuids,))
                results = cursor.fetchall()
                
                # 转换为字典列表
                assemblies = [dict(row) for row in results]
                
                logger.info(f"成功从数据库中抽取{len(assemblies)}个符合条件的装配体")
                return assemblies
                
        except Exception as e:
            logger.error(f"从数据库抽取装配体失败: {e}")
            return []
        finally:
            if 'conn' in locals():
                conn.close()
    
    def get_assembly_by_uuid(self, uuid: str) -> Optional[Dict[str, Any]]:
        """
        根据UUID获取装配体信息
        
        Args:
            uuid: 装配体UUID
            
        Returns:
            装配体信息或None
        """
        try:
            conn = psycopg2.connect(**self.postgres_config)
            
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                query = """
                SELECT 
                    uuid,
                    name,
                    length,
                    width,
                    height,
                    area,
                    volume,
                    density,
                    mass,
                    part_count,
                    joints_count,
                    contacts_count,
                    part_volume_std,
                    part_mass_std,
                    industry,
                    category,
                    description
                FROM assemblies
                WHERE uuid = %s
                """
                
                cursor.execute(query, (uuid,))
                result = cursor.fetchone()
                
                if result:
                    return dict(result)
                return None
                
        except Exception as e:
            logger.error(f"根据UUID获取装配体失败: {e}")
            return None
        finally:
            if 'conn' in locals():
                conn.close()
    
    def get_assembly_statistics(self) -> Dict[str, Any]:
        """
        获取装配体数据库统计信息
        
        Returns:
            统计信息字典
        """
        try:
            conn = psycopg2.connect(**self.postgres_config)
            
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                # 获取总数
                cursor.execute("SELECT COUNT(*) as total FROM assemblies")
                total_count = cursor.fetchone()['total']
                
                # 获取基本统计信息
                cursor.execute("""
                SELECT 
                    AVG(mass) as avg_mass,
                    MIN(mass) as min_mass,
                    MAX(mass) as max_mass,
                    AVG(part_count) as avg_part_count,
                    MIN(part_count) as min_part_count,
                    MAX(part_count) as max_part_count,
                    AVG(volume) as avg_volume,
                    MIN(volume) as min_volume,
                    MAX(volume) as max_volume
                FROM assemblies
                WHERE mass IS NOT NULL AND part_count IS NOT NULL AND volume IS NOT NULL
                """)
                
                stats = dict(cursor.fetchone())
                stats['total_assemblies'] = total_count
                
                # 获取行业分布
                cursor.execute("""
                SELECT industry, COUNT(*) as count
                FROM assemblies
                WHERE industry IS NOT NULL
                GROUP BY industry
                ORDER BY count DESC
                """)
                
                industry_distribution = {row['industry']: row['count'] for row in cursor.fetchall()}
                stats['industry_distribution'] = industry_distribution
                
                # 获取分类分布
                cursor.execute("""
                SELECT category, COUNT(*) as count
                FROM assemblies
                WHERE category IS NOT NULL
                GROUP BY category
                ORDER BY count DESC
                """)
                
                category_distribution = {row['category']: row['count'] for row in cursor.fetchall()}
                stats['category_distribution'] = category_distribution
                
                return stats
                
        except Exception as e:
            logger.error(f"获取装配体统计信息失败: {e}")
            return {}
        finally:
            if 'conn' in locals():
                conn.close()
