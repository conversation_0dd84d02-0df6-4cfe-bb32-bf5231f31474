# 装配体向量聚类分析指南

本指南介绍如何使用新的装配体向量聚类分析功能，这些功能专门用于处理通过 `extract_assembly_vectors.py` 生成的装配体向量表示。

## 概述

装配体向量聚类分析包含以下主要组件：

1. **装配体向量提取** (`scripts/extract_assembly_vectors.py`) - 从图数据中提取装配体节点的向量表示
2. **聚类分析** (`evaluate_scripts/clustering_analysis/hdbscan_cluster_assembly_vectors.py`) - 对向量进行 HDBSCAN 聚类
3. **结果可视化** (`evaluate_scripts/clustering_analysis/visualize_assembly_vector_clusters.py`) - 生成聚类结果的可视化
4. **功能测试** (`test_scripts/test_assembly_vector_clustering.py`) - 测试整个流程

## 前置条件

### 1. 数据准备

首先需要运行装配体向量提取脚本：

```bash
python scripts/extract_assembly_vectors.py --base-dir datasets/fusion360_assembly --output-path dataset/assembly_representations.pkl
```

这将生成包含装配体 ID 到向量映射的文件 `dataset/assembly_representations.pkl`。

### 2. 依赖安装

确保安装了以下依赖：

```bash
pip install hdbscan scikit-learn matplotlib pillow tqdm psycopg2
```

## 使用方法

### 1. 运行聚类分析

#### 基本用法

```bash
python evaluate_scripts/clustering_analysis/hdbscan_cluster_assembly_vectors.py \
    --embed dataset/assembly_representations.pkl
```

#### 自定义参数

```bash
# 使用余弦距离和较大的最小簇大小
python evaluate_scripts/clustering_analysis/hdbscan_cluster_assembly_vectors.py \
    --embed dataset/assembly_representations.pkl \
    --metric cosine \
    --min_cluster_size 10 \
    --norm 1

# 使用PCA降维
python evaluate_scripts/clustering_analysis/hdbscan_cluster_assembly_vectors.py \
    --embed dataset/assembly_representations.pkl \
    --pca 1 \
    --pca-components 32 \
    --min_cluster_size 5

# 包含详细分析
python evaluate_scripts/clustering_analysis/hdbscan_cluster_assembly_vectors.py \
    --embed dataset/assembly_representations.pkl \
    --analyze \
    --find-similar 100097_8a50ea9c
```

#### 参数说明

- `--embed`: 装配体向量表示文件路径
- `--out`: 输出聚类结果文件路径（默认：`dataset/hdbscan_assembly_vector_labels.pkl`）
- `--min_cluster_size`: HDBSCAN 最小簇大小（默认：5）
- `--metric`: 距离度量（euclidean/cosine，默认：euclidean）
- `--norm`: 是否进行 L2 归一化（1/0，默认：1）
- `--pca`: 是否使用 PCA 降维（1/0，默认：0）
- `--pca-components`: PCA 降维后的维度（默认：32）
- `--analyze`: 进行详细的聚类结果分析
- `--find-similar`: 查找与指定装配体 ID 相似的装配体

### 2. 生成可视化

#### 基本可视化

```bash
python evaluate_scripts/clustering_analysis/visualize_assembly_vector_clusters.py \
    --labels dataset/hdbscan_assembly_vector_labels.pkl \
    --img_dir datasets/fusion360_assembly
```

#### 自定义可视化

```bash
# 显示前15个最大的簇，每簇显示20个样本，并生成摘要
python evaluate_scripts/clustering_analysis/visualize_assembly_vector_clusters.py \
    --labels dataset/hdbscan_assembly_vector_labels.pkl \
    --img_dir datasets/fusion360_assembly \
    --top_n 15 \
    --samples_per_cluster 20 \
    --save_summary

# 自定义输出目录
python evaluate_scripts/clustering_analysis/visualize_assembly_vector_clusters.py \
    --labels dataset/hdbscan_assembly_vector_labels.pkl \
    --img_dir datasets/fusion360_assembly \
    --out_dir visualization_results/my_assembly_clusters
```

#### 参数说明

- `--labels`: 聚类结果文件路径
- `--img_dir`: 装配体图片目录（默认：`datasets/fusion360_assembly`）
- `--out_dir`: 输出目录（默认：`visualization_results/assembly_vector_clusters`）
- `--top_n`: 显示前 N 个最大的簇（默认：10）
- `--samples_per_cluster`: 每个簇显示的样本数量（默认：16）
- `--min_cluster_size`: 只显示大于等于此大小的簇（默认：1）
- `--save_summary`: 保存聚类摘要报告

### 3. 使用 VS Code 任务

项目提供了预配置的 VS Code 任务，可以通过命令面板（Ctrl+Shift+P）选择：

1. **Cluster Assembly Vectors** - 基本聚类
2. **Cluster Assembly Vectors (Cosine)** - 使用余弦距离的聚类
3. **Cluster Assembly Vectors (PCA)** - 使用 PCA 降维的聚类
4. **Visualize Assembly Vector Clusters** - 基本可视化
5. **Visualize Assembly Vector Clusters (Summary)** - 带摘要的可视化

### 4. 测试功能

运行测试脚本以验证功能是否正常：

```bash
python test_scripts/test_assembly_vector_clustering.py
```

## 输出说明

### 聚类结果文件

聚类分析会生成包含以下内容的 pickle 文件：

- `labels`: 每个装配体的簇标签（-1 表示噪声点）
- `assembly_ids`: 装配体 ID 列表
- `assembly_info`: 从数据库获取的装配体详细信息
- `feature_type`: 特征类型标识
- `params`: 聚类参数
- `metrics`: 评估指标（silhouette、DBI、CHI）
- `cluster_persistence`: HDBSCAN 聚类持久性信息
- `pca_model`: PCA 模型（如果使用）

### 可视化输出

可视化脚本会生成：

1. **簇图片网格** (`cluster_XXX_size_YYY.png`) - 每个簇的装配体图片网格
2. **簇大小总览** (`cluster_size_overview.png`) - 显示各簇大小的条形图
3. **聚类摘要** (`clustering_summary.md`) - 包含聚类统计信息的 Markdown 报告

## 评估指标

### 内部聚类评估指标

1. **Silhouette Score** (-1 到 1): 越接近 1 表示聚类质量越好
2. **Davies-Bouldin Index**: 越小表示聚类质量越好
3. **Calinski-Harabasz Index**: 越大表示聚类质量越好

### 聚类统计信息

- 簇数量
- 噪声点比例
- 簇大小分布
- 装配体特征统计（零件数、体积、质量等）

## 最佳实践

### 1. 参数调优

- **min_cluster_size**: 根据数据集大小调整，一般为数据量的 1-5%
- **metric**: 对于归一化的向量，推荐使用 cosine 距离
- **PCA**: 当向量维度较高时可以考虑降维

### 2. 结果解释

- 关注 silhouette score > 0.3 的聚类结果
- 噪声比例过高（>50%）可能需要调整参数
- 分析簇的装配体特征分布以理解聚类含义

### 3. 可视化技巧

- 使用`--save_summary`生成详细报告
- 根据需要调整`--samples_per_cluster`以平衡信息量和可视性
- 结合数据库中的装配体信息分析聚类特征

## 故障排除

### 常见问题

1. **文件不存在错误**: 确保先运行`extract_assembly_vectors.py`
2. **内存不足**: 对于大数据集，考虑使用 PCA 降维
3. **图片加载失败**: 检查`--img_dir`路径是否正确
4. **数据库连接失败**: 确保配置文件中的数据库连接信息正确

### 性能优化

- 使用 PCA 降维可以显著提高大数据集的处理速度
- 调整`min_cluster_size`以平衡聚类质量和计算效率
- 对于可视化，使用`--top_n`限制显示的簇数量

## 与其他聚类方法的比较

本实现与现有的`hdbscan_cluster_assembly_embeddings.py`的主要区别：

1. **数据格式**: 专门处理简单的 ID-向量映射格式
2. **预处理**: 自动检测和处理零向量、重复向量
3. **分析功能**: 提供更详细的聚类特征分析
4. **可视化**: 改进的图片加载逻辑和错误处理
5. **报告**: 生成更全面的聚类摘要报告

建议根据数据格式选择合适的聚类脚本：

- 使用`hdbscan_cluster_assembly_vectors.py`处理`assembly_representations.pkl`
- 使用`hdbscan_cluster_assembly_embeddings.py`处理`assembly_features.pkl`
