<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CAD-RAG 多模态查询系统</title>
    <style>
        :root {
            --primary-color: #2a6fc9;
            --secondary-color: #4CAF50;
            --error-color: #f44336;
            --background-color: #f5f8fa;
            --card-background: #ffffff;
            --text-color: #333333;
            --border-color: #e1e4e8;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: var(--background-color);
            color: var(--text-color);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            background-color: var(--primary-color);
            color: white;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            margin: 0;
            font-size: 2em;
        }
        
        .card {
            background-color: var(--card-background);
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .query-form {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        label {
            font-weight: 600;
            margin-bottom: 5px;
            display: block;
        }
        
        textarea, input[type="file"], input[type="number"] {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 1em;
        }
        
        textarea {
            min-height: 100px;
            resize: vertical;
        }
        
        .buttons {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }
        
        button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: #1c54a8;
        }
        
        .results-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .chat-container {
            display: flex;
            flex-direction: column;
            height: 500px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            overflow: hidden;
        }
        
        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background-color: var(--background-color);
        }
        
        .message {
            margin-bottom: 15px;
            padding: 10px 15px;
            border-radius: 8px;
            max-width: 80%;
            line-height: 1.5;
        }
        
        .message.system {
            background-color: #f1f1f1;
            align-self: center;
            width: fit-content;
            margin-left: auto;
            margin-right: auto;
            color: #666;
            font-style: italic;
        }
        
        .message.user {
            background-color: var(--primary-color);
            color: white;
            align-self: flex-end;
            margin-left: auto;
        }
        
        .message.agent {
            background-color: #e9ecef;
            align-self: flex-start;
            margin-right: auto;
        }
        
        .message.error {
            background-color: #ffebee;
            color: var(--error-color);
            border-left: 4px solid var(--error-color);
        }
        
        .message.success {
            background-color: #e8f5e9;
            color: var(--secondary-color);
            border-left: 4px solid var(--secondary-color);
        }
        
        .message.sub-query {
            background-color: #f3e5f5;
            color: #7b1fa2;
            border-left: 4px solid #7b1fa2;
            font-family: monospace;
            white-space: pre-line;
        }
        
        .message.conversion {
            background-color: #e3f2fd;
            color: #1976d2;
            border-left: 4px solid #1976d2;
        }
        
        .conversion-code {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            margin: 8px 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            overflow-x: auto;
            white-space: pre-wrap;
            word-break: break-all;
        }
        
        .chat-input {
            display: flex;
            padding: 10px;
            background-color: white;
            border-top: 1px solid var(--border-color);
        }
        
        .chat-input input {
            flex: 1;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            margin-right: 10px;
        }
        
        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .result-card {
            background-color: var(--card-background);
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: transform 0.3s;
        }
        
        .result-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .result-image {
            width: 100%;
            height: 180px;
            object-fit: cover;
            background-color: #f1f1f1;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .result-image img {
            max-width: 100%;
            max-height: 100%;
        }
        
        .result-details {
            padding: 15px;
        }
        
        .result-name {
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .result-description {
            color: #666;
            font-size: 0.9em;
            margin-bottom: 10px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .result-score {
            color: var(--primary-color);
            font-size: 0.9em;
            font-weight: 600;
        }
        
        .steps-container {
            margin-top: 20px;
        }
        
        .step {
            margin-bottom: 10px;
            padding: 10px;
            border-radius: 4px;
            border-left: 3px solid var(--primary-color);
            background-color: white;
        }
        
        .step-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        
        .step-title {
            font-weight: 600;
        }
        
        .step-status {
            font-size: 0.9em;
            padding: 2px 8px;
            border-radius: 10px;
        }
        
        .step-status.pending {
            background-color: #FFF9C4;
            color: #FBC02D;
        }
        
        .step-status.success {
            background-color: #C8E6C9;
            color: #388E3C;
        }
        
        .step-status.failure {
            background-color: #FFCDD2;
            color: #D32F2F;
        }
        
        .step-details {
            font-size: 0.9em;
            color: #666;
        }
        
        .tab-container {
            margin-top: 20px;
        }
        
        .tabs {
            display: flex;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 20px;
        }
        
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
        }
        
        .tab.active {
            border-bottom: 2px solid var(--primary-color);
            color: var(--primary-color);
            font-weight: 600;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .results-grid {
                grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>CAD-RAG 多模态查询系统</h1>
            <p>支持文字、图片或文字+图片的统一多模态查询</p>
        </header>
        
        <div class="card">
            <h2>查询表单</h2>
            <div class="query-form">
                <div>
                    <label for="query-text">查询文本（可选，与图片至少提供一项）</label>
                    <textarea id="query-text" placeholder="请输入查询文本，例如：'找一个含有齿轮的装配体'"></textarea>
                </div>
                
                <div>
                    <label for="query-image">图片上传（可选，与文本至少提供一项）</label>
                    <input type="file" id="query-image" accept="image/*">
                </div>
                
                <div>
                    <label for="top-k">返回结果数量</label>
                    <input type="number" id="top-k" value="5" min="1" max="50">
                </div>
                
                <div class="buttons">
                    <button id="query-btn">开始查询</button>
                </div>
            </div>
        </div>
        
        <div class="tab-container">
            <div class="tabs">
                <div class="tab active" data-tab="agent-chat">智能体对话</div>
                <div class="tab" data-tab="results">查询结果</div>
            </div>
            
            <div class="tab-content active" id="agent-chat">
                <div class="chat-container">
                    <div class="chat-messages" id="chat-messages">
                        <div class="message system">
                            系统已准备就绪，请输入查询。
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="tab-content" id="results">
                <div class="results-container">
                    <div id="results-info" class="card" style="display: none;">
                        <h3>查询信息</h3>
                        <div id="query-time"></div>
                        <div id="results-count"></div>
                    </div>
                    
                    <div id="results-grid" class="results-grid">
                        <!-- 结果将在这里显示 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // DOM 元素
        const queryBtn = document.getElementById('query-btn');
        const queryTextInput = document.getElementById('query-text');
        const queryImageInput = document.getElementById('query-image');
        const topKInput = document.getElementById('top-k');
        const chatMessages = document.getElementById('chat-messages');
        const resultsGrid = document.getElementById('results-grid');
        const resultsInfo = document.getElementById('results-info');
        const queryTimeEl = document.getElementById('query-time');
        const resultsCountEl = document.getElementById('results-count');
        const tabs = document.querySelectorAll('.tab');
        const tabContents = document.querySelectorAll('.tab-content');
        
        // WebSocket 连接
        let ws;
        let isConnected = false;
        
        // 初始化 WebSocket 连接
        function initWebSocket() {
            // 获取当前页面的主机和端口
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws`;
            
            ws = new WebSocket(wsUrl);
            
            ws.onopen = () => {
                console.log('WebSocket 连接已建立');
                isConnected = true;
                addSystemMessage('WebSocket 连接已建立');
            };
            
            ws.onmessage = (event) => {
                const data = JSON.parse(event.data);
                handleWebSocketMessage(data);
            };
            
            ws.onclose = () => {
                console.log('WebSocket 连接已关闭');
                isConnected = false;
                addSystemMessage('WebSocket 连接已关闭，请刷新页面重新连接');
            };
            
            ws.onerror = (error) => {
                console.error('WebSocket 错误:', error);
                addErrorMessage('WebSocket 连接错误');
            };
            
            // 每隔 30 秒发送一次心跳消息
            setInterval(() => {
                if (isConnected) {
                    ws.send(JSON.stringify({ action: 'ping' }));
                }
            }, 30000);
        }
        
        // 处理 WebSocket 消息
        function handleWebSocketMessage(data) {
            const eventType = data.event_type;
            const message = data.message;
            
            switch (eventType) {
                case 'connection_established':
                    addSystemMessage(message);
                    break;
                    
                case 'query_start':
                    // 清空旧消息和结果
                    clearChatMessages();
                    clearResults();
                    
                    // 添加系统开始查询消息
                    addAgentMessage(message);
                    break;
                    
                case 'plan_created':
                    addAgentMessage(message);
                    break;
                    
                case 'plan_step_detail':
                    addSubQueryMessage(message);
                    break;
                    
                case 'plan_step':
                    addAgentMessage(message);
                    break;
                    
                case 'step_start':
                    addAgentMessage(message);
                    break;
                    
                case 'step_info':
                    addAgentMessage(message);
                    break;
                    
                case 'step_complete':
                    addAgentMessage(message);
                    break;
                    
                case 'text2sql_result':
                    addConversionMessage(message, data);
                    break;
                    
                case 'sql_execution_result':
                    addAgentMessage(message);
                    break;
                    
                case 'text2cypher_result':
                    addConversionMessage(message, data);
                    break;
                    
                case 'cypher_execution_result':
                    addAgentMessage(message);
                    break;
                    
                case 'step_error':
                    addErrorMessage(message);
                    break;
                    
                case 'fusion_start':
                    addAgentMessage(message);
                    break;
                    
                case 'fusion_info':
                    addAgentMessage(message);
                    break;
                    
                case 'model_paths':
                    addAgentMessage(message);
                    break;
                    
                case 'fusion_complete':
                    addSuccessMessage(message);
                    break;
                    
                case 'query_complete':
                    // 显示结果
                    if (data.data && data.data.results) {
                        displayResults(data.data);
                    }
                    
                    // 添加完成消息
                    addSuccessMessage(message);
                    
                    // 切换到结果标签页
                    if (data.data && data.data.results && data.data.results.length > 0) {
                        setTimeout(() => {
                            activateTab('results');
                        }, 1000);
                    }
                    break;
                    
                case 'error':
                    addErrorMessage(message);
                    break;
                    
                case 'pong':
                    // 心跳响应，不做任何处理
                    break;
                    
                default:
                    console.log('未知的事件类型:', eventType, data);
                    break;
            }
            
            // 滚动到底部
            scrollToBottom();
        }
        
        // 发送查询请求
        function sendQuery() {
            if (!isConnected) {
                addErrorMessage('WebSocket 未连接，请刷新页面重试');
                return;
            }
            
            const queryText = queryTextInput.value.trim();
            const topK = parseInt(topKInput.value) || 5;
            const hasImage = queryImageInput.files && queryImageInput.files[0];
            
            // 验证至少有一个输入（文本或图片）
            if (!queryText && !hasImage) {
                addErrorMessage('请输入查询文本或选择图片');
                return;
            }
            
            // 准备查询数据
            const queryData = {
                action: 'query',
                top_k: topK
            };
            
            // 添加文本查询（如果有）
            if (queryText) {
                queryData.query_text = queryText;
            }
            
            // 添加图片查询（如果有）
            if (hasImage) {
                // 读取并编码图片
                const reader = new FileReader();
                reader.onload = function(e) {
                    queryData.image_data = e.target.result;
                    
                    // 显示用户查询
                    if (queryText) {
                        addUserMessage(`${queryText} (附带图片)`);
                    } else {
                        addUserMessage('图片查询');
                    }
                    
                    // 发送查询
                    ws.send(JSON.stringify(queryData));
                    
                    // 激活聊天标签页
                    activateTab('agent-chat');
                };
                reader.readAsDataURL(queryImageInput.files[0]);
            } else {
                // 仅文本查询
                addUserMessage(queryText);
                
                // 发送查询
                ws.send(JSON.stringify(queryData));
                
                // 激活聊天标签页
                activateTab('agent-chat');
            }
        }
        
        // 显示查询结果
        function displayResults(data) {
            // 显示查询信息
            if (data.execution_time) {
                queryTimeEl.textContent = `执行时间: ${data.execution_time.toFixed(2)} 秒`;
            }
            
            if (data.total_results !== undefined) {
                resultsCountEl.textContent = `结果数量: ${data.total_results}`;
            }
            
            resultsInfo.style.display = 'block';
            
            // 清空旧结果
            resultsGrid.innerHTML = '';
            
            // 添加新结果
            if (data.results && data.results.length > 0) {
                data.results.forEach(result => {
                    const resultCard = document.createElement('div');
                    resultCard.className = 'result-card';
                    
                    // 创建模型预览图
                    const resultImage = document.createElement('div');
                    resultImage.className = 'result-image';
                    
                    if (result.model_url) {
                        // 如果有模型URL，显示一个占位符图标
                        resultImage.innerHTML = `
                            <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="#2a6fc9" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M2 17L12 22L22 17" stroke="#2a6fc9" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M2 12L12 17L22 12" stroke="#2a6fc9" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        `;
                    } else {
                        // 无模型URL时的占位符
                        resultImage.innerHTML = `
                            <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect x="3" y="3" width="18" height="18" rx="2" stroke="#999" stroke-width="2"/>
                                <path d="M3 16L8 11L13 16" stroke="#999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M14 14L16 12L21 17" stroke="#999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <circle cx="16" cy="8" r="2" stroke="#999" stroke-width="2"/>
                            </svg>
                        `;
                    }
                    
                    // 创建结果详情
                    const resultDetails = document.createElement('div');
                    resultDetails.className = 'result-details';
                    
                    const resultName = document.createElement('div');
                    resultName.className = 'result-name';
                    resultName.textContent = result.name || '未命名模型';
                    
                    const resultDescription = document.createElement('div');
                    resultDescription.className = 'result-description';
                    resultDescription.textContent = result.description || '无描述';
                    
                    const resultScore = document.createElement('div');
                    resultScore.className = 'result-score';
                    if (result.similarity_score !== undefined && result.similarity_score !== null) {
                        resultScore.textContent = `相似度: ${(result.similarity_score * 100).toFixed(2)}%`;
                    } else {
                        resultScore.textContent = `ID: ${result.uuid}`;
                    }
                    
                    // 组装结果卡片
                    resultDetails.appendChild(resultName);
                    resultDetails.appendChild(resultDescription);
                    resultDetails.appendChild(resultScore);
                    
                    resultCard.appendChild(resultImage);
                    resultCard.appendChild(resultDetails);
                    
                    // 添加点击事件，如果有模型URL
                    if (result.model_url) {
                        resultCard.style.cursor = 'pointer';
                        resultCard.onclick = () => {
                            window.open(result.model_url, '_blank');
                        };
                    }
                    
                    resultsGrid.appendChild(resultCard);
                });
            } else {
                const noResults = document.createElement('div');
                noResults.className = 'card';
                noResults.style.gridColumn = '1 / -1';
                noResults.style.textAlign = 'center';
                noResults.innerHTML = '<p>没有找到匹配的结果</p>';
                resultsGrid.appendChild(noResults);
            }
        }
        
        // 添加用户消息
        function addUserMessage(text) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message user';
            messageDiv.textContent = text;
            chatMessages.appendChild(messageDiv);
        }
        
        // 添加智能体消息
        function addAgentMessage(text) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message agent';
            messageDiv.textContent = text;
            chatMessages.appendChild(messageDiv);
        }
        
        // 添加系统消息
        function addSystemMessage(text) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message system';
            messageDiv.textContent = text;
            chatMessages.appendChild(messageDiv);
        }
        
        // 添加错误消息
        function addErrorMessage(text) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message error';
            messageDiv.textContent = text;
            chatMessages.appendChild(messageDiv);
        }
        
        // 添加成功消息
        function addSuccessMessage(text) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message success';
            messageDiv.textContent = text;
            chatMessages.appendChild(messageDiv);
        }
        
        // 添加子查询详情消息
        function addSubQueryMessage(text) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message sub-query';
            messageDiv.textContent = text;
            chatMessages.appendChild(messageDiv);
        }
        
        // 添加转换结果消息
        function addConversionMessage(text, data) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message conversion';
            
            // 创建消息文本
            const textSpan = document.createElement('div');
            textSpan.textContent = text;
            messageDiv.appendChild(textSpan);
            
            // 添加转换后的代码
            if (data) {
                if (data.sql_query) {
                    const codeDiv = document.createElement('div');
                    codeDiv.className = 'conversion-code';
                    codeDiv.textContent = data.sql_query;
                    messageDiv.appendChild(codeDiv);
                } else if (data.cypher_query) {
                    const codeDiv = document.createElement('div');
                    codeDiv.className = 'conversion-code';
                    codeDiv.textContent = data.cypher_query;
                    messageDiv.appendChild(codeDiv);
                }
            }
            
            chatMessages.appendChild(messageDiv);
        }
        
        // 清空聊天消息
        function clearChatMessages() {
            chatMessages.innerHTML = '';
        }
        
        // 清空结果
        function clearResults() {
            resultsGrid.innerHTML = '';
            resultsInfo.style.display = 'none';
        }
        
        // 滚动到底部
        function scrollToBottom() {
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
        
        // 标签页切换
        function activateTab(tabId) {
            tabs.forEach(tab => {
                if (tab.dataset.tab === tabId) {
                    tab.classList.add('active');
                } else {
                    tab.classList.remove('active');
                }
            });
            
            tabContents.forEach(content => {
                if (content.id === tabId) {
                    content.classList.add('active');
                } else {
                    content.classList.remove('active');
                }
            });
        }
        
        // 事件监听
        queryBtn.addEventListener('click', sendQuery);
        
        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                activateTab(tab.dataset.tab);
            });
        });
        
        // 页面加载时初始化 WebSocket
        window.addEventListener('load', () => {
            initWebSocket();
        });
    </script>
</body>
</html>