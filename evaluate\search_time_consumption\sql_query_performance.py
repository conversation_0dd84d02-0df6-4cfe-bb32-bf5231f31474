#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SQL查询性能测试脚本

测试不同类型的SQL查询在零件表上的执行时间：
1. 基于单一物理属性的范围查询
2. 多重物理属性组合查询
3. 混合属性（物理+制造特征）的复杂查询

在整个零件表和随机子集上分别测试，并生成对比表格
"""

import psycopg2
import time
import random
import logging
import json
import os
import sys
from typing import List, Dict, Any, Tuple
from statistics import mean, stdev
from datetime import datetime
import pandas as pd
import numpy as np

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.config import Config

# 配置日志
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SQLQueryPerformanceTester:
    """SQL查询性能测试器"""
    
    def __init__(self, db_params: Dict[str, str], subset_size: int = 100):
        """
        初始化SQL查询性能测试器
        
        Args:
            db_params: 数据库连接参数
            subset_size: 测试子集大小
        """
        self.db_params = db_params
        self.subset_size = subset_size
        self.conn = None
        self.cursor = None
        self.evaluation_results = {}
        
    def connect(self):
        """建立数据库连接"""
        try:
            self.conn = psycopg2.connect(**self.db_params)
            self.cursor = self.conn.cursor()
            logger.info("成功连接到PostgreSQL数据库")
        except Exception as e:
            logger.error(f"连接数据库失败: {e}")
            raise
            
    def close(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()
            logger.info("数据库连接已关闭")
    
    def get_table_stats(self) -> Dict[str, Any]:
        """获取零件表的统计信息"""
        stats_queries = {
            'total_count': "SELECT COUNT(*) FROM parts",
            'mass_range': "SELECT MIN(mass), MAX(mass) FROM parts WHERE mass IS NOT NULL",
            'length_range': "SELECT MIN(length), MAX(length) FROM parts WHERE length IS NOT NULL",
            'width_range': "SELECT MIN(width), MAX(width) FROM parts WHERE width IS NOT NULL",
            'height_range': "SELECT MIN(height), MAX(height) FROM parts WHERE height IS NOT NULL",
            'hole_count_range': "SELECT MIN(hole_count), MAX(hole_count) FROM parts WHERE hole_count IS NOT NULL",
            'hole_diameter_range': "SELECT MIN(hole_diameter_mean), MAX(hole_diameter_mean) FROM parts WHERE hole_diameter_mean IS NOT NULL",
            'materials': "SELECT DISTINCT material FROM parts WHERE material IS NOT NULL LIMIT 10"
        }
        
        stats = {}
        for key, query in stats_queries.items():
            try:
                self.cursor.execute(query)
                result = self.cursor.fetchall()
                stats[key] = result
                logger.info(f"{key}: {result}")
            except Exception as e:
                logger.error(f"获取统计信息失败 {key}: {e}")
                stats[key] = None
                
        return stats
    
    def create_test_subset(self) -> str:
        """创建测试子集临时表"""
        try:
            # 删除可能存在的临时表
            self.cursor.execute("DROP TABLE IF EXISTS temp_parts_subset")
            
            # 创建临时表并插入随机选择的数据
            create_subset_query = f"""
            CREATE TEMP TABLE temp_parts_subset AS
            SELECT * FROM parts
            ORDER BY RANDOM()
            LIMIT {self.subset_size}
            """
            self.cursor.execute(create_subset_query)
            self.conn.commit()
            
            # 创建索引以提高查询性能
            self.cursor.execute("CREATE INDEX idx_temp_mass ON temp_parts_subset(mass)")
            self.cursor.execute("CREATE INDEX idx_temp_length ON temp_parts_subset(length)")
            self.cursor.execute("CREATE INDEX idx_temp_width ON temp_parts_subset(width)")
            self.cursor.execute("CREATE INDEX idx_temp_material ON temp_parts_subset(material)")
            self.cursor.execute("CREATE INDEX idx_temp_hole_count ON temp_parts_subset(hole_count)")
            self.conn.commit()
            
            logger.info(f"创建了包含{self.subset_size}条记录的测试子集")
            return "temp_parts_subset"
            
        except Exception as e:
            logger.error(f"创建测试子集失败: {e}")
            raise
    
    def generate_single_attribute_queries(self, stats: Dict[str, Any], count: int = 100) -> List[str]:
        """生成基于单一物理属性的范围查询"""
        queries = []
        
        # 获取数据范围
        if stats['mass_range'] and stats['mass_range'][0]:
            mass_min, mass_max = stats['mass_range'][0]
            if mass_min is not None and mass_max is not None:
                for _ in range(count):
                    threshold = random.uniform(mass_min, mass_max)
                    operator = random.choice(['<', '>', '<=', '>='])
                    query = f"""
                    SELECT uuid, name, mass, length, width, height
                    FROM parts
                    WHERE mass {operator} {threshold:.3f}
                    """
                    queries.append(query.strip())
        
        return queries
    
    def generate_multi_attribute_queries(self, stats: Dict[str, Any], count: int = 100) -> List[str]:
        """生成多重物理属性组合查询"""
        queries = []
        
        # 获取数据范围
        length_min, length_max = stats['length_range'][0] if stats['length_range'] and stats['length_range'][0] else (0, 1000)
        width_min, width_max = stats['width_range'][0] if stats['width_range'] and stats['width_range'][0] else (0, 1000)
        height_min, height_max = stats['height_range'][0] if stats['height_range'] and stats['height_range'][0] else (0, 1000)
        mass_min, mass_max = stats['mass_range'][0] if stats['mass_range'] and stats['mass_range'][0] else (0, 100)
        
        # 确保范围有效
        for var_name, var_value in [('length_min', length_min), ('length_max', length_max),
                                   ('width_min', width_min), ('width_max', width_max),
                                   ('height_min', height_min), ('height_max', height_max),
                                   ('mass_min', mass_min), ('mass_max', mass_max)]:
            if var_value is None:
                if 'min' in var_name:
                    exec(f"{var_name} = 0")
                else:
                    exec(f"{var_name} = 1000 if 'mass' not in var_name else 100")
        
        for _ in range(count):
            # 随机生成阈值
            length_threshold = random.uniform(length_min, length_max)
            width_threshold = random.uniform(width_min, width_max)
            height_threshold = random.uniform(height_min, height_max)
            mass_threshold = random.uniform(mass_min, mass_max)
            
            # 随机选择操作符
            length_op = random.choice(['<', '>', '<=', '>='])
            width_op = random.choice(['<', '>', '<=', '>='])
            height_op = random.choice(['<', '>', '<=', '>='])
            mass_op = random.choice(['<', '>', '<=', '>='])
            
            # 随机选择2-3个属性组合
            attributes = [
                (f"length {length_op} {length_threshold:.3f}", "length, width, height"),
                (f"width {width_op} {width_threshold:.3f}", "length, width, height"),
                (f"height {height_op} {height_threshold:.3f}", "length, width, height"),
                (f"mass {mass_op} {mass_threshold:.3f}", "mass, volume, density")
            ]
            
            selected_attrs = random.sample(attributes, random.randint(2, 3))
            conditions = [attr[0] for attr in selected_attrs]
            columns = selected_attrs[0][1]  # 使用第一个属性组的列
            
            query = f"""
            SELECT uuid, name, {columns}, material
            FROM parts
            WHERE {' AND '.join(conditions)}
            """
            queries.append(query.strip())
        
        return queries
    
    def generate_mixed_attribute_queries(self, stats: Dict[str, Any], count: int = 100) -> List[str]:
        """生成混合属性（物理+制造特征）的复杂查询"""
        queries = []
        
        # 获取材料列表
        materials = []
        if stats['materials'] and stats['materials'][0]:
            materials = [mat[0] for mat in stats['materials'] if mat[0] is not None]
        
        # 如果没有材料数据，使用默认材料
        if not materials:
            materials = ['Aluminum', 'Steel', 'Plastic', 'Brass', 'Copper', 'Iron', 'Titanium']
        
        # 获取孔数范围
        hole_count_min, hole_count_max = (0, 20)
        if stats['hole_count_range'] and stats['hole_count_range'][0]:
            hole_count_min, hole_count_max = stats['hole_count_range'][0]
            if hole_count_min is None: hole_count_min = 0
            if hole_count_max is None: hole_count_max = 20
        
        # 获取孔直径范围
        hole_diameter_min, hole_diameter_max = (0, 50)
        if stats['hole_diameter_range'] and stats['hole_diameter_range'][0]:
            hole_diameter_min, hole_diameter_max = stats['hole_diameter_range'][0]
            if hole_diameter_min is None: hole_diameter_min = 0
            if hole_diameter_max is None: hole_diameter_max = 50
        
        for _ in range(count):
            # 随机选择材料
            material = random.choice(materials)
            
            # 随机生成孔相关阈值
            hole_count_threshold = random.randint(hole_count_min, hole_count_max)
            hole_diameter_threshold = random.uniform(hole_diameter_min, hole_diameter_max)
            
            # 随机选择操作符
            hole_count_op = random.choice(['<', '>', '<=', '>=', '='])
            hole_diameter_op = random.choice(['<', '>', '<=', '>='])
            
            # 随机选择查询类型
            query_types = [
                # 材料 + 孔数量
                f"""
                SELECT uuid, name, material, hole_count, hole_diameter_mean
                FROM parts
                WHERE material = '{material}' AND hole_count {hole_count_op} {hole_count_threshold}
                """,
                # 材料 + 孔直径
                f"""
                SELECT uuid, name, material, hole_count, hole_diameter_mean, volume
                FROM parts
                WHERE material = '{material}' AND hole_diameter_mean {hole_diameter_op} {hole_diameter_threshold:.3f}
                """,
                # 孔数量 + 孔直径
                f"""
                SELECT uuid, name, hole_count, hole_diameter_mean, hole_depth_mean
                FROM parts
                WHERE hole_count {hole_count_op} {hole_count_threshold} AND hole_diameter_mean {hole_diameter_op} {hole_diameter_threshold:.3f}
                """
            ]
            
            query = random.choice(query_types)
            queries.append(query.strip())
        
        return queries
    
    def execute_query_with_timing(self, query: str, table_name: str = "parts") -> Tuple[float, int]:
        """
        执行查询并测量执行时间
        
        Args:
            query: SQL查询语句
            table_name: 表名，用于替换查询中的表名
            
        Returns:
            执行时间(秒), 结果行数
        """
        # 替换查询中的表名
        if table_name != "parts":
            query = query.replace("parts", table_name)
        
        try:
            start_time = time.time()
            self.cursor.execute(query)
            results = self.cursor.fetchall()
            end_time = time.time()
            
            execution_time = end_time - start_time
            result_count = len(results)
            
            return execution_time, result_count
            
        except Exception as e:
            logger.error(f"查询执行失败: {e}")
            logger.error(f"问题查询: {query}")
            return 0.0, 0
    
    def run_performance_test(self, queries: List[str], query_type: str, table_name: str = "parts") -> Dict[str, Any]:
        """
        运行性能测试
        
        Args:
            queries: 查询列表
            query_type: 查询类型名称
            table_name: 表名
            
        Returns:
            性能测试结果
        """
        logger.info(f"开始测试 {query_type} 查询 (共{len(queries)}个查询)")
        
        execution_times = []
        result_counts = []
        failed_queries = []
        
        for i, query in enumerate(queries):
            if i % 10 == 0:
                logger.info(f"进度: {i}/{len(queries)}")
            
            exec_time, result_count = self.execute_query_with_timing(query, table_name)
            
            if exec_time > 0:
                execution_times.append(exec_time)
                result_counts.append(result_count)
            else:
                failed_queries.append(query)
        
        # 计算统计信息
        if execution_times:
            avg_time = mean(execution_times)
            std_time = stdev(execution_times) if len(execution_times) > 1 else 0
            min_time = min(execution_times)
            max_time = max(execution_times)
            avg_results = mean(result_counts)
        else:
            avg_time = std_time = min_time = max_time = avg_results = 0
        
        results = {
            'query_type': query_type,
            'table_name': table_name,
            'total_queries': len(queries),
            'successful_queries': len(execution_times),
            'failed_queries': len(failed_queries),
            'avg_execution_time': avg_time,
            'std_execution_time': std_time,
            'min_execution_time': min_time,
            'max_execution_time': max_time,
            'avg_result_count': avg_results,
            'execution_times': execution_times,
            'result_counts': result_counts
        }
        
        logger.info(f"{query_type} 测试完成:")
        logger.info(f"  成功查询: {len(execution_times)}/{len(queries)}")
        logger.info(f"  平均执行时间: {avg_time:.4f}s")
        logger.info(f"  标准差: {std_time:.4f}s")
        logger.info(f"  平均结果数: {avg_results:.1f}")
        
        return results
    
    def generate_comparison_table(self) -> pd.DataFrame:
        """生成性能对比表格"""
        if not self.evaluation_results:
            logger.warning("没有评估结果可以生成表格")
            return pd.DataFrame()
        
        # 提取表格数据
        table_data = []
        
        for query_type in ['single_attribute', 'multi_attribute', 'mixed_attribute']:
            results = self.evaluation_results.get(query_type, {})
            
            full_result = results.get('full_table', {})
            subset_result = results.get('subset_table', {})
            
            # 整个表的结果
            if full_result:
                table_data.append({
                    '查询类型': query_type.replace('_', ' ').title(),
                    '表类型': '完整表',
                    '成功查询数': f"{full_result.get('successful_queries', 0)}/{full_result.get('total_queries', 0)}",
                    '平均执行时间(s)': f"{full_result.get('avg_execution_time', 0):.4f}",
                    '最小执行时间(s)': f"{full_result.get('min_execution_time', 0):.4f}",
                    '最大执行时间(s)': f"{full_result.get('max_execution_time', 0):.4f}",
                    '时间标准差(s)': f"{full_result.get('std_execution_time', 0):.4f}",
                    '平均结果数': f"{full_result.get('avg_result_count', 0):.1f}"
                })
            
            # 子集的结果
            if subset_result:
                table_data.append({
                    '查询类型': query_type.replace('_', ' ').title(),
                    '表类型': f'子集({self.subset_size})',
                    '成功查询数': f"{subset_result.get('successful_queries', 0)}/{subset_result.get('total_queries', 0)}",
                    '平均执行时间(s)': f"{subset_result.get('avg_execution_time', 0):.4f}",
                    '最小执行时间(s)': f"{subset_result.get('min_execution_time', 0):.4f}",
                    '最大执行时间(s)': f"{subset_result.get('max_execution_time', 0):.4f}",
                    '时间标准差(s)': f"{subset_result.get('std_execution_time', 0):.4f}",
                    '平均结果数': f"{subset_result.get('avg_result_count', 0):.1f}"
                })
            
            # 计算性能提升
            if full_result and subset_result:
                full_avg = full_result.get('avg_execution_time', 0)
                subset_avg = subset_result.get('avg_execution_time', 0)
                if subset_avg > 0:
                    speedup = full_avg / subset_avg
                    table_data.append({
                        '查询类型': query_type.replace('_', ' ').title(),
                        '表类型': '性能提升',
                        '成功查询数': '-',
                        '平均执行时间(s)': f"{speedup:.2f}x",
                        '最小执行时间(s)': '-',
                        '最大执行时间(s)': '-',
                        '时间标准差(s)': '-',
                        '平均结果数': '-'
                    })
        
        return pd.DataFrame(table_data)
    
    def print_comparison_table(self):
        """打印性能对比表格"""
        df = self.generate_comparison_table()
        if df.empty:
            logger.warning("无法生成对比表格")
            return
        
        print("\n" + "="*120)
        print("SQL查询性能对比表格")
        print("="*120)
        print(df.to_string(index=False))
        print("="*120)
    
    def save_results(self, output_file: str):
        """保存测试结果到文件"""
        try:
            # 创建输出目录
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            
            # 准备保存的数据
            save_data = {
                'test_timestamp': datetime.now().isoformat(),
                'database_config': {
                    'host': self.db_params.get('host'),
                    'port': self.db_params.get('port'),
                    'dbname': self.db_params.get('dbname')
                },
                'test_config': {
                    'subset_size': self.subset_size
                },
                'results': self.evaluation_results
            }
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"测试结果已保存到: {output_file}")
            
            # 保存表格到CSV
            df = self.generate_comparison_table()
            if not df.empty:
                csv_file = output_file.replace('.json', '_comparison.csv')
                df.to_csv(csv_file, index=False, encoding='utf-8')
                logger.info(f"对比表格已保存到: {csv_file}")
            
        except Exception as e:
            logger.error(f"保存结果失败: {e}")
    
    def run_full_evaluation(self, output_file: str = None) -> Dict[str, Any]:
        """运行完整的性能评估"""
        if output_file is None:
            output_file = f"results/sql_performance_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            # 获取表统计信息
            logger.info("获取表统计信息...")
            stats = self.get_table_stats()
            
            # 生成测试查询
            logger.info("生成测试查询...")
            single_queries = self.generate_single_attribute_queries(stats, 100)
            multi_queries = self.generate_multi_attribute_queries(stats, 100)
            mixed_queries = self.generate_mixed_attribute_queries(stats, 100)
            
            # 创建测试子集
            logger.info("创建测试子集...")
            subset_table = self.create_test_subset()
            
            # 运行性能测试
            all_results = {}
            
            # 1. 单一属性查询测试
            logger.info("=" * 60)
            logger.info("测试单一属性查询")
            logger.info("=" * 60)
            
            single_full = self.run_performance_test(single_queries, "单一属性查询", "parts")
            single_subset = self.run_performance_test(single_queries, "单一属性查询", subset_table)
            
            all_results['single_attribute'] = {
                'full_table': single_full,
                'subset_table': single_subset
            }
            
            # 2. 多属性组合查询测试
            logger.info("=" * 60)
            logger.info("测试多属性组合查询")
            logger.info("=" * 60)
            
            multi_full = self.run_performance_test(multi_queries, "多属性组合查询", "parts")
            multi_subset = self.run_performance_test(multi_queries, "多属性组合查询", subset_table)
            
            all_results['multi_attribute'] = {
                'full_table': multi_full,
                'subset_table': multi_subset
            }
            
            # 3. 混合属性查询测试
            logger.info("=" * 60)
            logger.info("测试混合属性查询")
            logger.info("=" * 60)
            
            mixed_full = self.run_performance_test(mixed_queries, "混合属性查询", "parts")
            mixed_subset = self.run_performance_test(mixed_queries, "混合属性查询", subset_table)
            
            all_results['mixed_attribute'] = {
                'full_table': mixed_full,
                'subset_table': mixed_subset
            }
            
            # 汇总结果
            self.evaluation_results = {
                'evaluation_info': {
                    'subset_size': self.subset_size,
                    'timestamp': datetime.now().isoformat(),
                    'database_config': {
                        'host': self.db_params.get('host'),
                        'port': self.db_params.get('port'),
                        'dbname': self.db_params.get('dbname')
                    }
                },
                'table_stats': stats,
                **all_results
            }
            
            # 保存结果
            self.save_results(output_file)
            
            # 打印对比表格
            self.print_comparison_table()
            
            return self.evaluation_results
            
        except Exception as e:
            logger.error(f"测试过程中发生错误: {e}")
            raise


def main():
    """主函数"""
    # 获取数据库配置
    db_params = Config.POSTGRES_CONFIG
    
    # 创建测试器实例
    tester = SQLQueryPerformanceTester(db_params, subset_size=100)
    
    try:
        # 连接数据库
        tester.connect()
        
        # 运行完整测试
        output_file = "results/sql_performance_test_results.json"
        tester.run_full_evaluation(output_file)
        
    finally:
        # 关闭数据库连接
        tester.close()


if __name__ == "__main__":
    main()
