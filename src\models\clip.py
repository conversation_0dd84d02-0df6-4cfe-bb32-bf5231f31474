import open_clip
import torch
import torch.nn as nn
import torchvision.transforms as T
from PIL import Image

class CLIPFeatureExtractor(nn.Module):
    def __init__(self, model_name: str = 'ViT-L-14', pretrained: str = 'laion2b_s32b_b82k', device=None):
        super().__init__()
        # 设置设备，如果未指定则自动选择
        self.device = device if device is not None else torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        self.model, self.preprocess = open_clip.create_model_from_pretrained(model_name=model_name, pretrained=pretrained)
        # 将模型移至指定设备
        self.model = self.model.to(self.device)
        self.model.eval()
        self.transform = T.Normalize(mean=(0.48145466, 0.4578275, 0.40821073), std=(0.26862954, 0.26130258, 0.27577711))
        self.tokenizer = open_clip.get_tokenizer(model_name)

    def forward(self, images, normalize=True):
        # 确保输入图像在正确的设备上
        images = images.to(self.device)
        with torch.inference_mode():
            image_features = self.model.encode_image(self.transform(images))
            if normalize:
                # L2归一化特征向量
                image_features = image_features / image_features.norm(dim=1, keepdim=True)
        return image_features
    

if __name__ == '__main__':
    clip_feature_extractor = CLIPFeatureExtractor()
    print(f"使用设备: {clip_feature_extractor.device}")
    
    image = clip_feature_extractor.preprocess(Image.open(r"E:\project\cad-rag\datasets\fusion360_assembly\7780_6c885e81\assembly.png")).unsqueeze(0)
    # 不需要在这里将图像移至设备，因为forward方法会处理
    image_features = clip_feature_extractor(image, normalize=True)
    print(f"特征形状: {image_features.shape}")
    print(f"特征范数: {torch.norm(image_features[0])}")  # 检查是否已归一化，应接近1.0 