# LLM配置和使用指南

## 概述

本系统使用`MultiProviderLLM`类来统一管理不同的LLM提供商，支持OpenAI、Azure OpenAI、Ollama、Gemini等多种服务。系统采用智能的处理策略：

- **图片输入**: 直接进行`shape_embedding`相似性检索，不使用LLM
- **文字输入**: 使用`MultiProviderLLM.chat`方法进行智能查询分解

## LLM配置方式

### 1. 环境变量配置（推荐）

```bash
# 基础配置
export LLM_PROVIDER=openai
export LLM_API_URL=https://api.openai.com
export LLM_API_KEY=your-api-key
export LLM_MODEL=gpt-3.5-turbo

# 可选配置
export LLM_TEMPERATURE=0.1
export LLM_TIMEOUT=60
```

### 2. 支持的LLM提供商

#### OpenAI
```bash
export LLM_PROVIDER=openai
export LLM_API_URL=https://api.openai.com
export LLM_API_KEY=sk-your-openai-api-key
export LLM_MODEL=gpt-3.5-turbo
```

#### Azure OpenAI
```bash
export LLM_PROVIDER=azure
export LLM_API_URL=https://your-resource.openai.azure.com
export LLM_API_KEY=your-azure-api-key
export LLM_MODEL=gpt-35-turbo
export AZURE_DEPLOYMENT=your-deployment-name
export AZURE_API_VERSION=2024-02-15-preview
```

#### Ollama (本地部署)
```bash
export LLM_PROVIDER=ollama
export LLM_API_URL=http://localhost:11434
export LLM_API_KEY=not-required
export LLM_MODEL=llama2
```

#### Google Gemini
```bash
export LLM_PROVIDER=gemini
export LLM_API_URL=https://generativelanguage.googleapis.com
export LLM_API_KEY=your-gemini-api-key
export LLM_MODEL=gemini-pro
```

## 系统处理策略

### 图片输入处理

```python
# 图片输入时的处理流程
image_query = MultimodalQuery(
    image_data=shape_embedding_vector,  # 已提取的形状特征向量
    query_intent="基于图片查找相似形状"
)

# 系统处理:
# 1. 检测到图片输入
# 2. 直接跳过LLM处理
# 3. 使用shape_embedding进行向量相似性搜索
# 4. 返回形状相似的结果
```

**关键点**:
- 图片数据应该是已经提取好的`shape_embedding`向量
- 不需要LLM处理图片内容
- 直接在Neo4j中进行向量相似性搜索

### 文字输入处理

```python
# 文字输入时的处理流程
text_query = MultimodalQuery(
    text_query="查找铝合金材料的发动机零件",
    query_intent="基于文本的智能查询"
)

# 系统处理:
# 1. 检测到文字输入
# 2. 使用LLM.chat方法进行查询分解
# 3. 识别查询类型和属性
# 4. 生成相应的Cypher查询
# 5. 返回查询结果
```

**LLM使用方式**:
```python
# 编排智能体中的LLM调用
messages = [
    {"role": "system", "content": "你是一个CAD/机械设计领域的查询分解专家。"},
    {"role": "user", "content": prompt}
]

response = self.llm.chat(messages, temperature=0.1)
```

## 回退机制

系统具有完善的回退机制，确保在LLM不可用时仍能正常工作：

### 1. LLM初始化失败
```python
try:
    self.llm = get_default_llm()
except Exception as e:
    logging.warning(f"LLM初始化失败: {e}，将使用规则分解")
    self.llm = None
```

### 2. LLM查询失败
```python
if self.llm is not None:
    try:
        # 尝试使用LLM分解
        response = self.llm.chat(messages, temperature=0.1)
        # 处理响应...
    except Exception as e:
        logging.warning(f"LLM查询分解失败，使用规则分解: {e}")

# 回退到基于规则的分解
return self._rule_based_decomposition(multimodal_query)
```

### 3. 规则分解策略
当LLM不可用时，系统使用基于关键词的规则分解：

- **材料关键词**: 铝、钢、塑料、钛等 → `metadata`查询
- **结构关键词**: 包含、组成、结构等 → `structure`查询  
- **语义关键词**: 相似、功能、用途等 → `vector_similarity`查询

## 文本嵌入配置

对于`description_embedding`的语义相似性搜索，需要配置文本嵌入服务：

### 方案1: Sentence Transformers（推荐）
```python
from sentence_transformers import SentenceTransformer

model = SentenceTransformer('sentence-transformers/all-MiniLM-L6-v2')
embedding = model.encode(text)
```

### 方案2: OpenAI Embedding API
```python
import openai

response = openai.Embedding.create(
    input=text, 
    model="text-embedding-ada-002"
)
embedding = response['data'][0]['embedding']
```

### 方案3: 本地嵌入模型
```python
# 使用本地部署的嵌入模型
# 例如通过Ollama或其他本地服务
```

## 性能优化建议

### 1. LLM调用优化
- 设置合适的`temperature`参数（推荐0.1）
- 使用较快的模型（如gpt-3.5-turbo）
- 设置合理的超时时间

### 2. 缓存策略
```python
# 可以实现查询分解结果的缓存
cache_key = hash(multimodal_query.text_query)
if cache_key in decomposition_cache:
    return decomposition_cache[cache_key]
```

### 3. 批量处理
对于大量查询，可以考虑批量调用LLM API以提高效率。

## 故障排除

### 常见问题

1. **LLM API密钥错误**
   ```
   错误: Authentication failed
   解决: 检查LLM_API_KEY环境变量
   ```

2. **网络连接问题**
   ```
   错误: Connection timeout
   解决: 检查LLM_API_URL和网络连接
   ```

3. **模型不存在**
   ```
   错误: Model not found
   解决: 检查LLM_MODEL配置是否正确
   ```

### 调试模式

启用详细日志来调试LLM相关问题：

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# 系统将输出详细的LLM调用信息
```

## 部署建议

### 生产环境
- 使用稳定的LLM服务（如OpenAI、Azure OpenAI）
- 配置API密钥管理和轮换
- 实现请求限流和重试机制
- 监控LLM调用成功率和延迟

### 开发环境
- 可以使用本地Ollama进行开发测试
- 实现LLM mock服务用于单元测试
- 使用较小的模型以降低成本

这个配置指南确保了系统能够正确使用`MultiProviderLLM.chat`方法，并且在图片输入时直接进行形状相似性检索，不依赖LLM处理图片内容。
