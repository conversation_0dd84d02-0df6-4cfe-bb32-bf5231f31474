#!/usr/bin/env python3
"""
测试智能体使用ID列表占位符的功能
"""

import asyncio
import os
import sys
import logging

# 添加项目根目录到系统路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.agents.attribute_filtering_agent import AttributeFilteringAgent
from src.agents.structural_topology_agent import StructuralTopologyAgent

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


async def test_structured_data_agent():
    """测试结构化数据智能体的ID列表占位符功能"""
    print("\n=== 测试结构化数据智能体的ID列表占位符功能 ===")
    print("注意：在PostgreSQL数据库中，ID字段名为'uuid'")
    
    agent = AttributeFilteringAgent()
    await agent.connect()
    
    # 测试查询文本
    query_text = "查找质量大于1kg的零件"
    
    # 测试ID列表 - 这些ID将被用作uuid字段的过滤条件
    id_list = ["part_001", "part_002", "part_003", "part_004", "part_005"]
    
    print(f"查询文本: {query_text}")
    print(f"UUID列表: {id_list}")
    
    # 使用ID列表生成SQL
    sql_with_ids = await agent.text_to_sql(query_text, id_list)
    print(f"\n生成的SQL (带UUID列表):\n{sql_with_ids}")
    
    # 不使用ID列表生成SQL
    sql_without_ids = await agent.text_to_sql(query_text)
    print(f"\n生成的SQL (不带UUID列表):\n{sql_without_ids}")
    
    await agent.disconnect()


async def test_structural_relationship_agent():
    """测试结构拓扑智能体的ID列表占位符功能"""
    print("\n=== 测试结构拓扑智能体的ID列表占位符功能 ===")
    print("注意：在Neo4j图数据库中，ID字段名为'uuid'")
    
    agent = StructuralTopologyAgent()
    await agent.connect()
    
    # 测试查询文本
    query_text = "查找包含螺栓的装配体"
    
    # 测试ID列表 - 这些ID将被用作uuid字段的过滤条件
    id_list = ["assembly_001", "assembly_002", "assembly_003"]
    
    print(f"查询文本: {query_text}")
    print(f"UUID列表: {id_list}")
    
    # 使用ID列表生成Cypher
    cypher_with_ids = await agent.text_to_cypher(query_text, id_list)
    print(f"\n生成的Cypher (带UUID列表):\n{cypher_with_ids}")
    
    # 不使用ID列表生成Cypher
    cypher_without_ids = await agent.text_to_cypher(query_text)
    print(f"\n生成的Cypher (不带UUID列表):\n{cypher_without_ids}")
    
    await agent.disconnect()


async def main():
    """主函数"""
    print("测试智能体使用ID列表占位符的功能")
    print("=" * 50)
    print("重要说明：在所有数据库中，ID字段统一命名为'uuid'")
    
    try:
        await test_structured_data_agent()
        await test_structural_relationship_agent()
    except Exception as e:
        logger.error(f"测试过程中出错: {e}")
    
    print("\n测试完成")


if __name__ == "__main__":
    asyncio.run(main()) 