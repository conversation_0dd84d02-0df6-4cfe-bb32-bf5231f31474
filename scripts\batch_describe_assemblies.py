import os
import sys
import json
from tqdm import tqdm
import threading
import queue

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.extractors.fusion360_extractor import Fusion360Extractor
from src.utils.llm_utils import call_llm, call_mllm
from src.utils.file_utils import get_fusion360_test_paths
from src.models.LLM import MultiProviderLLM
from src.config import Config
from src.utils.model_name_cleaner import clean_model_name

mllm_system_prompt = """
    你是一名兼具机械设计与技术写作背景的资深工程师。请根据提供的装配渲染图和结构化信息，为非专业读者生成一段准确、通俗、连贯的中文文字说明，突出整体外形、主要组成、功能用途和材料特色。"
"""

mllm_system_prompt_en = """
    You are a senior engineer with expertise in mechanical design and technical writing. Based on the provided assembly rendering and structured information, generate an accurate, accessible, and coherent English description for non-professional readers, highlighting overall shape, main components, functional uses, and material characteristics.
"""

# 初始化大模型实例（支持多供应商）
llm1 = MultiProviderLLM(
    provider="gemini",
    api_url="https://generativelanguage.googleapis.com",
    api_key="AIzaSyBnj3qTOY-ggSKTHrxm5c8SNauBKeX9llU",
    model="gemini-2.5-flash",
)

# 初始化大模型实例（支持多供应商）
llm2 = MultiProviderLLM(
    provider="gemini",
    api_url="https://generativelanguage.googleapis.com",
    api_key="AIzaSyAMwYuTPdcnVklJk9T3EY2wGSxV12WKFNw",
    model="gemini-2.5-flash",
)

# 初始化大模型实例（支持多供应商）
llm3 = MultiProviderLLM(
    provider="gemini",
    api_url="https://generativelanguage.googleapis.com",
    api_key="AIzaSyDjMk2PlUwbhZfoQDZ9NMhH6X4zsZNiak8",
    model="gemini-2.5-flash",
)

# 初始化大模型实例（支持多供应商）
llm4 = MultiProviderLLM(
    provider="gemini",
    api_url="https://generativelanguage.googleapis.com",
    api_key="AIzaSyCjxH_RHwOuziGG7qIpLXY6cre4dRTadwc",
    model="gemini-2.5-flash",
)

def describe_assembly(json_path: str, image_path: str, lang: str = "zh", llm=None):
    """
    通过cad_model.to_json()的json信息和渲染图片，得到装配体的文本描述
    
    Args:
        json_path: assembly.json文件路径
        image_path: assembly.png文件路径
        lang: 语言类型，"zh"为中文，"en"为英文
        llm: 使用的LLM实例，如果为None则使用llm1
    """
    if llm is None:
        llm = llm1
        
    extractor = Fusion360Extractor(json_path, extract_shape_embedding=False)
    cad_model = extractor.convert()
    cad_model_json = cad_model.to_json()

    if lang == "zh":
        system_prompt = mllm_system_prompt
        mllm_prompt = f"""
**图片**：<IMAGE>
**装配体结构化信息**：
{cad_model_json}

**命名清洗规则**
- 忽略"Untitled""Component+数字""Body+数字"等无意义名称。
- 必要时基于图像与上下文推断更常见的称谓（如"机身""轮组"等）。

**输出格式要求**
请以如下JSON格式输出：
```
{{
  "assembly_name": "（推断出的装配体名称，简洁明了）",
  "description": "（整体概述，2-3句，突出用途和外观）",
  "components": [
    {{"name": "部件名称", "feature": "简要特征/作用"}},
    ...
  ],
  "materials and techniques": "（如可判断，简要描述主要材料和工艺亮点）"
}}
```
注意：所有字段均需填写，components为部件列表，feature为该部件的简要说明。
风格：简洁专业但易懂，避免长复句，适当使用空间位置词。
"""
    else:  # English
        system_prompt = mllm_system_prompt_en
        mllm_prompt = f"""
**Image**: <IMAGE>
**Assembly Structured Information**:
{cad_model_json}

**Naming Cleaning Rules**
- Ignore meaningless names like "Untitled", "Component+numbers", "Body+numbers".
- Infer more common terms based on image and context when necessary (e.g., "body", "wheel assembly", etc.).

**Output Format Requirements**
Please output in the following JSON format:
```
{{
  "assembly_name": "(Inferred assembly name, concise and clear)",
  "description": "(Overall overview, 2-3 sentences, highlighting purpose and appearance)",
  "components": [
    {{"name": "component name", "feature": "brief characteristics/function"}},
    ...
  ],
  "materials and techniques": "(Brief description of main materials and process highlights if determinable)"
}}
```
Note: All fields must be filled. Components is a list of parts, and feature is a brief description of that part.
Style: Concise and professional but easy to understand, avoid long complex sentences, use spatial position words appropriately.
"""

    # 合并 system prompt 到 user prompt
    full_prompt = system_prompt.strip() + "\n" + mllm_prompt.strip()
    messages = [
        {"role": "user", "content": full_prompt}
    ]

    # 使用 MultiProviderLLM 进行多模态对话
    description = llm.chat_with_images(messages=messages, image_paths=[image_path])
    return description


def parse_description_response(description, lang: str = "zh"):
    """
    解析AI返回的描述响应
    
    Args:
        description: AI返回的描述
        lang: 语言类型
    
    Returns:
        dict: 解析后的描述数据
    """
    try:
        # 如果返回的是JSON字符串，尝试解析
        if isinstance(description, str):
            # 提取JSON部分（如果有```json标记）
            if "```json" in description:
                start = description.find("```json") + 7
                end = description.find("```", start)
                json_str = description[start:end].strip()
                description_data = json.loads(json_str)
            elif description.strip().startswith("{"):
                description_data = json.loads(description)
            else:
                # 如果不是JSON格式，包装成标准格式
                if lang == "zh":
                    description_data = {
                        "assembly_name": "未知装配体",
                        "description": description,
                        "components": [],
                        "materials and techniques": "未指定"
                    }
                else:
                    description_data = {
                        "assembly_name": "Unknown Assembly",
                        "description": description,
                        "components": [],
                        "materials and techniques": "Not specified"
                    }
        else:
            description_data = description
    except json.JSONDecodeError:
        # 如果解析失败，包装成标准格式
        if lang == "zh":
            description_data = {
                "assembly_name": "未知装配体",
                "description": str(description),
                "components": [],
                "materials and techniques": "未指定"
            }
        else:
            description_data = {
                "assembly_name": "Unknown Assembly",
                "description": str(description),
                "components": [],
                "materials and techniques": "Not specified"
            }
    
    return description_data


def process_description_data(description_data: dict, lang: str = "zh"):
    """
    对描述数据中的名称进行清洗处理
    
    Args:
        description_data: 描述数据字典
        lang: 语言类型
    """
    # 清洗装配体名称
    if "assembly_name" in description_data:
        description_data["assembly_name"] = clean_model_name(description_data["assembly_name"])
    
    # 清洗组件名称
    if "components" in description_data and isinstance(description_data["components"], list):
        for component in description_data["components"]:
            if isinstance(component, dict) and "name" in component:
                component["name"] = clean_model_name(component["name"])
    
    return description_data


def worker(task_queue, results, results_lock, llm_instance):
    """
    工作线程函数，处理队列中的任务
    
    Args:
        task_queue: 任务队列
        results: 结果字典
        results_lock: 结果字典的锁
        llm_instance: 使用的LLM实例
    """
    while not task_queue.empty():
        try:
            task = task_queue.get(block=False)
            test_path = task["test_path"]
            json_path = task["json_path"]
            image_path = task["image_path"]
            description_path = task["description_path"]
            description_en_path = task["description_en_path"]
            skip_zh = task["skip_zh"]
            skip_en = task["skip_en"]
            
            print(f"线程 {threading.current_thread().name} 正在处理: {test_path}")
            
            task_result = {
                "status": "success",
                "output_files": {
                    "zh": description_path if not skip_zh else "skipped",
                    "en": description_en_path if not skip_en else "skipped"
                }
            }
            
            try:
                # 处理中文描述
                if not skip_zh:
                    description = describe_assembly(json_path, image_path, lang="zh", llm=llm_instance)
                    description_data = parse_description_response(description, lang="zh")
                    description_data = process_description_data(description_data, lang="zh")
                    
                    # 添加元数据
                    description_data["metadata"] = {
                        "source_json": json_path,
                        "source_image": image_path,
                        "generated_at": __import__('datetime').datetime.now().isoformat(),
                        "language": "zh"
                    }
                    
                    # 保存到description.json
                    with open(description_path, 'w', encoding='utf-8') as f:
                        json.dump(description_data, f, ensure_ascii=False, indent=2)
                    
                    print(f"线程 {threading.current_thread().name} 成功生成中文描述: {description_path}")
                
                # 处理英文描述
                if not skip_en:
                    description_en = describe_assembly(json_path, image_path, lang="en", llm=llm_instance)
                    description_en_data = parse_description_response(description_en, lang="en")
                    description_en_data = process_description_data(description_en_data, lang="en")
                    
                    # 添加元数据
                    description_en_data["metadata"] = {
                        "source_json": json_path,
                        "source_image": image_path,
                        "generated_at": __import__('datetime').datetime.now().isoformat(),
                        "language": "en"
                    }
                    
                    # 保存到description_en.json
                    with open(description_en_path, 'w', encoding='utf-8') as f:
                        json.dump(description_en_data, f, ensure_ascii=False, indent=2)
                    
                    print(f"线程 {threading.current_thread().name} 成功生成英文描述: {description_en_path}")
                
                with results_lock:
                    results["details"][test_path] = task_result
                    results["success"] += 1
                    results["processed"] += 1
                    
            except Exception as e:
                error_msg = f"线程 {threading.current_thread().name} 处理失败 {test_path}: {str(e)}"
                print(error_msg)
                with results_lock:
                    results["details"][test_path] = {"status": "failed", "error": str(e)}
                    results["failed"] += 1
                    
        except queue.Empty:
            break
        finally:
            task_queue.task_done()

def batch_describe_assemblies():
    """
    批量处理所有测试集中的装配体，使用四个线程并行处理，生成描述并保存到description.json
    """
    # 获取所有测试路径
    test_paths = get_fusion360_test_paths()
    print(f"发现 {len(test_paths)} 个测试集文件夹")
    
    # 统计结果
    results = {
        "total": len(test_paths),
        "processed": 0,
        "success": 0,
        "failed": 0,
        "details": {}
    }
    
    # 创建任务队列
    task_queue = queue.Queue()
    
    # 填充任务队列
    for test_path in test_paths:
        # 构建文件路径
        json_path = os.path.join(test_path, "assembly.json")
        image_path = os.path.join(test_path, "assembly.png")
        description_path = os.path.join(test_path, "description.json")
        description_en_path = os.path.join(test_path, "description_en.json")
        
        # 检查必要文件是否存在
        if not os.path.exists(json_path):
            print(f"警告: assembly.json不存在于路径: {test_path}")
            results["details"][test_path] = {"status": "failed", "error": "assembly.json不存在"}
            results["failed"] += 1
            continue
            
        if not os.path.exists(image_path):
            print(f"警告: assembly.png不存在于路径: {test_path}")
            results["details"][test_path] = {"status": "failed", "error": "assembly.png不存在"}
            results["failed"] += 1
            continue
        
        # 如果description.json已存在，跳过处理
        skip_zh = os.path.exists(description_path)
        skip_en = os.path.exists(description_en_path)
        
        if skip_zh and skip_en:
            print(f"跳过（已存在）: {description_path} 和 {description_en_path}")
            results["details"][test_path] = {"status": "skipped", "reason": "description.json和description_en.json已存在"}
            results["processed"] += 1
            continue
        
        # 添加任务到队列
        task_queue.put({
            "test_path": test_path,
            "json_path": json_path,
            "image_path": image_path,
            "description_path": description_path,
            "description_en_path": description_en_path,
            "skip_zh": skip_zh,
            "skip_en": skip_en
        })
    
    # 创建结果字典的锁
    results_lock = threading.Lock()
    
    # 创建并启动工作线程
    threads = []
    llm_instances = [llm1, llm2, llm3, llm4]
    
    for i, llm_instance in enumerate(llm_instances):
        thread = threading.Thread(
            target=worker, 
            args=(task_queue, results, results_lock, llm_instance),
            name=f"LLM-Thread-{i+1}"
        )
        threads.append(thread)
        thread.start()
        print(f"启动线程 {thread.name}")
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    # 输出最终统计结果
    print("\n" + "="*50)
    print("批量处理完成！")
    print(f"总计: {results['total']} 个文件夹")
    print(f"已处理: {results['processed']} 个")
    print(f"成功: {results['success']} 个")
    print(f"失败: {results['failed']} 个")
    print(f"跳过: {results['total'] - results['processed'] - results['failed']} 个")
    
    # 保存详细结果到日志文件
    log_path = "batch_describe_results.json"
    with open(log_path, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    print(f"详细结果已保存到: {log_path}")
    
    return results


if __name__ == "__main__":
    # 运行批量处理
    results = batch_describe_assemblies()
