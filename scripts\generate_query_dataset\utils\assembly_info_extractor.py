#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
装配体信息提取器

用于从数据库中提取装配体的完整信息，包括属性、结构和语义信息
"""

import sys
import os
import logging
from typing import Dict, List, Any, Optional

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

import psycopg2
from psycopg2.extras import RealDictCursor
from neo4j import GraphDatabase
from src.config import Config

logger = logging.getLogger(__name__)


class AssemblyInfoExtractor:
    """装配体信息提取器"""
    
    def __init__(self):
        """初始化装配体信息提取器"""
        self.postgres_config = Config.POSTGRES_CONFIG
        self.neo4j_driver = GraphDatabase.driver(
            Config.NEO4J_URI,
            auth=(Config.NEO4J_USER, Config.NEO4J_PASSWORD)
        )
    
    def extract_full_info(self, assembly: Dict[str, Any]) -> Dict[str, Any]:
        """
        提取装配体的完整信息
        
        Args:
            assembly: 基本装配体信息
            
        Returns:
            完整的装配体信息
        """
        assembly_info = assembly.copy()
        
        # 提取结构信息
        structure_info = self._extract_structure_info(assembly['uuid'])
        assembly_info['structure'] = structure_info
        
        # 提取零件信息
        parts_info = self._extract_parts_info(assembly['uuid'])
        assembly_info['parts'] = parts_info
        
        return assembly_info
    
    def _extract_structure_info(self, assembly_uuid: str) -> Dict[str, Any]:
        """
        从Neo4j中提取装配体的结构信息
        
        Args:
            assembly_uuid: 装配体UUID
            
        Returns:
            结构信息字典
        """
        try:
            with self.neo4j_driver.session() as session:
                # 获取装配体包含的所有零件和子装配体
                query = """
                MATCH (a:Assembly {uuid: $uuid})-[:hasComponent*]->(node)
                WHERE node:Part OR node:SubAssembly
                RETURN 
                    labels(node) as node_type,
                    node.name as name,
                    node.uuid as uuid
                ORDER BY name
                """
                
                result = session.run(query, uuid=assembly_uuid)
                
                components = []
                component_names = []
                component_counts = {}
                
                for record in result:
                    node_type = record['node_type'][0]  # 获取第一个标签
                    name = record['name']
                    uuid = record['uuid']
                    
                    components.append({
                        'type': node_type,
                        'name': name,
                        'uuid': uuid
                    })
                    
                    if name:
                        component_names.append(name)
                        component_counts[name] = component_counts.get(name, 0) + 1
                
                # 获取零件材质统计
                material_query = """
                MATCH (a:Assembly {uuid: $uuid})-[:hasComponent*]->(p:Part)
                WHERE p.material IS NOT NULL
                RETURN p.material as material, COUNT(*) as count
                ORDER BY count DESC
                """
                
                material_result = session.run(material_query, uuid=assembly_uuid)
                materials = {record['material']: record['count'] for record in material_result}
                
                return {
                    'components': components,
                    'component_names': list(set(component_names)),
                    'component_counts': component_counts,
                    'materials': materials,
                    'total_components': len(components)
                }
                
        except Exception as e:
            logger.error(f"从Neo4j提取结构信息失败: {e}")
            return {
                'components': [],
                'component_names': [],
                'component_counts': {},
                'materials': {},
                'total_components': 0
            }
    
    def _extract_parts_info(self, assembly_uuid: str) -> List[Dict[str, Any]]:
        """
        从PostgreSQL中提取装配体的零件信息
        
        Args:
            assembly_uuid: 装配体UUID
            
        Returns:
            零件信息列表
        """
        try:
            conn = psycopg2.connect(**self.postgres_config)
            
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                query = """
                SELECT 
                    uuid,
                    name,
                    material,
                    description,
                    length,
                    width,
                    height,
                    area,
                    volume,
                    density,
                    mass,
                    hole_count,
                    hole_diameter_mean,
                    hole_diameter_std,
                    hole_depth_mean,
                    hole_depth_std
                FROM parts
                WHERE top_level_assembly_id = %s
                ORDER BY name
                """
                
                cursor.execute(query, (assembly_uuid,))
                results = cursor.fetchall()
                
                parts = [dict(row) for row in results]
                
                return parts
                
        except Exception as e:
            logger.error(f"从PostgreSQL提取零件信息失败: {e}")
            return []
        finally:
            if 'conn' in locals():
                conn.close()
    
    def __del__(self):
        """析构函数，关闭Neo4j连接"""
        if hasattr(self, 'neo4j_driver'):
            self.neo4j_driver.close()
