#!/usr/bin/env python3
"""
查询规划智能体
负责将用户的自然、模糊且多模态的查询分解为结构化的查询计划
"""

import json
import uuid
import time
from typing import List, Dict, Any, Optional

from ..models.LLM import MultiProviderLLM
from .data_models import (
    BaseTask, QueryResult, SearchResultItem,
    QueryPlan, QueryPlannerTask,
    UnifiedStructuredDataTask,
    UnifiedStructuralQueryTask, 
    UnifiedGeometrySemanticTask
)


class QueryPlannerAgent:
    """
    查询规划智能体 - 将用户查询分解为结构化的查询计划
    
    主要功能:
    1. 分析用户的自然语言查询
    2. 生成包含多个子查询的执行计划
    3. 指定子查询间的融合策略
    
    重要特性:
    - 生成的子查询将被并行执行以提高性能
    - 支持三种融合策略: INTERSECT(交集), UNION(并集), WEIGHTED(加权)
    - 每个子查询都针对特定的智能体和数据源
    """
    
    def __init__(self, llm: Optional[MultiProviderLLM] = None):
        """初始化查询规划智能体"""
        self.llm = llm or MultiProviderLLM()
        
        # 智能体能力描述
        self.agent_capabilities = {
            "AttributeFilteringAgent": {
                "description": "属性筛选智能体，处理数据库表中的字段查询",
                "supported_fields": [
                    "物理属性: mass, volume, density, length, width, height, area",
                    "材料属性: material", 
                    "数值属性: hole_count, part_count",
                    "分类属性: industry, category"
                ],
                "task_type": "UnifiedStructuredDataTask"
            },
            "GeometrySemanticAgent": {
                "description": "几何和语义查询智能体，处理功能描述、外形描述等文本语义信息和形状特征向量",
                "capabilities": [
                    "文本语义搜索",
                    "形状相似性搜索", 
                    "混合搜索（文本+形状）"
                ],
                "task_type": "UnifiedGeometrySemanticTask"
            },
            "StructuralTopologyAgent": {
                "description": "结构拓扑智能体，处理组件包含关系、装配体层次结构、连接关系",
                "capabilities": [
                    "装配体层次结构查询",
                    "零件包含关系查询",
                    "连接关系查询"
                ],
                "task_type": "UnifiedStructuralQueryTask"
            }
        }
    
    def _get_query_planning_prompt(self, query_text: str, has_shape_vector: bool = False) -> str:
        """构建查询规划的提示词"""
        
        prompt = f"""You are a query planning agent responsible for decomposing user's natural, fuzzy, and multimodal queries into structured query plans.

User Query: {query_text}
{'User provided shape vector' if has_shape_vector else 'User did not provide shape vector'}

The system has three specialized agents:

1. **AttributeFilteringAgent** 
   - Handles database field queries
   - Supported fields: physical attributes (mass, volume, density, length, width, height, area), material attributes (material), numerical attributes (hole_count, part_count), classification attributes (industry, category)
   - Task type: UnifiedStructuredDataTask
   - **IMPORTANT**: When generating query_text for AttributeFilteringAgent, you MUST start with either "Find parts with" or "Find assemblies with" based on what the user is looking for

2. **GeometrySemanticAgent**  
   - Handles functional descriptions, shape descriptions, model names and other meaningful text semantic information and shape feature vectors
   - Semantic query requirements: must include specific functional descriptions, usage descriptions, appearance features and other meaningful text, do not use meaningless descriptions like "similar shape", "similar model"
   - Shape query: when user provides shape vector, can perform shape similarity search
   - Supports: text semantic search, shape similarity search, hybrid search
   - Task type: UnifiedGeometrySemanticTask

3. **StructuralTopologyAgent**
   - Handles component containment relationships, assembly hierarchies, connection relationships
   - Supports: assembly hierarchy queries, part containment relationship queries, connection relationship queries
   - Task type: UnifiedStructuralQueryTask
   - **IMPORTANT**: When generating query_text for StructuralTopologyAgent, you MUST start with either "Find parts" or "Find assemblies" based on what the user is looking for

Please analyze the user query intent and generate a structured query plan. The query plan should include:

1. **query_type**: A descriptive type indicating which agents are involved (e.g., "Hybrid:structural_topology + attribute_filtering", "Single:geometry_semantic", etc.)

2. **sub_queries**: List of sub-queries for each agent, each containing:
   - agent: Agent name to use (AttributeFilteringAgent, GeometrySemanticAgent, or StructuralTopologyAgent)
   - task_params: Task parameters containing query_text and other relevant parameters
   
   **Note: All sub-queries will be executed in parallel to improve performance.**

3. **fusion_strategy**: Strategy for combining results from multiple agents after parallel execution:
   - "INTERSECT": Returns only results that appear in ALL sub-queries (intersection)
   - "UNION": Combines all results from all sub-queries and removes duplicates
   - "WEIGHTED": Weighted fusion of results with equal weights for each sub-query

Please return the query plan in JSON format, ensuring correct JSON formatting.

**Important Rules:**
1. For GeometrySemanticAgent:
   - If user provided shape vector, prioritize shape search (can omit query_text or use simple functional description)
   - If no shape vector, only use semantic search when query contains specific functional descriptions, usage descriptions, appearance features
   - Do not use meaningless descriptions like "similar shape", "similar model", "similar appearance" as query_text
   - If query has no specific semantic information and no shape vector, can skip geometry_semantic step
   - **Must set reasonable top_k value** (usually between 5-50, adjust based on query requirements)

2. For AttributeFilteringAgent and StructuralTopologyAgent:
   - **Do not set top_k parameter in task_params**, let system return all matching results
   - Only need to set query_text parameter

3. **For AttributeFilteringAgent query_text generation**:
   - **MUST start with "Find parts with" or "Find assemblies with"**
   - How to determine parts vs assemblies:
     - If query mentions "assemblies", "assembly", "装配体", "组装件" → use "Find assemblies with"
     - If query mentions "parts", "part", "零件", "部件", "component" → use "Find parts with"  
     - If query mentions structural relationships (contains, includes, consists of) → usually "Find assemblies with"
     - If query is about individual components without assembly context → use "Find parts with"
     - When in doubt, prefer "Find assemblies with" for complex multi-component searches
   - Examples:
     - User: "mass < 0.5 kg AND volume > 150 cm³" → "Find parts with mass < 0.5 kg AND volume > 150 cm³"
     - User: "assemblies with high strength" → "Find assemblies with high strength"
     - User: "components containing steel bolts" → "Find assemblies with components containing steel bolts"

4. **For StructuralTopologyAgent query_text generation**:
   - **MUST start with "Find parts" or "Find assemblies"** (without "with")
   - Usually structural queries are about assemblies since they involve component relationships
   - Examples:
     - User: "contains gear and shaft" → "Find assemblies that contain gear and shaft"
     - User: "has multiple bolts" → "Find assemblies that have multiple bolts"

Notes:
- If user provided shape vector, do not include specific shape_vector value in GeometrySemanticAgent's task_params, system will handle automatically
- For geometry and semantic queries, can include query_text, top_k, use_reranker parameters
- For structured data queries, only include query_text parameter, do not set top_k (let system return all matching results)
- For structural relationship queries, only include query_text parameter, do not set top_k (let system return all matching results)

Example query plan format (sub-queries will be executed in parallel):
```json
{{
    "query_type": "Hybrid:structural_topology + attribute_filtering",
    "sub_queries": [
        {{
            "agent": "StructuralTopologyAgent",
            "task_params": {{
                "query_text": "Find assemblies that contain Miteebite T Nut and Miteebite Socket Head Cap Screw Ai."
            }}
        }},
        {{
            "agent": "AttributeFilteringAgent",
            "task_params": {{
                "query_text": "Find assemblies with mass less than 0.5 kg and volume greater than 30 cm³ and part count equal to 5."
            }}
        }}
    ],
    "fusion_strategy": "INTERSECT"
}}
```

Please strictly follow the above format to generate the query plan, return only JSON, do not include other text.
"""
        return prompt
    
    async def generate_query_plan(self, task: QueryPlannerTask) -> QueryPlan:
        """生成查询计划"""        
        # 构建提示词
        prompt = self._get_query_planning_prompt(
            task.query_text, 
            task.shape_vector is not None
        )
        
        # 调用LLM生成查询计划
        messages = [{"role": "user", "content": prompt}]
        
        try:
            # 在线程池中调用LLM，实现真正的并行
            import asyncio
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                self.llm.chat,
                messages
            )
            
            # 检查响应是否为空
            if not response or not response.strip():
                raise ValueError(f"LLM返回空响应")
            
            # 尝试清理响应中的非JSON内容
            response_clean = response.strip()
            if response_clean.startswith("```json"):
                response_clean = response_clean[7:]
            if response_clean.endswith("```"):
                response_clean = response_clean[:-3]
            response_clean = response_clean.strip()
            
            # 解析JSON响应
            plan_data = json.loads(response_clean)
            
            # 创建查询计划对象
            query_plan = QueryPlan(
                plan_id=str(uuid.uuid4()),
                query_text=task.query_text,
                query_type=plan_data["query_type"],
                sub_queries=plan_data["sub_queries"],
                fusion_strategy=plan_data["fusion_strategy"],
                execution_mode="parallel"  # 明确指定并行执行模式
            )
            
            return query_plan
            
        except json.JSONDecodeError as e:
            raise ValueError(f"LLM返回的JSON格式无效: {e}")
        except KeyError as e:
            raise ValueError(f"查询计划缺少必要字段: {e}")
        except Exception as e:
            raise RuntimeError(f"生成查询计划失败: {e}")
    
    async def execute_task(self, task: QueryPlannerTask) -> QueryResult:
        """执行查询规划任务"""
        start_time = time.time()
        
        try:
            # 生成查询计划
            query_plan = await self.generate_query_plan(task)
            
            # 创建成功结果
            execution_time = time.time() - start_time
            
            # 将查询计划转换为SearchResultItem格式以便与其他智能体保持一致
            result_item = SearchResultItem(
                rank=1,
                uuid=query_plan.plan_id,
                name=f"查询计划 - {task.query_text[:50]}...",
                description=f"生成了包含{len(query_plan.sub_queries)}个子查询的并行执行计划",
                search_type="query_planning",
                metadata={
                    "query_plan": query_plan.model_dump(),
                    "sub_query_count": len(query_plan.sub_queries),
                    "query_type": query_plan.query_type,
                    "fusion_strategy": query_plan.fusion_strategy,
                    "execution_mode": query_plan.execution_mode,
                    "parallel_execution": True
                }
            )
            
            return QueryResult(
                task_id=task.task_id,
                status='success',
                results=[result_item],
                execution_time=execution_time,
                total_results=1
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            return QueryResult(
                task_id=task.task_id,
                status='failure',
                error_message=str(e),
                execution_time=execution_time,
                total_results=0
            )
    
    def parse_query_plan_from_result(self, result: QueryResult) -> Optional[QueryPlan]:
        """从查询结果中解析查询计划"""
        if result.status != 'success' or not result.results:
            return None
        
        metadata = result.results[0].metadata
        if 'query_plan' not in metadata:
            return None
        
        plan_data = metadata['query_plan']
        return QueryPlan(**plan_data)
