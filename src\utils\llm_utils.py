import base64
from openai import OpenAI
from src.config import Config

# 全局客户端实例，避免重复创建
_client = None

def get_openai_client():
    """获取或创建OpenAI客户端"""
    global _client
    if _client is None:
        _client = OpenAI(
            api_key=Config.API_KEY,
            base_url=Config.API_URL
        )
    return _client


def call_mllm(prompt, system_prompt=None, image_path=None, model_name="gemma-3-27b"):
    """
    通用多模态大模型调用函数，支持图片和文本输入。
    :param prompt: 用户输入的文本
    :param system_prompt: 系统提示词（可选）
    :param image_path: 图片路径（可选）
    :param model_name: 模型名称
    :return: 模型回答字符串
    """
    client = get_openai_client()
    
    messages = []
    if system_prompt:
        messages.append({"role": "system", "content": system_prompt})
    
    # 如果有图片，构建多模态消息
    if image_path:
        with open(image_path, "rb") as f:
            img_b64 = base64.b64encode(f.read()).decode()
        
        content = [
            {"type": "text", "text": prompt},
            {
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/jpeg;base64,{img_b64}"
                }
            }
        ]
        messages.append({"role": "user", "content": content})
    else:
        messages.append({"role": "user", "content": prompt})

    response = client.chat.completions.create(
        model=model_name,
        messages=messages
    )
    
    return response.choices[0].message.content


def call_llm(prompt, system_prompt=None, model_name="gemma-3-27b"):
    """
    通用大语言模型调用函数，仅文本输入。
    :param prompt: 用户输入的文本
    :param system_prompt: 系统提示词（可选）
    :param model_name: 模型名称
    :return: 模型回答字符串
    """
    client = get_openai_client()
    
    messages = []
    if system_prompt:
        messages.append({"role": "system", "content": system_prompt})
    messages.append({"role": "user", "content": prompt})

    response = client.chat.completions.create(
        model=model_name,
        messages=messages
    )
    
    return response.choices[0].message.content

