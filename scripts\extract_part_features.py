#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
零件特征提取器使用示例

本脚本用于批量提取零件的多模态特征，包括结构化特征、形状特征和语义特征，并对特征进行对齐、PCA降维、归一化和加权融合等处理。

使用方法：
python scripts/extract_part_features.py [--output-dir 输出目录] [--part-ids 零件ID列表] [--max-parts 最大零件数] [--load-only] [--no-process] [--enable-fusion]

参数说明：
--output-dir    特征输出目录，默认为 dataset
--part-ids      指定要处理的零件ID列表
--max-parts     限制处理的最大零件数量
--load-only     仅加载和测试现有特征，不重新提取
--no-process    不进行特征处理（对齐、PCA、归一化、融合）
--enable-fusion 启用加权拼接融合特征，默认为False

输出：
- part_features.pkl      提取的零件特征（字典，包含多种特征），结构如下：
    {
        'part_ids': [零件ID列表],
        'structural': np.array,     # 结构化特征矩阵 (n_parts, n_features)
        'shape': np.array,          # 形状特征矩阵 (n_parts, n_features)
        'semantic': np.array,       # 语义特征矩阵 (n_parts, n_features)
        'fused_features': np.array, # 融合特征矩阵 (n_parts, n_features) - 可选
        'metadata': {               # 元数据信息
            'processing_enabled': bool,
            'fusion_enabled': bool,
            'feature_dimensions': {
                'structural': int,
                'shape': int,
                'semantic': int,
                'fused': int
            }
        }
    }

- part_feature_scalers.pkl    特征标准化器
- 日志输出特征统计和分析信息

依赖：
- src/part_feature_extractor.py
- numpy, argparse, logging

示例：
python scripts/extract_part_features.py --output-dir dataset --max-parts 100
python scripts/extract_part_features.py --no-process
python scripts/extract_part_features.py --enable-fusion --max-parts 50
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.extractors.part_feature_extractor import PartFeatureExtractor
import numpy as np
import argparse
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def extract_and_save_features(output_dir='dataset', part_ids=None, max_parts=None, process_features=True, enable_fusion=False):
    """
    提取并保存零件特征
    
    Args:
        output_dir: 输出目录
        part_ids: 指定的零件ID列表
        max_parts: 最大零件数量限制
        process_features: 是否进行特征处理
        enable_fusion: 是否启用加权拼接融合特征
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建特征提取器
    extractor = PartFeatureExtractor()
    
    # 如果指定了最大零件数量，则限制part_ids
    if max_parts and part_ids and len(part_ids) > max_parts:
        part_ids = part_ids[:max_parts]
        logger.info(f"限制零件数量为: {max_parts}")
    
    # 提取所有特征（包括特征处理）
    logger.info("开始提取零件特征...")
    features = extractor.extract_all_features(
        part_ids=part_ids,
        include_shape_features=True,
        include_semantic_features=True,
        process_features=process_features,  # 根据参数决定是否进行特征处理
        enable_fusion=enable_fusion  # 根据参数决定是否启用加权拼接
    )
    
    if not features:
        logger.error("未能提取到任何特征")
        return
    
    # 添加元数据信息
    features['metadata'] = {
        'processing_enabled': process_features,
        'fusion_enabled': enable_fusion and process_features,
        'feature_dimensions': {}
    }
    
    # 记录各特征维度
    for feature_type in ['structural', 'shape', 'semantic', 'fused_features']:
        if feature_type in features and isinstance(features[feature_type], np.ndarray):
            features['metadata']['feature_dimensions'][feature_type] = features[feature_type].shape[1]
    
    # 打印特征统计信息
    logger.info("=== 特征提取统计 ===")
    total_parts = len(features.get('part_ids', []))
    logger.info(f"总零件数: {total_parts}")
    
    for feature_type, data in features.items():
        if isinstance(data, np.ndarray):
            logger.info(f"{feature_type}: {data.shape}")
        elif isinstance(data, list):
            logger.info(f"{feature_type}: {len(data)} items")
    
    # 保存特征
    features_path = os.path.join(output_dir, 'part_features.pkl')
    extractor.save_features(features, features_path)
    logger.info(f"特征已保存到: {features_path}")
    
    # 保存标准化器
    scalers_path = os.path.join(output_dir, 'part_feature_scalers.pkl')
    extractor.save_scalers(scalers_path)
    logger.info(f"标准化器已保存到: {scalers_path}")
    
    # 分析特征质量
    analyze_feature_quality(features)
    
    return features

def analyze_feature_quality(features):
    """分析特征质量"""
    logger.info("=== 特征质量分析 ===")
    
    # 分析结构化特征
    if 'structural' in features:
        structural = features['structural']
        logger.info(f"结构化特征统计:")
        logger.info(f"  维度: {structural.shape}")
        logger.info(f"  均值范围: [{np.min(np.mean(structural, axis=0)):.4f}, {np.max(np.mean(structural, axis=0)):.4f}]")
        logger.info(f"  标准差范围: [{np.min(np.std(structural, axis=0)):.4f}, {np.max(np.std(structural, axis=0)):.4f}]")
        logger.info(f"  缺失值: {np.sum(np.isnan(structural))}")
        logger.info(f"  L2范数范围: [{np.min(np.linalg.norm(structural, axis=1)):.4f}, {np.max(np.linalg.norm(structural, axis=1)):.4f}]")
    
    # 分析形状特征
    if 'shape' in features:
        shape = features['shape']
        logger.info(f"形状特征统计:")
        logger.info(f"  维度: {shape.shape}")
        logger.info(f"  均值范围: [{np.min(np.mean(shape, axis=0)):.4f}, {np.max(np.mean(shape, axis=0)):.4f}]")
        logger.info(f"  标准差范围: [{np.min(np.std(shape, axis=0)):.4f}, {np.max(np.std(shape, axis=0)):.4f}]")
        logger.info(f"  缺失值: {np.sum(np.isnan(shape))}")
        logger.info(f"  L2范数范围: [{np.min(np.linalg.norm(shape, axis=1)):.4f}, {np.max(np.linalg.norm(shape, axis=1)):.4f}]")
    
    # 分析语义特征
    if 'semantic' in features:
        semantic = features['semantic']
        logger.info(f"语义特征统计:")
        logger.info(f"  维度: {semantic.shape}")
        logger.info(f"  均值范围: [{np.min(np.mean(semantic, axis=0)):.4f}, {np.max(np.mean(semantic, axis=0)):.4f}]")
        logger.info(f"  标准差范围: [{np.min(np.std(semantic, axis=0)):.4f}, {np.max(np.std(semantic, axis=0)):.4f}]")
        logger.info(f"  缺失值: {np.sum(np.isnan(semantic))}")
        logger.info(f"  L2范数范围: [{np.min(np.linalg.norm(semantic, axis=1)):.4f}, {np.max(np.linalg.norm(semantic, axis=1)):.4f}]")
    
    # 分析融合特征
    if 'fused_features' in features:
        fused = features['fused_features']
        logger.info(f"融合特征统计:")
        logger.info(f"  维度: {fused.shape}")
        logger.info(f"  均值范围: [{np.min(np.mean(fused, axis=0)):.4f}, {np.max(np.mean(fused, axis=0)):.4f}]")
        logger.info(f"  标准差范围: [{np.min(np.std(fused, axis=0)):.4f}, {np.max(np.std(fused, axis=0)):.4f}]")
        logger.info(f"  缺失值: {np.sum(np.isnan(fused))}")
        logger.info(f"  L2范数范围: [{np.min(np.linalg.norm(fused, axis=1)):.4f}, {np.max(np.linalg.norm(fused, axis=1)):.4f}]")
    
    # 分析特征覆盖率
    analyze_feature_coverage(features)

def analyze_feature_coverage(features):
    """分析特征覆盖率"""
    logger.info("=== 特征覆盖率分析 ===")
    
    total_parts = len(features.get('part_ids', []))
    if total_parts == 0:
        logger.warning("没有零件数据")
        return
    
    # 结构化特征覆盖率
    if 'structural' in features:
        structural_count = features['structural'].shape[0]
        logger.info(f"结构化特征覆盖率: {structural_count}/{total_parts} ({structural_count/total_parts*100:.1f}%)")
    
    # 形状特征覆盖率（处理前）
    if 'shape' in features:
        shape_count = features['shape'].shape[0]
        logger.info(f"形状特征覆盖率: {shape_count}/{total_parts} ({shape_count/total_parts*100:.1f}%)")
    
    # 语义特征覆盖率（处理前）
    if 'semantic' in features:
        semantic_count = features['semantic'].shape[0]
        logger.info(f"语义特征覆盖率: {semantic_count}/{total_parts} ({semantic_count/total_parts*100:.1f}%)")
    
    # 融合特征覆盖率
    if 'fused_features' in features:
        fused_count = features['fused_features'].shape[0]
        logger.info(f"融合特征覆盖率: {fused_count}/{total_parts} ({fused_count/total_parts*100:.1f}%)")
    
    # 特征处理信息
    logger.info("=== 特征处理信息 ===")
    if features.get('metadata', {}).get('processing_enabled', False):
        logger.info("已启用特征处理：")
        logger.info("✓ 特征对齐（去除缺失特征的零件）")
        logger.info("✓ PCA降维（形状和语义特征降至64维）")
        logger.info("✓ L2归一化（所有特征类型）")
        if features.get('metadata', {}).get('fusion_enabled', False):
            logger.info("✓ 加权拼接（形状:0.3, 语义:0.3, 结构化:0.4）")
        else:
            logger.info("✗ 加权拼接（未启用）")
    else:
        logger.info("✗ 特征处理（未启用）")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='零件特征提取器使用示例')
    parser.add_argument('--output-dir', default='dataset', help='输出目录')
    parser.add_argument('--part-ids', nargs='+', help='指定零件ID列表')
    parser.add_argument('--max-parts', type=int, help='最大零件数量限制')
    parser.add_argument('--load-only', action='store_true', help='仅加载和测试现有特征')
    parser.add_argument('--no-process', action='store_true', help='不进行特征处理（对齐、PCA、归一化、融合）')
    parser.add_argument('--enable-fusion', action='store_true', help='启用加权拼接融合特征，默认为False')
    
    args = parser.parse_args()
    
    if args.load_only:
        # 仅加载和测试现有特征
        features_path = os.path.join(args.output_dir, 'part_features.pkl')
        scalers_path = os.path.join(args.output_dir, 'part_feature_scalers.pkl')
        
    else:
        # 提取并保存特征
        logger.info(f"特征处理{'禁用' if args.no_process else '启用'}")
        logger.info(f"加权拼接{'启用' if args.enable_fusion else '禁用'}")
        
        features = extract_and_save_features(
            output_dir=args.output_dir,
            part_ids=args.part_ids,
            max_parts=args.max_parts,
            process_features=not args.no_process,
            enable_fusion=args.enable_fusion
        )
        
        if features:
            logger.info("特征提取和保存完成")
        else:
            logger.error("特征提取失败")

if __name__ == "__main__":
    main()
