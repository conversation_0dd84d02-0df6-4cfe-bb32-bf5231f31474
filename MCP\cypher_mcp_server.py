#!/usr/bin/env python3
"""
Cypher MCP 服务器

基于 FastMCP 实现的 Neo4j Cypher 查询执行服务。
提供一个工具用于执行 Cypher 查询语句。
"""

import logging
import sys
import os
from typing import Any, Dict, List
from fastmcp import FastMCP
from pydantic import BaseModel, Field

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.knowledge_graph import CADKnowledgeGraph

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# 创建 FastMCP 应用
mcp = FastMCP("Cypher Executor")

# 全局知识图谱实例
kg_instance = CADKnowledgeGraph.from_config()

# def get_knowledge_graph() -> CADKnowledgeGraph:
#     """获取知识图谱实例"""
#     global kg_instance
#     if kg_instance is None:
#         kg_instance = CADKnowledgeGraph.from_config()
#     return kg_instance

class CypherRequest(BaseModel):
    """Cypher查询请求"""
    cypher: str = Field(description="要执行的Cypher查询语句")

@mcp.tool()
def execute_cypher(cypher: str = Field(description="要执行的Cypher查询语句")) -> Dict[str, Any]:
    """
    执行Cypher查询语句
        
    Returns:
        包含查询结果和状态信息的字典
    """
    try:
        kg = kg_instance
        results = kg.run_cypher(cypher)
        
        return {
            "success": True,
            "results": results,
            "count": len(results)
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "results": [],
            "count": 0
        }

if __name__ == "__main__":
    mcp.run(
        transport="sse",
        host="0.0.0.0",
        port=8003,
    )
