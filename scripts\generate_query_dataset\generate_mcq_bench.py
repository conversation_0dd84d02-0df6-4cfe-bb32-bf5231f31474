#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
MCQ-Bench Benchmark Dataset Generation Script

This script is used to generate a multimodal complex query benchmark dataset for real industrial application scenarios.
Randomly extract 100 assemblies from the database and generate for each assembly:
- 9 single-modal queries (3 attribute + 3 structure + 3 semantic)
- 4 multimodal queries (3 pairwise combinations + 1 three-category fusion)

Usage:
python scripts/generate_query_dataset/generate_mcq_bench.py

Output:
- data/benchmark/{assembly_id}/single_modal_query.json
- data/benchmark/{assembly_id}/Hybrid_query.json
"""

import sys
import os
import json
import random
import logging
import time
from pathlib import Path
from typing import Dict, List, Any, Optional
from tqdm import tqdm

# 添加项目根目录到系统路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.append(project_root)

from src.models.LLM import MultiProviderLLM

# 导入本地模块
try:
    from utils.data_sampler import DataSampler
    from utils.query_generator import QueryGenerator
    from utils.assembly_info_extractor import AssemblyInfoExtractor
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    utils_dir = os.path.join(current_dir, 'utils')
    sys.path.insert(0, utils_dir)
    from data_sampler import DataSampler
    from query_generator import QueryGenerator
    from assembly_info_extractor import AssemblyInfoExtractor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/mcq_bench_generation.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class MCQBenchGenerator:
    """MCQ-Bench Benchmark Dataset Generator"""
    
    def __init__(self, num_assemblies: int = 100):
        """
        Initialize generator
        
        Args:
            num_assemblies: Number of assemblies to extract
        """
        self.num_assemblies = num_assemblies
        self.output_dir = Path("data/benchmark")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize components
        self.llm = MultiProviderLLM()
        self.data_sampler = DataSampler()
        self.query_generator = QueryGenerator(self.llm)
        self.assembly_extractor = AssemblyInfoExtractor()
        
        # Statistics
        self.stats = {
            'total_assemblies': 0,
            'qualified_assemblies_count': 0,  # New: Total qualified assemblies count
            'single_modal_queries': 0,
            'hybrid_queries': 0,
            'failed_assemblies': [],
            'query_types': {
                'attribute': 0,
                'structure': 0,
                'semantic': 0,
                'hybrid_structure_attribute': 0,
                'hybrid_attribute_semantic': 0,
                'hybrid_structure_semantic': 0,
                'hybrid_all': 0
            },
            'filter_criteria': "Query Assembly nodes through Neo4j, filter assemblies where all child node names are not 'Component', 'Untitled', or 'Body'"  # New: Filter criteria description
        }
    
    def generate_benchmark(self):
        """Generate complete benchmark dataset"""
        logger.info("Starting MCQ-Bench benchmark dataset generation...")
        
        # 1. First count the number of qualified assemblies
        logger.info("Counting qualified assemblies...")
        qualified_uuids = self.data_sampler.get_qualified_assemblies()
        qualified_count = len(qualified_uuids)
        
        # Record in statistics
        self.stats['qualified_assemblies_count'] = qualified_count
        
        logger.info(f"=== Filter Criteria Statistics ===")
        logger.info(f"Filter Criteria: Query Assembly nodes through Neo4j, filter assemblies where all child node names are not 'Component', 'Untitled', or 'Body'")
        logger.info(f"Total qualified assemblies: {qualified_count}")
        
        if qualified_count == 0:
            logger.error("No qualified assemblies found, cannot generate benchmark dataset")
            return
        
        if qualified_count < self.num_assemblies:
            logger.warning(f"Qualified assemblies count ({qualified_count}) is less than target count ({self.num_assemblies})")
            logger.warning(f"Adjusting target count to: {qualified_count}")
            self.num_assemblies = qualified_count
        
        # 2. Randomly sample from qualified assemblies
        logger.info(f"Randomly sampling {self.num_assemblies} assemblies from {qualified_count} qualified assemblies...")
        assemblies = self.data_sampler.sample_assemblies(self.num_assemblies)
        
        if not assemblies:
            logger.error("Failed to retrieve assembly data from database")
            return
        
        logger.info(f"Successfully sampled {len(assemblies)} assemblies")
        
        # 3. Generate queries for each assembly
        for assembly in tqdm(assemblies, desc="Generating queries"):
            try:
                self._generate_queries_for_assembly(assembly)
                self.stats['total_assemblies'] += 1
            except Exception as e:
                logger.error(f"Failed to generate queries for assembly {assembly['uuid']}: {e}")
                self.stats['failed_assemblies'].append(assembly['uuid'])
        
        # 4. Generate statistics report
        self._generate_statistics()
        
        logger.info("MCQ-Bench benchmark dataset generation completed!")
    
    def _generate_queries_for_assembly(self, assembly: Dict[str, Any]):
        """Generate all queries for a single assembly"""
        assembly_id = assembly['uuid']
        
        # Create assembly folder
        assembly_dir = self.output_dir / assembly_id
        assembly_dir.mkdir(parents=True, exist_ok=True)
        
        # Check if queries already exist
        single_modal_file = assembly_dir / "single_modal_query.json"
        hybrid_file = assembly_dir / "Hybrid_query.json"
        
        if single_modal_file.exists() and hybrid_file.exists():
            logger.info(f"Assembly {assembly_id} already has query files, skipping generation")
            return
        
        # Extract complete assembly information
        logger.info(f"Extracting complete information for assembly {assembly_id}...")
        assembly_info = self.assembly_extractor.extract_full_info(assembly)
        
        # Generate single-modal queries
        logger.info(f"Generating single-modal queries for assembly {assembly_id}...")
        single_modal_queries = self._generate_single_modal_queries(assembly_info)
        
        # Generate multimodal queries
        logger.info(f"Generating multimodal queries for assembly {assembly_id}...")
        hybrid_queries = self._generate_hybrid_queries(assembly_info, single_modal_queries)
        
        # Save query files
        self._save_queries(assembly_dir, single_modal_queries, hybrid_queries)
        
        # Update statistics
        self.stats['single_modal_queries'] += len(single_modal_queries)
        self.stats['hybrid_queries'] += len(hybrid_queries)
    
    def _generate_single_modal_queries(self, assembly_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate single-modal queries"""
        queries = []
        
        # Attribute queries (3)
        attribute_queries = self.query_generator.generate_attribute_queries(assembly_info, count=3)
        queries.extend(attribute_queries)
        self.stats['query_types']['attribute'] += len(attribute_queries)
        logger.info(f"Generated {len(attribute_queries)} attribute queries, waiting 3 seconds...")
        time.sleep(8)
        
        # Structure queries (3)
        structure_queries = self.query_generator.generate_structure_queries(assembly_info, count=3)
        queries.extend(structure_queries)
        self.stats['query_types']['structure'] += len(structure_queries)
        logger.info(f"Generated {len(structure_queries)} structure queries, waiting 3 seconds...")
        time.sleep(8)
        
        # Semantic queries (3)
        semantic_queries = self.query_generator.generate_semantic_queries(assembly_info, count=3)
        queries.extend(semantic_queries)
        self.stats['query_types']['semantic'] += len(semantic_queries)
        logger.info(f"Generated {len(semantic_queries)} semantic queries, waiting 3 seconds...")
        time.sleep(8)
        
        return queries
    
    def _generate_hybrid_queries(self, assembly_info: Dict[str, Any], 
                                single_queries: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Generate multimodal queries"""
        queries = []
        
        # Group single-modal queries by type
        attribute_queries = [q for q in single_queries if q['query_type'] == 'Attribute']
        structure_queries = [q for q in single_queries if q['query_type'] == 'Structure']
        semantic_queries = [q for q in single_queries if q['query_type'] == 'Semantic']
        
        # Pairwise combination queries
        # 1. Structure + Attribute
        hybrid_query = self.query_generator.generate_hybrid_query(
            assembly_info, 
            [random.choice(structure_queries), random.choice(attribute_queries)]
        )
        queries.append(hybrid_query)
        self.stats['query_types']['hybrid_structure_attribute'] += 1
        logger.info("Generated Structure + Attribute hybrid query, waiting 3 seconds...")
        time.sleep(8)
        
        # 2. Attribute + Semantic
        hybrid_query = self.query_generator.generate_hybrid_query(
            assembly_info, 
            [random.choice(attribute_queries), random.choice(semantic_queries)]
        )
        queries.append(hybrid_query)
        self.stats['query_types']['hybrid_attribute_semantic'] += 1
        logger.info("Generated Attribute + Semantic hybrid query, waiting 3 seconds...")
        time.sleep(8)
        
        # 3. Structure + Semantic
        hybrid_query = self.query_generator.generate_hybrid_query(
            assembly_info, 
            [random.choice(structure_queries), random.choice(semantic_queries)]
        )
        queries.append(hybrid_query)
        self.stats['query_types']['hybrid_structure_semantic'] += 1
        logger.info("Generated Structure + Semantic hybrid query, waiting 3 seconds...")
        time.sleep(8)
        
        # Three-category fusion query
        hybrid_query = self.query_generator.generate_hybrid_query(
            assembly_info, 
            [random.choice(attribute_queries), random.choice(structure_queries), random.choice(semantic_queries)]
        )
        queries.append(hybrid_query)
        self.stats['query_types']['hybrid_all'] += 1
        logger.info("Generated three-category fusion hybrid query, waiting 3 seconds...")
        time.sleep(8)
        
        return queries
    
    def _save_queries(self, assembly_dir: Path, single_queries: List[Dict[str, Any]], 
                     hybrid_queries: List[Dict[str, Any]]):
        """Save query files"""
        # Save single-modal queries
        single_modal_file = assembly_dir / "single_modal_query.json"
        with open(single_modal_file, 'w', encoding='utf-8') as f:
            json.dump(single_queries, f, ensure_ascii=False, indent=2)
        
        # Save multimodal queries
        hybrid_file = assembly_dir / "Hybrid_query.json"
        with open(hybrid_file, 'w', encoding='utf-8') as f:
            json.dump(hybrid_queries, f, ensure_ascii=False, indent=2)
    
    def _generate_statistics(self):
        """Generate statistics report"""
        stats_file = self.output_dir / "generation_statistics.json"
        
        # Calculate total queries
        total_queries = self.stats['single_modal_queries'] + self.stats['hybrid_queries']
        
        # Add summary information
        self.stats['total_queries'] = total_queries
        self.stats['success_rate'] = (self.stats['total_assemblies'] / self.num_assemblies) * 100
        
        # Save statistics
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(self.stats, f, ensure_ascii=False, indent=2)
        
        # Print statistics
        logger.info("=== Generation Statistics ===")
        logger.info(f"Filter Criteria: {self.stats['filter_criteria']}")
        logger.info(f"Total Qualified Assemblies: {self.stats['qualified_assemblies_count']}")
        logger.info(f"Target Assembly Count: {self.num_assemblies}")
        logger.info(f"Successfully Processed Assemblies: {self.stats['total_assemblies']}")
        logger.info(f"Success Rate: {self.stats['success_rate']:.2f}%")
        logger.info(f"Total Single-Modal Queries: {self.stats['single_modal_queries']}")
        logger.info(f"Total Multimodal Queries: {self.stats['hybrid_queries']}")
        logger.info(f"Total Queries: {total_queries}")
        logger.info(f"Failed Assemblies: {len(self.stats['failed_assemblies'])}")
        
        # Print query type distribution
        logger.info("=== Query Type Distribution ===")
        for query_type, count in self.stats['query_types'].items():
            logger.info(f"{query_type}: {count}")


def main():
    """Main function"""
    # Create logs directory
    os.makedirs("logs", exist_ok=True)
    
    # Create generator and run
    generator = MCQBenchGenerator(num_assemblies=100)
    generator.generate_benchmark()


if __name__ == "__main__":
    main()
