from typing import List, Dict, Any, Literal, Optional
from pydantic import BaseModel, Field, model_validator

class BaseTask(BaseModel):
    task_id: str = Field(..., description="唯一任务ID")

class SearchResultItem(BaseModel):
    # TODO 返回的结果还要包括模型路径，各个智能体只返回uuid, 最后再从postgres数据库中检索元数据。
    """搜索结果项 - 标准化的搜索结果格式"""
    rank: int = Field(description="结果排名")
    uuid: str = Field(description="模型uuid（装配体uuid或零件uuid）")
    name: str = Field(description="模型名称")
    description: str = Field(description="模型描述")
    similarity_score: Optional[float] = Field(default=None, description="相似度分数（适用于向量搜索）")
    search_type: str = Field(description="搜索类型：geometry, semantic, hybrid, structured, structural")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="额外的元数据（如材料、质量、体积等）")
    model_url: Optional[str] = Field(default=None, description="模型文件的URL路径")

class QueryResult(BaseModel):
    """标准化查询结果"""
    task_id: str
    status: Literal['success', 'failure']
    error_message: str | None = None
    results: List[SearchResultItem] = Field(default_factory=list, description="标准化的搜索结果列表")
    execution_time: Optional[float] = Field(default=None, description="查询执行时间（秒）")
    total_results: int = Field(default=0, description="结果总数")
    execution_plan: Optional['QueryPlan'] = Field(default=None, description="实际执行的查询计划")

# --- 具体的任务模型 ---

class StructuredDataTask(BaseTask):
    """结构化数据查询任务 (in PostgreSQL)"""
    sql_query: str = Field(description="完整的SQL查询语句，由大模型生成")

class VectorSearchTask(BaseTask):
    """通用向量搜索任务 (in Milvus)"""
    query_vector: List[float]
    search_partition: Literal['geometric', 'semantic'] = Field(description="指定在哪个向量分区或集合中搜索")
    top_k: int = 10

class StructuralQueryTask(BaseTask):
    """结构关系查询任务 (in Neo4j)"""
    cypher_query: str = Field(description="完整的Cypher查询语句，由大模型生成")

# --- 统一的智能体任务模型 ---

class UnifiedStructuredDataTask(BaseTask):
    """统一的结构化数据查询任务"""
    query_text: str = Field(description="自然语言查询文本")
    id_list: Optional[List[str]] = Field(default=None, description="可选的ID列表，用于缩小查询范围")

class UnifiedStructuralQueryTask(BaseTask):
    """统一的结构关系查询任务"""
    query_text: str = Field(description="自然语言查询文本，描述装配层次结构和连接关系")
    id_list: Optional[List[str]] = Field(default=None, description="可选的ID列表，用于缩小查询范围")

class UnifiedGeometrySemanticTask(BaseTask):
    """统一的几何和语义查询任务"""
    query_text: Optional[str] = Field(default=None, description="自然语言查询文本")
    shape_vector: Optional[List[float]] = Field(default=None, description="形状特征向量")
    id_list: Optional[List[str]] = Field(default=None, description="可选的ID列表，用于缩小查询范围")
    top_k: int = Field(default=5, description="返回结果数量")
    shape_weight: float = Field(default=0.3, description="形状搜索权重，范围0-1")
    use_reranker: bool = Field(default=True, description="是否使用重排序器")

    @model_validator(mode='after')
    def validate_query_inputs(self):
        """验证查询输入：自然语言描述和形状特征向量不可同时为空"""
        if not self.query_text and not self.shape_vector:
            raise ValueError("自然语言描述和形状特征向量不可同时为空")
        return self

# --- 增强的任务模型 ---

class TextSearchTask(BaseTask):
    """文本语义搜索任务"""
    query_text: str = Field(description="自然语言查询文本")
    top_k: int = Field(default=5, description="返回结果数量")
    use_reranker: bool = Field(default=True, description="是否使用重排序器")

class ShapeSearchTask(BaseTask):
    """形状几何搜索任务"""
    shape_vector: List[float] = Field(description="形状特征向量，通常为768维")
    top_k: int = Field(default=5, description="返回结果数量")

class HybridSearchTask(BaseTask):
    """混合搜索任务（文本+形状）"""
    query_text: str = Field(description="自然语言查询文本")
    shape_vector: Optional[List[float]] = Field(default=None, description="形状特征向量（可选）")
    top_k: int = Field(default=5, description="返回结果数量")
    shape_weight: float = Field(default=0.3, description="形状搜索权重，范围0-1")

class Text2SQLTask(BaseTask):
    """自然语言转SQL查询任务"""
    query_text: str = Field(description="自然语言查询")
    auto_execute: bool = Field(default=True, description="是否自动执行生成的SQL")

class Text2CypherTask(BaseTask):
    """自然语言转Cypher查询任务"""
    query_text: str = Field(description="自然语言查询")
    auto_execute: bool = Field(default=True, description="是否自动执行生成的Cypher")

class PropertyFilterTask(BaseTask):
    """基于属性过滤的查询任务"""
    material: Optional[str] = None
    mass_min: Optional[float] = None
    mass_max: Optional[float] = None
    volume_min: Optional[float] = None
    volume_max: Optional[float] = None
    has_holes: Optional[bool] = None
    limit: int = Field(default=50, description="返回结果数量限制")

# --- 查询模型 (用于测试和直接查询) ---

class GeometricQuery(BaseModel):
    """几何查询模型"""
    query_text: str = Field(description="自然语言查询文本")
    shape_vector: Optional[List[float]] = Field(default=None, description="形状特征向量")
    top_k: int = Field(default=5, description="返回结果数量")
    similarity_threshold: float = Field(default=0.5, description="相似度阈值")

class SemanticQuery(BaseModel):
    """语义查询模型"""
    query_text: str = Field(description="自然语言查询文本")
    top_k: int = Field(default=5, description="返回结果数量")
    use_reranker: bool = Field(default=True, description="是否使用重排序器")

class StructuredQuery(BaseModel):
    """结构化数据查询模型"""
    query_text: str = Field(description="自然语言查询文本")
    table_name: Optional[str] = Field(default=None, description="目标表名")
    filters: Optional[Dict[str, Any]] = Field(default=None, description="过滤条件")
    limit: int = Field(default=10, description="返回结果数量限制")

class StructuralQuery(BaseModel):
    """结构关系查询模型"""
    query_text: str = Field(description="自然语言查询文本")
    node_type: Optional[str] = Field(default=None, description="节点类型，如Part、Assembly等")
    relationship_type: Optional[str] = Field(default=None, description="关系类型，如CONTAINS、CONNECTED_TO等")
    start_node_id: Optional[str] = Field(default=None, description="起始节点ID")
    end_node_id: Optional[str] = Field(default=None, description="结束节点ID")
    max_depth: int = Field(default=5, description="最大搜索深度")
    properties: Optional[Dict[str, Any]] = Field(default=None, description="节点属性过滤条件")
    limit: int = Field(default=10, description="返回结果数量限制")

# --- 复合查询任务模型 ---

class MultiModalQueryTask(BaseTask):
    """多模态查询任务"""
    text_query: Optional[str] = None
    shape_vector: Optional[List[float]] = None
    property_filters: Optional[PropertyFilterTask] = None
    structural_query: Optional[str] = None
    fusion_strategy: Literal['weighted', 'sequential', 'parallel'] = Field(default='weighted')
    weights: Dict[str, float] = Field(default_factory=dict, description="各查询模态的权重")

class StructuralQueryResult(BaseModel):
    """结构关系查询结果"""
    success: bool = Field(description="查询是否成功")
    cypher_query: Optional[str] = Field(default=None, description="生成的Cypher查询")
    results: Optional[List[Dict[str, Any]]] = Field(default=None, description="查询结果")
    error: Optional[str] = Field(default=None, description="错误信息")
    execution_time: Optional[float] = Field(default=None, description="执行时间")

# --- 查询规划智能体的数据模型 ---

class QueryPlan(BaseModel):
    """
    查询计划模型
    
    表示一个完整的查询执行计划，包含:
    - 多个子查询，将并行执行
    - 结果融合策略，在所有子查询完成后执行
    """
    plan_id: str = Field(description="计划唯一ID")
    query_text: str = Field(description="原始用户查询文本")
    query_type: str = Field(description="查询类型")
    sub_queries: List[Dict[str, Any]] = Field(description="子查询列表，将并行执行")
    fusion_strategy: str = Field(description="结果融合策略: INTERSECT(交集), UNION(并集), WEIGHTED(加权)")
    execution_mode: str = Field(default="parallel", description="执行模式，默认为并行执行")

class QueryPlannerTask(BaseTask):
    """查询规划任务模型"""
    query_text: str = Field(description="用户原始查询文本")
    shape_vector: Optional[List[float]] = Field(default=None, description="形状特征向量（如果有）")
    top_k: int = Field(default=10, description="最终返回结果数量")

class EnhancedQueryResult(QueryResult):
    """增强的查询结果，包含查询信息"""
    query_info: Dict[str, Any] = Field(default_factory=dict, description="查询相关信息")

# --- 多智能体协调相关模型 ---

class MultiAgentTask(BaseTask):
    """多智能体协调任务"""
    query_text: str = Field(description="用户查询文本")
    shape_vector: Optional[List[float]] = Field(default=None, description="形状特征向量")
    top_k: int = Field(default=10, description="返回结果数量")
    enable_query_planning: bool = Field(default=True, description="是否启用查询规划")

class AgentExecutionStep(BaseModel):
    """智能体执行步骤"""
    step_id: str = Field(description="步骤ID")
    agent_type: str = Field(description="智能体类型")
    task_params: Dict[str, Any] = Field(description="任务参数")
    depends_on: Optional[str] = Field(default=None, description="依赖的步骤ID")
    use_previous_results: bool = Field(default=False, description="是否使用前置结果")
    status: Optional[str] = Field(default=None, description="执行状态")
    result: Optional[QueryResult] = Field(default=None, description="执行结果")

class MultiAgentExecutionPlan(BaseModel):
    """多智能体执行计划"""
    plan_id: str = Field(description="计划ID")
    query_text: str = Field(description="原始查询")
    steps: List[AgentExecutionStep] = Field(description="执行步骤")
    fusion_strategy: Dict[str, Any] = Field(description="结果融合策略")

# 更新前向引用
QueryResult.model_rebuild()