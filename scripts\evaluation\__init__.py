"""
混合查询评估模块

本模块提供了完整的混合查询评估功能，包括：
- 单个查询评估
- 批量评估
- 对比分析
- 结果保存和可视化

主要组件：
- HybridQueryEvaluator: 核心评估器类
- BatchEvaluator: 批量评估器
- 各种评估脚本和工具

使用示例：
    from scripts.evaluation.hybrid_query_evaluator import HybridQueryEvaluator
    
    evaluator = HybridQueryEvaluator()
    await evaluator.initialize()
    summary = await evaluator.evaluate_all_hybrid_queries(max_assemblies=10)
"""

import os
from pathlib import Path

# 确保必要的目录存在
def ensure_directories():
    """确保评估所需的目录存在"""
    directories = [
        "results",
        "results/evaluation",
        "logs"
    ]
    
    for dir_path in directories:
        Path(dir_path).mkdir(parents=True, exist_ok=True)

# 在模块导入时自动创建目录
ensure_directories()

__version__ = "1.0.0"
__author__ = "CAD-RAG Team"
