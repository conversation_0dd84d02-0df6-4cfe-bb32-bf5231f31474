import re

def clean_model_name(name: str) -> str:
    """
    对输入的模型名称进行规范化处理：
    1. 每个单词首字母大写
    2. 单词之间用一个空格隔开
    3. 删除名称中的数字
    """
    # 去除数字
    name_no_digits = re.sub(r'\d+', '', name)
    # 用非字母分隔符分割为单词
    words = re.split(r'[^A-Za-z]+', name_no_digits)
    # 过滤空字符串并首字母大写
    words = [w.capitalize() for w in words if w]
    # 用空格连接
    return ' '.join(words)

if __name__ == "__main__":
    test_names = [
        "Component123Body456",
        "Untitled_789",
        "wheel_group_01",
        "mainBody",
        "Frame-2025",
        "assembly_part_3",
        "motor Housing12",
        "Screw_001"
    ]
    for name in test_names:
        print(f"原始: {name} -> 规范化: {clean_model_name(name)}")
