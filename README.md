# CAD-RAG

#### 介绍

将 fusion 360 assembly 中的装配体模型构造为知识图谱，然后进行 RAG 检索和问答

#### 软件架构

软件架构说明

#### 安装教程

1. 克隆项目

```bash
git clone [项目地址]
cd cad-rag
```

2. 创建并激活虚拟环境（使用 uv）

```bash
# 创建虚拟环境
uv venv

# 激活虚拟环境
# Windows
.venv\Scripts\activate
# Linux/Mac
source .venv/bin/activate
```

3. 安装依赖

```bash
uv sync
or
uv pip install . -i https://pypi.tuna.tsinghua.edu.cn/simple

.\.venv\Scripts\activate
```

windows 系统的 pytorch 需要单独安装：uv pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

pip install -U FlagEmbedding

4. neo4j 安装
   cd external\milvus-docker\neo4j
   docker-compose -f docker-compose-windows.yml up -d
   or
   docker-compose -f docker-compose-linux.yml up -d

#### 使用说明

1.  将 fusion360 测试集中的数据插入到 neo4j 中，使用 scripts/fusion360_to_KG.py
2.  创建形状嵌入的索引：python scripts/shape_embedding_index.py create --dimension 768 --metric cosine

#### 数据库设计

##### 关系型数据库

零件表：包括 ID(唯一标识符，text, 不是主键)、顶层装配体 ID(唯一标识符，text)、名称（text）、材质（text）、描述（text）、长（float）、宽（float）、高（float）、面积（float）、体积（float）、密度（float）、质量（float）、孔的数量（int）、hole*avg_diameter（float）、hole_std_dev_diameter（float）、hole_avg_length（float）、hole* std_dev_length（float）

其中，长度的单位是 cm，重量的单位是 kg。 The area in cm^2， The volume in cm^3，The density in kg/cm^3， The mass in kg

零件表创建语句：

```sql
CREATE TABLE parts (
    uuid TEXT PRIMARY KEY,  -- 唯一标识符
    top_level_assembly_id TEXT NOT NULL,  -- 顶层装配体的唯一标识符
    name TEXT,  -- 零件名称
    material TEXT,  -- 材质
    description TEXT,  -- 描述信息
    length FLOAT,  -- 长度
    width FLOAT,  -- 宽度
    height FLOAT,  -- 高度
    area FLOAT,  -- 表面积
    volume FLOAT,  -- 体积
    density FLOAT,  -- 密度
    mass FLOAT,  -- 质量
    hole_count INT,  -- 孔的数量
    hole_diameter_mean FLOAT,  -- 孔的平均直径
    hole_diameter_std FLOAT,  -- 孔直径的标准差
    hole_depth_mean FLOAT,  -- 孔的平均深度
    hole_depth_std FLOAT  -- 孔深度的标准差
);
COMMENT ON COLUMN parts.uuid IS '唯一标识符';
COMMENT ON COLUMN parts.top_level_assembly_id IS '顶层装配体的唯一标识符';
COMMENT ON COLUMN parts.name IS '零件名称';
COMMENT ON COLUMN parts.material IS '材质';
COMMENT ON COLUMN parts.description IS '描述信息';
COMMENT ON COLUMN parts.length IS '长度';
COMMENT ON COLUMN parts.width IS '宽度';
COMMENT ON COLUMN parts.height IS '高度';
COMMENT ON COLUMN parts.area IS '表面积';
COMMENT ON COLUMN parts.volume IS '体积';
COMMENT ON COLUMN parts.density IS '密度';
COMMENT ON COLUMN parts.mass IS '质量';
COMMENT ON COLUMN parts.hole_count IS '孔的数量';
COMMENT ON COLUMN parts.hole_diameter_mean IS '孔的平均直径';
COMMENT ON COLUMN parts.hole_diameter_std IS '孔直径的标准差';
COMMENT ON COLUMN parts.hole_depth_mean IS '孔的平均深度';
COMMENT ON COLUMN parts.hole_depth_std IS '孔深度的标准差';

-- 确保用户有表的操作权限
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE parts TO postgres;
```

装配体表创建语句：

```sql
CREATE TABLE assemblies (
    uuid TEXT PRIMARY KEY,  -- 唯一标识符
    name TEXT,  -- 装配体名称
    length FLOAT,  -- 长度
    width FLOAT,  -- 宽度
    height FLOAT,  -- 高度
    area FLOAT,  -- 表面积
    volume FLOAT,  -- 体积
    density FLOAT,  -- 密度
    mass FLOAT,  -- 质量
    part_count INT,  -- 包含的零件数量
    joints_count INT,  -- 关节数量
    contacts_count INT,  -- 接触面数量
    part_volume_std FLOAT,  -- 零件体积的标准差
    part_mass_std FLOAT,    -- 零件质量的标准差
    industry TEXT,  -- 所属行业
    category TEXT,  -- 分类
    description TEXT  -- 描述信息
);
COMMENT ON COLUMN assemblies.uuid IS '唯一标识符';
COMMENT ON COLUMN assemblies.name IS '装配体名称';
COMMENT ON COLUMN assemblies.length IS '长度';
COMMENT ON COLUMN assemblies.width IS '宽度';
COMMENT ON COLUMN assemblies.height IS '高度';
COMMENT ON COLUMN assemblies.area IS '表面积';
COMMENT ON COLUMN assemblies.volume IS '体积';
COMMENT ON COLUMN assemblies.density IS '密度';
COMMENT ON COLUMN assemblies.mass IS '质量';
COMMENT ON COLUMN assemblies.part_count IS '包含的零件数量';
COMMENT ON COLUMN assemblies.joints_count IS '关节数量';
COMMENT ON COLUMN assemblies.contacts_count IS '接触面数量';
COMMENT ON COLUMN assemblies.part_volume_std IS '零件体积的标准差';
COMMENT ON COLUMN assemblies.part_mass_std IS '零件质量的标准差';
COMMENT ON COLUMN assemblies.industry IS '所属行业';
COMMENT ON COLUMN assemblies.category IS '分类';
COMMENT ON COLUMN assemblies.description IS '描述信息';

-- 确保用户有表的操作权限
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE assemblies TO postgres;
```

postgres 启动：
docker run -d --name postgres -e POSTGRES_USER=postgres -e POSTGRES_PASSWORD=password123 -e POSTGRES_DB=postgres -p 5432:5432 postgres:latest

#### 参与贡献

1.  Fork 本仓库
2.  新建 Feat_xxx 分支
3.  提交代码
4.  新建 Pull Request

#### 特技

1.  使用 Readme_XXX.md 来支持不同的语言，例如 Readme_en.md, Readme_zh.md
2.  Gitee 官方博客 [blog.gitee.com](https://blog.gitee.com)
3.  你可以 [https://gitee.com/explore](https://gitee.com/explore) 这个地址来了解 Gitee 上的优秀开源项目
4.  [GVP](https://gitee.com/gvp) 全称是 Gitee 最有价值开源项目，是综合评定出的优秀开源项目
5.  Gitee 官方提供的使用手册 [https://gitee.com/help](https://gitee.com/help)
6.  Gitee 封面人物是一档用来展示 Gitee 会员风采的栏目 [https://gitee.com/gitee-stars/](https://gitee.com/gitee-stars/)

#### 开发记录

2025.6.10：为形状嵌入创建索引，但是只能分别为不同类型的节点创建索引，这样查询时也需要分多条语句才能查询
2025.6.12：1.scripts\part*shape_embedding_extractor.py，使用 CLIP 提取所有测试零件的向量，保存到本地；2.使用降维可视化和 HDBSCAN 聚类分析结果；3. 将向量插入到 milvus，未验证
2025.6.13：提取零件的孔聚合特征，和装配体的孔数等，和论文对应。并导入到 postgres 数据库中。
2025.6.17：新增大模型调用工具类，整合各类大模型供应商，目前测试 gemini 没问题。 添加 src\agent\text2cypher_agent.py。 但是使用 augment 生成的多智能体系统目前没用。
2025.6.18: 结合 BOM 树和渲染图片一起生成装配体的文本描述。 使用 bge-3+milvus，实现装配体语义描述的混合搜索。
将装配体的形状嵌入也存入到 milvus 中，实现形状嵌入的混合搜索，但是没有测试混合检索的性能。MCP 也没有测试。将零件的 uuid,name,description,shape_embedding, text_embedding 存入到 milvus 中
2025.6.20：使用 claude 4 创建我的多智能体，目前测试三个查询智能体都能跑通。
2025.6.23: 先提取零件和装配体的文本描述，再导入到 milvus 中。但是文本描述的稀疏向量不会处理，最终还是在导入 mivlus 时才提取文本向量。
2025.6.24：修改形状向量的度量方式为余弦相似度。修复导入 milvus 的零件名称与其他数据库不一致的问题（装配体 id*零件 id）
2025.6.25：多智能体系统现在会在初始化时就连接数据库并预加载 CLIP 模型和 BGE 模型，而不是在查询时才加载。
api 接口实现智能体中间过程输出。
2025.7.7 零件的多模态特征提取，并相应的修改聚类分析代码，可以分析不同类型的零件特征向量。
2025.7.8 装配体的多模态特征提取，物理数值属性聚类效果太拉了。
2025.7.29 修复子查询未能并行执行的bug。主要原因：MultiProviderLLM.chat()方法是同步方法（第57行），而不是异步方法。这意味着当多个智能体同时调用LLM时，它们实际上是在阻塞等待LLM响应，而不是真正的并行执行。同样，Milvus的搜索方法也是同步的。

# TODO

1. 子装配体没有自带的渲染图片，需要自行渲染。

2. 比较 BAAI/bge-large-zh-v1.5 和 BAAI/bge-m3 对于装配体语义检索，哪个效果更好。

3. 当前的查询策略是串行的，但是有的其实可以并行处理，只有复杂的耗时查询，才需要通过简单查询的结果来过滤。需要统计一下不同查询的耗时，最终给出一个合理的查询策略。

4. 大模型调用采用 openai 兼容的方式。
