#!/usr/bin/env python
# -*- coding: utf-8 -*-

import torch

def test_cuda_availability():
    """测试CUDA是否可用，并显示相关信息"""
    
    print(f"PyTorch版本: {torch.__version__}")
    
    # 检查CUDA是否可用
    cuda_available = torch.cuda.is_available()
    print(f"CUDA是否可用: {cuda_available}")
    
    if cuda_available:
        # 获取当前设备
        current_device = torch.cuda.current_device()
        print(f"当前CUDA设备: {current_device}")
        
        # 获取设备名称
        device_name = torch.cuda.get_device_name(current_device)
        print(f"设备名称: {device_name}")
        
        # 获取可用GPU数量
        gpu_count = torch.cuda.device_count()
        print(f"可用GPU数量: {gpu_count}")
        
        # 设备能力
        device_capability = torch.cuda.get_device_capability(current_device)
        print(f"设备能力: {device_capability}")
        
        # 分配一个小张量到GPU以测试是否工作正常
        try:
            x = torch.tensor([1.0, 2.0, 3.0]).cuda()
            print(f"GPU张量测试成功: {x}")
            print(f"GPU张量设备: {x.device}")
        except Exception as e:
            print(f"GPU张量测试失败: {e}")
    else:
        print("CUDA不可用，PyTorch将使用CPU。")
        print(f"当前设备: CPU")

if __name__ == "__main__":
    test_cuda_availability() 