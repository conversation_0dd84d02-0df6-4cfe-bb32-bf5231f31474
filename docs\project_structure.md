# 项目结构说明

## 概述

本文档描述了 CAD-RAG 项目重新组织后的目录结构和各部分的功能。

## 目录结构

```
cad-rag/
├── src/                    # 核心源代码
│   ├── agents/            # 多智能体系统
│   ├── models/            # 模型相关（LLM、CLIP、文本嵌入等）
│   ├── database/          # 数据库操作（Milvus、Neo4j、PostgreSQL）
│   ├── extractors/        # 特征提取器（零件、装配体、Fusion360）
│   ├── utils/             # 工具函数
│   ├── config/            # 配置管理
│   └── evaluate/          # 评估相关代码
├── scripts/               # 脚本文件（数据处理、导入导出等）
├── tests/                 # 所有测试文件
│   ├── unit/              # 单元测试
│   └── integration/       # 集成测试
├── docs/                  # 文档
├── data/                  # 数据文件
│   ├── datasets/          # 数据集（Fusion360数据）
│   ├── features/          # 特征文件
│   │   ├── parts/         # 零件特征
│   │   └── assemblies/    # 装配体特征
│   ├── clustering/        # 聚类结果
│   │   ├── parts/         # 零件聚类
│   │   └── assemblies/    # 装配体聚类
│   ├── embeddings/        # 嵌入向量
│   ├── scalers/           # 特征缩放器
│   ├── cache/             # 缓存文件
│   └── models/            # 预训练模型
├── results/               # 结果输出
│   ├── visualization/     # 可视化结果
│   ├── clustering/        # 聚类分析结果
│   └── features/          # 特征分析结果
├── docker/                # Docker相关文件
│   ├── milvus/            # Milvus配置
│   └── neo4j/             # Neo4j配置
├── api/                   # API服务
├── tools/                 # 外部工具和实用程序
└── MCP/                   # MCP服务器
```

## 各目录详细说明

### src/ - 核心源代码

- **agents/**: 多智能体系统实现
  - 查询规划智能体
  - 结构化数据智能体
  - 几何语义智能体
  - 结构关系智能体
  - 智能体协调器

- **models/**: 模型相关代码
  - LLM 接口和配置
  - CLIP 模型
  - 文本嵌入模型

- **database/**: 数据库操作
  - Milvus 向量数据库操作
  - Neo4j 图数据库操作
  - PostgreSQL 关系数据库操作

- **extractors/**: 特征提取器
  - 零件特征提取器
  - 装配体特征提取器
  - Fusion360 数据提取器

- **utils/**: 工具函数
  - CAD 相关工具
  - 文件操作工具
  - LLM 工具

- **config/**: 配置管理
  - 统一配置系统
  - 环境配置

- **evaluate/**: 评估相关
  - 聚类分析
  - 性能评估

### data/ - 数据文件

重新组织的数据目录按照数据类型和用途进行分类：

- **datasets/**: 原始数据集
- **features/**: 提取的特征文件，按零件和装配体分类
- **clustering/**: 聚类结果，按零件和装配体分类
- **embeddings/**: 各种嵌入向量
- **scalers/**: 特征缩放器
- **cache/**: 缓存文件
- **models/**: 预训练模型文件

### results/ - 结果输出

所有分析和处理结果的输出目录：

- **visualization/**: 可视化图表和图像
- **clustering/**: 聚类分析结果
- **features/**: 特征分析结果

### tests/ - 测试

- **unit/**: 单元测试
- **integration/**: 集成测试

### tools/ - 工具

外部工具和实用程序：

- 装配体树可视化工具
- Milvus Docker 配置
- 健康检查工具
- 项目重构工具

## 主要改进

1. **清晰的模块分离**: 按功能将代码组织到不同的模块中
2. **数据分类管理**: 将数据按类型和用途进行分类存储
3. **结果集中管理**: 所有输出结果统一存放在 results/ 目录
4. **测试组织**: 将测试按类型分类
5. **工具集中**: 将外部工具和实用程序集中管理

## 迁移说明

从旧结构迁移到新结构的主要变化：

1. `datasets/` → `data/datasets/`
2. `dataset/` 中的文件按类型分散到 `data/` 的各个子目录
3. `visualization_results/` → `results/visualization/`
4. `test_scripts/` → `tests/unit/`
5. `docker_files/` → `docker/`
6. `external/` 和 `fusion360_tools/` → `tools/`
7. `src/agent/` → `src/agents/`
8. 特征提取器移动到 `src/extractors/`
9. 数据库相关代码移动到 `src/database/`

## 配置更新

由于目录结构的变化，可能需要更新以下配置：

1. 导入路径
2. 数据文件路径
3. 输出路径
4. 配置文件中的路径引用

建议在使用前检查和更新相关的路径配置。
