# 单模态查询评估器

本目录包含用于评估单个智能体查询准确率的评估工具，专门用于测试 `scripts/generate_query_dataset/generate_mcq_bench.py` 生成的单模态查询的准确率。

## 文件说明

### 1. `single_modal_evaluator.py`
主要的单模态查询评估器，用于评估所有单模态查询的准确率。

**功能特点：**
- 支持评估三种类型的智能体：属性查询智能体、结构关系查询智能体、几何和语义查询智能体
- 当结果中包含 ground_truth_results 中的 uuid 时视为准确
- 提供详细的评估统计信息，包括按智能体和查询类型的准确率分析
- 支持保存详细结果和评估总结

### 2. `agent_specific_evaluator.py`
特定智能体评估器，用于单独评估某个特定智能体的性能。

**功能特点：**
- 可以单独评估属性查询智能体、结构关系查询智能体或几何和语义查询智能体
- 提供针对特定智能体的详细性能分析
- 支持筛选特定查询类型进行评估

### 3. `run_single_modal_evaluation.py`
运行单模态查询评估的便捷脚本。

### 4. `hybrid_query_evaluator.py`
混合查询评估器（已存在），用于评估多智能体协调的混合查询。

## 使用方法

### 方法一：使用便捷脚本

```bash
# 评估所有装配体的单模态查询
python scripts/evaluation/run_single_modal_evaluation.py

# 评估前10个装配体，每个查询返回前5个结果
python scripts/evaluation/run_single_modal_evaluation.py --max_assemblies 10 --top_k 5

# 指定基准测试数据目录
python scripts/evaluation/run_single_modal_evaluation.py --benchmark_dir data/benchmark
```

### 方法二：直接使用评估器

```python
import asyncio
from scripts.evaluation.single_modal_evaluator import SingleModalEvaluator

async def main():
    evaluator = SingleModalEvaluator()
    
    # 初始化评估器
    await evaluator.initialize()
    
    # 执行评估
    summary = await evaluator.evaluate_all_single_modal_queries(
        max_assemblies=10,  # 评估前10个装配体
        top_k=10           # 每个查询返回前10个结果
    )
    
    # 打印结果
    evaluator.print_summary(summary)
    
    # 保存详细结果
    evaluator.save_detailed_results("results/detailed_results.json")

asyncio.run(main())
```

### 方法三：评估特定智能体

```python
import asyncio
from scripts.evaluation.agent_specific_evaluator import AgentSpecificEvaluator

async def main():
    evaluator = AgentSpecificEvaluator()
    
    # 评估属性查询智能体
    result = await evaluator.evaluate_agent(
        agent_type="attribute",  # "attribute", "structural", "semantic"
        max_assemblies=10,
        top_k=10
    )
    
    # 打印结果
    evaluator.print_agent_result(result)
    
    # 保存结果
    evaluator.save_agent_result(result, "results/attribute_agent_result.json")

asyncio.run(main())
```

## 评估指标

### 准确率计算
- **正确判断标准**: 当智能体返回的结果中包含任何一个 ground_truth_results 中的 uuid 时，视为该查询预测正确
- **整体准确率**: 正确预测的查询数 / 总查询数
- **按智能体准确率**: 每个智能体的正确预测数 / 该智能体处理的查询数
- **按查询类型准确率**: 每种查询类型的正确预测数 / 该类型的查询数

### 其他指标
- **平均执行时间**: 成功执行查询的平均耗时
- **错误率**: 执行失败的查询数 / 总查询数
- **失败查询数**: 由于错误无法正常执行的查询数量

## 输出结果

### 控制台输出
```
============================================================
单模态查询评估结果总结
============================================================
总查询数: 90
正确预测数: 72
整体准确率: 80.00%
平均执行时间: 2.35秒
失败查询数: 3
错误率: 3.33%

按智能体的准确率:
----------------------------------------
属性查询智能体: 85.00%
结构关系查询智能体: 78.33%
几何和语义查询智能体: 76.67%

按查询类型的准确率:
----------------------------------------
Attribute: 85.00%
Structure: 78.33%
Semantic: 76.67%
============================================================
```

### 保存的文件
1. **详细结果文件** (`single_modal_evaluation_YYYYMMDD_HHMMSS.json`): 包含每个查询的详细评估结果
2. **评估总结文件** (`single_modal_summary_YYYYMMDD_HHMMSS.json`): 包含汇总的评估统计信息

## 数据格式要求

### 输入数据格式
评估器期望的单模态查询文件格式（`single_modal_query.json`）：

```json
[
  {
    "query_id": "Attribute_469",
    "query_type": "Attribute",
    "natural_language_prompt": "Find assemblies with mass between 0.8 kg and 1.0 kg.",
    "ground_truth_plan": {
      "sub_queries": [
        {
          "agent": "属性查询智能体",
          "task_params": {
            "query_text": "Find assemblies with mass between 0.8 kg and 1.0 kg."
          }
        }
      ],
      "fusion_strategy": "NONE"
    },
    "ground_truth_results": [
      {
        "uuid": "132863_90d729e2"
      }
    ]
  }
]
```

### 输出数据格式
详细结果文件包含每个查询的评估结果：

```json
[
  {
    "query_id": "Attribute_469",
    "query_type": "Attribute", 
    "natural_language_prompt": "Find assemblies with mass between 0.8 kg and 1.0 kg.",
    "ground_truth_uuids": ["132863_90d729e2"],
    "predicted_uuids": ["132863_90d729e2", "other_uuid"],
    "is_correct": true,
    "execution_time": 2.35,
    "agent_name": "属性查询智能体",
    "error_message": null
  }
]
```

## 注意事项

1. **数据库连接**: 确保 PostgreSQL、Neo4j 和 Milvus 数据库正常运行并可连接
2. **模型加载**: 首次运行时需要加载语言模型和向量模型，可能需要较长时间
3. **内存使用**: 评估大量查询时注意内存使用情况
4. **网络延迟**: 评估过程中会添加适当延迟以避免过于频繁的数据库请求
5. **错误处理**: 单个查询失败不会中断整个评估过程，会记录错误信息并继续

## 扩展功能

可以根据需要扩展以下功能：
- 支持更多的评估指标（如 Precision@K, Recall@K）
- 添加可视化图表生成
- 支持并行评估以提高速度
- 添加更详细的错误分析
- 支持自定义评估标准
