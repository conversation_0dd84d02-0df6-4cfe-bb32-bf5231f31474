import os
import logging
from typing import Dict, Any
from dotenv import load_dotenv

# 尝试加载.env文件（如果存在）
load_dotenv()

class Config:
    """
    配置管理类，用于管理环境变量和配置项
    """
    
    # Neo4j数据库配置
    NEO4J_URI = os.getenv("NEO4J_URI", "bolt://localhost:7687")
    NEO4J_USER = os.getenv("NEO4J_USER", "neo4j")
    NEO4J_PASSWORD = os.getenv("NEO4J_PASSWORD", "password123")
    
    # Supabase配置
    SUPABASE_URL = os.getenv("SUPABASE_URL", "")
    SUPABASE_KEY = os.getenv("SUPABASE_KEY", "")
    
    # 日志配置
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
    
    # PostgreSQL数据库配置
    POSTGRES_CONFIG = {
        'host': os.getenv('POSTGRES_HOST', 'localhost'),
        'port': int(os.getenv('POSTGRES_PORT', '5434')),
        'dbname': os.getenv('POSTGRES_DB', 'postgres'),
        'user': os.getenv('POSTGRES_USER', 'postgres'),
        'password': os.getenv('POSTGRES_PASSWORD', '')
    }
    
    # Milvus向量数据库配置
    MILVUS_HOST = os.getenv("MILVUS_HOST", "localhost")
    MILVUS_PORT = os.getenv("MILVUS_PORT", "19530")
    
    # LLM/MLLM相关配置
    PROVIDER = os.getenv("LLM_PROVIDER", "openai")
    API_URL = os.getenv("LLM_API_URL", "http://*************:4033/v1")
    API_KEY = os.getenv("LLM_API_KEY", "")
    DEFAULT_LLM_MODEL = os.getenv("DEFAULT_LLM_MODEL", "DeepSeek-V3-0324")
    DEFAULT_MLLM_MODEL = os.getenv("DEFAULT_MLLM_MODEL", "gemma-3-27b")
    
    # 文本嵌入模型配置
    TEXT_EMBEDDING_API_URL = os.getenv("TEXT_EMBEDDING_API_URL", "http://*************:8080")
    TEXT_EMBEDDING_MODEL = os.getenv("TEXT_EMBEDDING_MODEL", "Qwen3-Embedding-8B")
    
    @classmethod
    def get_neo4j_config(cls) -> Dict[str, str]:
        """
        获取Neo4j数据库配置
        
        Returns:
            包含Neo4j连接信息的字典
        """
        return {
            "uri": cls.NEO4J_URI,
            "user": cls.NEO4J_USER,
            "password": cls.NEO4J_PASSWORD
        }
    
    @classmethod
    def get_supabase_config(cls) -> Dict[str, str]:
        """
        获取Supabase配置
        
        Returns:
            包含Supabase连接信息的字典
        """
        return {
            "url": cls.SUPABASE_URL,
            "key": cls.SUPABASE_KEY
        }
    
    @classmethod
    def get_milvus_config(cls) -> Dict[str, str]:
        """
        获取Milvus数据库配置
        
        Returns:
            包含Milvus连接信息的字典
        """
        return {
            "host": cls.MILVUS_HOST,
            "port": cls.MILVUS_PORT
        }
    
    @classmethod
    def configure_logging(cls) -> None:
        """
        配置日志
        """
        log_level = getattr(logging, cls.LOG_LEVEL.upper(), logging.INFO)
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    
    @classmethod
    def to_dict(cls) -> Dict[str, Any]:
        """
        将配置转换为字典
        
        Returns:
            配置字典
        """
        return {
            "neo4j": {
                "uri": cls.NEO4J_URI,
                "user": cls.NEO4J_USER,
                "password": "******"  # 隐藏密码
            },
            "supabase": {
                "url": cls.SUPABASE_URL,
                "key": "******"  # 隐藏密钥
            },
            "milvus": {
                "host": cls.MILVUS_HOST,
                "port": cls.MILVUS_PORT
            },
            "logging": {
                "level": cls.LOG_LEVEL
            },
            "postgres": cls.POSTGRES_CONFIG
        }
    
    @classmethod
    def print_config(cls, include_secrets: bool = False) -> None:
        """
        打印配置信息
        
        Args:
            include_secrets: 是否包含敏感信息
        """
        config = cls.to_dict()
        if include_secrets:
            config["neo4j"]["password"] = cls.NEO4J_PASSWORD
            config["supabase"]["key"] = cls.SUPABASE_KEY
        
        logging.info(f"当前配置: {config}")


# 在导入模块时配置日志
Config.configure_logging()