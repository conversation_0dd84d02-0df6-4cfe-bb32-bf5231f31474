import os
import sys
# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.models.LLM import MultiProviderLLM

# 1. OpenAI
# openai_llm = MultiProviderLLM(
#     provider="openai",
#     api_url="https://api.openai.com",
#     api_key="sk-...",
#     model="gpt-4o-preview",
# )
# reply = openai_llm.chat([{"role": "user", "content": "用一句话介绍量子计算"}])
# print(reply)

# 2. Azure OpenAI
# azure_llm = MultiProviderLLM(
#     provider="azure",
#     api_url="https://my-res.openai.azure.com",
#     api_key="az-...",
#     deployment="gpt4",
# )
# print(azure_llm.chat([{"role": "user", "content": "Hi!"}]))

# 3. Gemini + 图片
# llm = MultiProviderLLM(
#     provider="openai",
#     api_url="http://*************:4034",
#     api_key="sk-5489e9282d98",
#     model="Qwen3-235B-A22B-GPTQ-Int4",
# )
llm = MultiProviderLLM()


print(
    llm.chat(
        messages=[{"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "你是哪个模型"}]
    )
)

# print(
#     llm.chat_with_images(
#         messages=[{"role": "system", "content": "You are a helpful assistant."},
#             {"role": "user", "content": "描述一下图片中的物体"}],
#         image_paths=[r'E:\project\cad-rag\data\datasets\fusion360_assembly\19566_19d85d3b\assembly.png']
#     )
# )
