#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
使用不同的降维算法(TSNE, PCA, UMAP)对向量特征进行可视化。

适用于键为ID、值为向量的pkl文件格式（如assembly_representations.pkl）。

用法示例：

# 使用UMAP对装配体向量进行降维可视化
python evaluate_scripts/clustering_analysis/visualize_vector_embeddings.py \
    --feature_path dataset/assembly_representations.pkl \
    --method umap \
    --output_dir visualization_results/assembly_umap

# 使用TSNE进行可视化，并显示标签
python evaluate_scripts/clustering_analysis/visualize_vector_embeddings.py \
    --feature_path dataset/assembly_representations.pkl \
    --method tsne \
    --show_labels 1 \
    --max_labels 20

# 使用PCA进行可视化，自定义图形大小
python evaluate_scripts/clustering_analysis/visualize_vector_embeddings.py \
    --feature_path dataset/assembly_representations.pkl \
    --method pca \
    --figsize 15 12 \
    --alpha 0.8

# 3D可视化
python evaluate_scripts/clustering_analysis/visualize_vector_embeddings.py \
    --feature_path dataset/assembly_representations.pkl \
    --method umap \
    --n_components 3 \
    --output_dir visualization_results/assembly_3d

# 加载聚类标签进行彩色可视化
python evaluate_scripts/clustering_analysis/visualize_vector_embeddings.py \
    --feature_path dataset/assembly_representations.pkl \
    --method umap \
    --cluster_labels dataset/hdbscan_assembly_vector_labels.pkl \
    --color_by cluster
"""
import os
import sys
import pickle
import numpy as np
import matplotlib.pyplot as plt
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
import umap
from pathlib import Path
import random
import argparse
from typing import Dict, List, Tuple, Optional

def load_vector_features(feature_path: str) -> Tuple[np.ndarray, List[str]]:
    """
    从pkl文件中加载向量特征（键为ID，值为向量的字典格式）
    
    参数:
        feature_path (str): 特征文件路径
        
    返回:
        tuple: (特征向量数组, ID列表)
    """
    print(f"正在加载向量特征: {feature_path}")
    
    with open(feature_path, 'rb') as f:
        features_dict = pickle.load(f)
    
    if not isinstance(features_dict, dict):
        raise ValueError(f"期望字典格式，但得到了 {type(features_dict)}")
    
    # 提取ID和向量
    ids = list(features_dict.keys())
    vectors = list(features_dict.values())
    
    # 转换为numpy数组
    feature_vectors = np.array(vectors)
    
    print(f"加载了 {len(ids)} 个向量，维度: {feature_vectors.shape[1]}")
    
    # 检查数据质量
    nan_count = np.isnan(feature_vectors).sum()
    if nan_count > 0:
        print(f"警告: 发现 {nan_count} 个NaN值")
    
    zero_vectors = np.sum(np.all(feature_vectors == 0, axis=1))
    print(f"零向量数量: {zero_vectors} ({zero_vectors/len(vectors)*100:.1f}%)")
    
    return feature_vectors, ids

def load_cluster_labels(label_path: str) -> Tuple[Dict[str, int], int]:
    """
    加载聚类标签文件
    
    参数:
        label_path (str): 标签文件路径
        
    返回:
        tuple: (ID到标签的映射字典, 簇数)
    """
    print(f"正在加载聚类标签: {label_path}")
    
    with open(label_path, 'rb') as f:
        labels_data = pickle.load(f)
    
    # 处理不同的标签文件格式
    if isinstance(labels_data, dict):
        if 'labels' in labels_data:
            # 格式: {'labels': {id: label}, 'metadata': {...}}
            labels_dict = labels_data['labels']
        else:
            # 格式: {id: label}
            labels_dict = labels_data
    else:
        raise ValueError(f"不支持的标签文件格式: {type(labels_data)}")
    
    # 计算簇数（排除噪声标签-1）
    unique_labels = set(labels_dict.values())
    n_clusters = len(unique_labels) - (1 if -1 in unique_labels else 0)
    
    print(f"加载了 {len(labels_dict)} 个标签，簇数: {n_clusters}")
    
    return labels_dict, n_clusters

def apply_dimensionality_reduction(features: np.ndarray, method: str = 'umap', 
                                 n_components: int = 2, random_state: int = 42, **kwargs) -> np.ndarray:
    """
    应用降维算法
    
    参数:
        features (numpy.ndarray): 特征向量数组
        method (str): 降维方法，可选 'tsne', 'pca', 或 'umap'
        n_components (int): 降维后的维度
        random_state (int): 随机种子
        **kwargs: 其他降维参数
        
    返回:
        numpy.ndarray: 降维后的特征向量
    """
    print(f"使用 {method.upper()} 进行降维到 {n_components} 维...")
    print(f"原始特征维度: {features.shape}")
    
    if method.lower() == 'tsne':
        # 对于TSNE，如果特征维度太高，先用PCA降维
        if features.shape[1] > 50:
            print("特征维度过高，先使用PCA降维到50维")
            pca = PCA(n_components=50, random_state=random_state)
            features = pca.fit_transform(features)
        
        # TSNE参数
        tsne_params = {
            'n_components': n_components,
            'random_state': random_state,
            'perplexity': min(30, features.shape[0] // 4),  # 自适应perplexity
            'n_iter': 2000,
            'learning_rate': 'auto'
        }
        tsne_params.update(kwargs)
        reducer = TSNE(**tsne_params)
        
    elif method.lower() == 'pca':
        # 确保PCA的组件数不超过特征维度和样本数
        n_components = min(n_components, features.shape[1], features.shape[0])
        pca_params = {
            'n_components': n_components,
            'random_state': random_state
        }
        pca_params.update(kwargs)
        reducer = PCA(**pca_params)
        
    elif method.lower() == 'umap':
        # UMAP参数
        umap_params = {
            'n_components': n_components,
            'random_state': random_state,
            'n_neighbors': min(15, features.shape[0] - 1),  # 自适应邻居数
            'min_dist': 0.1,
            'metric': 'cosine'
        }
        umap_params.update(kwargs)
        reducer = umap.UMAP(**umap_params)
        
    else:
        raise ValueError(f"不支持的降维方法: {method}")
    
    reduced_features = reducer.fit_transform(features)
    print(f"降维后特征维度: {reduced_features.shape}")
    
    # 如果是PCA，输出解释方差比
    if method.lower() == 'pca':
        explained_var = reducer.explained_variance_ratio_.sum()
        print(f"PCA解释方差比: {explained_var:.4f}")
    
    return reduced_features

def get_colors_for_visualization(ids: List[str], color_by: str = 'random', 
                                cluster_labels: Optional[Dict[str, int]] = None) -> Tuple[List, List, str]:
    """
    为可视化生成颜色方案
    
    参数:
        ids: ID列表
        color_by: 着色方式
        cluster_labels: 聚类标签字典
        
    返回:
        tuple: (颜色列表, 图例元素列表, 图例标题)
    """
    if color_by == 'cluster' and cluster_labels is not None:
        # 根据聚类标签着色
        unique_labels = sorted(set(cluster_labels.values()))
        # 为噪声点(-1)使用灰色，为其他簇使用不同颜色
        cmap = plt.cm.tab20
        color_dict = {}
        for i, label in enumerate(unique_labels):
            if label == -1:
                color_dict[label] = 'gray'
            else:
                color_dict[label] = cmap(i % 20)
        
        colors = []
        for id_ in ids:
            label = cluster_labels.get(id_, -1)
            colors.append(color_dict[label])
        
        # 创建图例
        legend_elements = []
        for label in unique_labels:
            if label == -1:
                legend_elements.append(plt.Line2D([0], [0], marker='o', color='w',
                                                label='噪声', markerfacecolor='gray', markersize=8))
            else:
                legend_elements.append(plt.Line2D([0], [0], marker='o', color='w',
                                                label=f'簇 {label}', markerfacecolor=color_dict[label], markersize=8))
        
        return colors, legend_elements, "聚类"
    
    elif color_by == 'prefix':
        # 根据ID前缀着色
        prefixes = [id_.split('_')[0] if '_' in id_ else id_[:3] for id_ in ids]
        unique_prefixes = list(set(prefixes))
        color_dict = {prefix: plt.cm.tab20(i % 20) for i, prefix in enumerate(unique_prefixes)}
        colors = [color_dict[prefix] for prefix in prefixes]
        
        legend_elements = [plt.Line2D([0], [0], marker='o', color='w',
                                    label=prefix, markerfacecolor=color, markersize=8)
                          for prefix, color in color_dict.items()]
        
        return colors, legend_elements, "ID前缀"
    
    else:  # random coloring
        colors = [plt.cm.tab20(random.randint(0, 19)) for _ in range(len(ids))]
        return colors, [], None

def visualize_2d(reduced_features: np.ndarray, ids: List[str], output_dir: str = "visualization_results",
                method: str = "unknown", figsize: Tuple[float, float] = (12, 10), save_fig: bool = True,
                show_labels: bool = False, max_labels: int = 50, alpha: float = 0.7,
                color_by: str = 'random', cluster_labels: Optional[Dict[str, int]] = None,
                title_suffix: str = ""):
    """
    2D可视化降维后的特征向量
    
    参数:
        reduced_features: 降维后的特征向量
        ids: ID列表
        output_dir: 输出目录
        method: 降维方法
        figsize: 图形大小
        save_fig: 是否保存图形
        show_labels: 是否显示标签
        max_labels: 最大标签数量
        alpha: 透明度
        color_by: 着色方式
        cluster_labels: 聚类标签
        title_suffix: 标题后缀
    """
    os.makedirs(output_dir, exist_ok=True)
    
    plt.figure(figsize=figsize)
    ax = plt.subplot(111)
    
    # 获取颜色方案
    colors, legend_elements, legend_title = get_colors_for_visualization(ids, color_by, cluster_labels)
    
    # 绘制散点图
    scatter = ax.scatter(reduced_features[:, 0], reduced_features[:, 1], c=colors, alpha=alpha, s=50)
    
    # 不添加标签，避免编码混乱
    # if show_labels and len(ids) <= max_labels:
    #     for i, (x, y) in enumerate(reduced_features):
    #         plt.annotate(ids[i], (x, y), fontsize=8, alpha=0.8)
    # elif show_labels and len(ids) > max_labels:
    #     # 随机选择一些点进行标注
    #     indices = random.sample(range(len(ids)), max_labels)
    #     for i in indices:
    #         x, y = reduced_features[i]
    #         plt.annotate(ids[i], (x, y), fontsize=8, alpha=0.8)
    
    # 添加图例
    if legend_elements:
        if len(legend_elements) > 20:
            # 如果类别太多，只显示前20个
            legend_elements = legend_elements[:20]
        plt.legend(handles=legend_elements, title=legend_title, loc='best', fontsize='small')
    
    # 设置标题和轴标签
    title = f"向量特征的二维降维可视化 ({method.upper()})"
    if title_suffix:
        title += f" - {title_suffix}"
    plt.title(title)
    plt.xlabel("维度 1")
    plt.ylabel("维度 2")
    plt.grid(True, linestyle='--', alpha=0.3)
    
    # 保存图形
    if save_fig:
        plt.tight_layout()
        filename = f"vector_embeddings_2d_{method}.png"
        if title_suffix:
            filename = f"vector_embeddings_2d_{method}_{title_suffix.replace(' ', '_')}.png"
        filepath = os.path.join(output_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        print(f"已保存可视化图片到 {filepath}")
    
    plt.show()

def visualize_3d(reduced_features: np.ndarray, ids: List[str], output_dir: str = "visualization_results",
                method: str = "unknown", figsize: Tuple[float, float] = (12, 10), save_fig: bool = True,
                alpha: float = 0.7, color_by: str = 'random', cluster_labels: Optional[Dict[str, int]] = None,
                title_suffix: str = ""):
    """
    3D可视化降维后的特征向量
    
    参数:
        reduced_features: 降维后的特征向量
        ids: ID列表
        output_dir: 输出目录
        method: 降维方法
        figsize: 图形大小
        save_fig: 是否保存图形
        alpha: 透明度
        color_by: 着色方式
        cluster_labels: 聚类标签
        title_suffix: 标题后缀
    """
    os.makedirs(output_dir, exist_ok=True)
    
    fig = plt.figure(figsize=figsize)
    ax = fig.add_subplot(111, projection='3d')
    
    # 获取颜色方案
    colors, legend_elements, legend_title = get_colors_for_visualization(ids, color_by, cluster_labels)
    
    # 绘制3D散点图
    scatter = ax.scatter(
        reduced_features[:, 0],
        reduced_features[:, 1],
        reduced_features[:, 2],
        c=colors,
        alpha=alpha,
        s=50
    )
    
    # 添加图例
    if legend_elements:
        if len(legend_elements) > 20:
            legend_elements = legend_elements[:20]
        plt.legend(handles=legend_elements, title=legend_title, loc='best', fontsize='small')
    
    # 设置标题和轴标签
    title = f"向量特征的三维降维可视化 ({method.upper()})"
    if title_suffix:
        title += f" - {title_suffix}"
    plt.title(title)
    ax.set_xlabel("维度 1")
    ax.set_ylabel("维度 2")
    ax.set_zlabel("维度 3")
    
    # 保存图形
    if save_fig:
        plt.tight_layout()
        filename = f"vector_embeddings_3d_{method}.png"
        if title_suffix:
            filename = f"vector_embeddings_3d_{method}_{title_suffix.replace(' ', '_')}.png"
        filepath = os.path.join(output_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        print(f"已保存可视化图片到 {filepath}")
    
    plt.show()

def analyze_vector_statistics(features: np.ndarray, ids: List[str]):
    """
    分析向量统计信息
    
    参数:
        features: 特征向量数组
        ids: ID列表
    """
    print("\n=== 向量统计分析 ===")
    print(f"向量数量: {len(ids)}")
    print(f"向量维度: {features.shape[1]}")
    print(f"向量范围: [{features.min():.4f}, {features.max():.4f}]")
    print(f"向量均值: {features.mean():.4f}")
    print(f"向量标准差: {features.std():.4f}")
    
    # 零向量统计
    zero_vectors = np.sum(np.all(features == 0, axis=1))
    print(f"零向量数量: {zero_vectors} ({zero_vectors/len(features)*100:.1f}%)")
    
    # NaN值统计
    nan_count = np.isnan(features).sum()
    print(f"NaN值数量: {nan_count}")
    
    # 向量模长统计
    norms = np.linalg.norm(features, axis=1)
    print(f"向量模长统计:")
    print(f"  均值: {norms.mean():.4f}")
    print(f"  标准差: {norms.std():.4f}")
    print(f"  范围: [{norms.min():.4f}, {norms.max():.4f}]")
    
    # 向量相似性分析（如果向量数量不太多）
    if len(features) <= 1000:
        from sklearn.metrics.pairwise import cosine_similarity
        similarity_matrix = cosine_similarity(features)
        
        # 排除对角线元素
        mask = np.ones_like(similarity_matrix, dtype=bool)
        np.fill_diagonal(mask, False)
        similarities = similarity_matrix[mask]
        
        print(f"向量相似度统计:")
        print(f"  平均相似度: {similarities.mean():.4f}")
        print(f"  相似度标准差: {similarities.std():.4f}")
        print(f"  最大相似度: {similarities.max():.4f}")
        print(f"  最小相似度: {similarities.min():.4f}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="向量特征降维可视化")
    parser.add_argument("--feature_path", default="dataset/assembly_representations.pkl", 
                       help="特征文件路径（键为ID，值为向量的字典格式）")
    parser.add_argument("--method", default="umap", 
                       choices=['tsne', 'pca', 'umap'],
                       help="降维方法")
    parser.add_argument("--output_dir", default="visualization_results", help="输出目录")
    parser.add_argument("--n_components", type=int, default=2, 
                       choices=[2, 3], help="降维后的维度")
    parser.add_argument("--figsize", nargs=2, type=float, default=[12, 10], help="图形大小")
    parser.add_argument("--random_state", type=int, default=42, help="随机种子")
    parser.add_argument("--show_labels", type=int, choices=[0, 1], default=0, help="是否显示标签")
    parser.add_argument("--max_labels", type=int, default=50, help="最大标签数量")
    parser.add_argument("--alpha", type=float, default=0.7, help="点的透明度")
    parser.add_argument("--color_by", default="random", 
                       choices=['random', 'prefix', 'cluster'],
                       help="着色方式")
    parser.add_argument("--cluster_labels", default=None, 
                       help="聚类标签文件路径（用于cluster着色）")
    parser.add_argument("--analyze_stats", type=int, choices=[0, 1], default=1, 
                       help="是否分析向量统计信息")
    parser.add_argument("--title_suffix", default="", help="标题后缀")
    
    # 降维算法特定参数
    parser.add_argument("--tsne_perplexity", type=float, default=30, help="TSNE perplexity参数")
    parser.add_argument("--umap_n_neighbors", type=int, default=15, help="UMAP n_neighbors参数")
    parser.add_argument("--umap_min_dist", type=float, default=0.1, help="UMAP min_dist参数")
    
    args = parser.parse_args()
    
    # 创建输出目录
    output_dir = os.path.join(args.output_dir, f"vectors_{args.method}_{args.n_components}d")
    
    try:
        # 加载向量特征
        feature_array, ids = load_vector_features(args.feature_path)
        
        # 分析统计信息
        if args.analyze_stats:
            analyze_vector_statistics(feature_array, ids)
        
        # 加载聚类标签（如果提供）
        cluster_labels = None
        if args.cluster_labels and os.path.exists(args.cluster_labels):
            cluster_labels, n_clusters = load_cluster_labels(args.cluster_labels)
            print(f"已加载聚类标签，簇数: {n_clusters}")
        
        # 准备降维参数
        reduction_kwargs = {}
        if args.method == 'tsne':
            reduction_kwargs['perplexity'] = args.tsne_perplexity
        elif args.method == 'umap':
            reduction_kwargs['n_neighbors'] = args.umap_n_neighbors
            reduction_kwargs['min_dist'] = args.umap_min_dist
        
        # 应用降维
        reduced_features = apply_dimensionality_reduction(
            feature_array, 
            method=args.method, 
            n_components=args.n_components,
            random_state=args.random_state,
            **reduction_kwargs
        )
        
        # 生成可视化
        if args.n_components == 2:
            visualize_2d(
                reduced_features, 
                ids, 
                output_dir=output_dir,
                method=args.method,
                figsize=tuple(args.figsize),
                save_fig=True,
                show_labels=bool(args.show_labels),
                max_labels=args.max_labels,
                alpha=args.alpha,
                color_by=args.color_by,
                cluster_labels=cluster_labels,
                title_suffix=args.title_suffix
            )
        elif args.n_components == 3:
            visualize_3d(
                reduced_features, 
                ids, 
                output_dir=output_dir,
                method=args.method,
                figsize=tuple(args.figsize),
                save_fig=True,
                alpha=args.alpha,
                color_by=args.color_by,
                cluster_labels=cluster_labels,
                title_suffix=args.title_suffix
            )
            
        print(f"\n降维可视化完成！输出目录: {output_dir}")
        
    except Exception as e:
        print(f"可视化过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
