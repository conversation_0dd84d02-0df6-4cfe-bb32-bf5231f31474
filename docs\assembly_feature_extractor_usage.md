# 装配体特征提取器使用指南

## 概述

装配体特征提取器是一个用于从 CAD-RAG 系统中提取装配体多模态特征的工具。它可以从 PostgreSQL 数据库和 Milvus 向量数据库中提取装配体的结构化特征、形状特征和语义特征，并进行标准化、PCA 降维、L2 归一化和加权融合等处理。

## 主要组件

### 1. AssemblyFeatureExtractor 类 (`src/extractors/assembly_feature_extractor.py`)

核心特征提取器类，提供以下功能：

- **结构化特征提取**：从 PostgreSQL 的`assemblies`表提取 12 维数值特征
- **形状特征提取**：从 Milvus 的`cad_collection_en`集合提取 768 维形状向量
- **语义特征提取**：从 Milvus 的`cad_collection_en`集合提取 1024 维语义向量
- **特征处理**：Z-score 标准化、PCA 降维、L2 归一化、加权拼接

### 2. 提取脚本 (`scripts/extract_assembly_features.py`)

命令行工具，用于批量提取和保存装配体特征。

### 3. 测试脚本 (`tests/unit/test_assembly_feature_extractor.py`)

完整的测试套件，验证特征提取器的各项功能。

## 特征说明

### 结构化特征 (12 维)

从`assemblies`表提取的数值特征：

| 特征名          | 描述           | 单位        |
| --------------- | -------------- | ----------- |
| length          | 长度           | 毫米        |
| width           | 宽度           | 毫米        |
| height          | 高度           | 毫米        |
| area            | 表面积         | 平方毫米    |
| volume          | 体积           | 立方毫米    |
| density         | 密度           | 克/立方厘米 |
| mass            | 质量           | 克          |
| part_count      | 包含的零件数量 | 个          |
| joints_count    | 关节数量       | 个          |
| contacts_count  | 接触面数量     | 个          |
| part_volume_std | 零件体积标准差 | 立方毫米    |
| part_mass_std   | 零件质量标准差 | 克          |

### 向量特征

- **形状特征**：768 维 → PCA 降维至 64 维
- **语义特征**：1024 维 → PCA 降维至 64 维

### 融合特征

通过加权拼接合并所有特征类型：

- 形状特征权重：0.3
- 语义特征权重：0.2
- 结构化特征权重：0.5

## 使用方法

### 基本使用

```bash
# 提取所有装配体特征（默认处理）
python scripts/extract_assembly_features.py

# 限制装配体数量
python scripts/extract_assembly_features.py --max-assemblies 100

# 指定输出目录
python scripts/extract_assembly_features.py --output-dir my_dataset

# 启用特征融合
python scripts/extract_assembly_features.py --enable-fusion
```

### 高级选项

```bash
# 不进行特征处理（保持原始维度）
python scripts/extract_assembly_features.py --no-process

# 仅加载和测试现有特征
python scripts/extract_assembly_features.py --load-only

# 显示装配体数据库统计信息
python scripts/extract_assembly_features.py --show-stats

# 指定特定装配体ID
python scripts/extract_assembly_features.py --assembly-ids id1 id2 id3
```

### VS Code 任务

在 VS Code 中可以通过以下任务快速执行：

1. **Extract Assembly Features**: 提取 100 个装配体特征
2. **Extract Assembly Features (Sample)**: 提取 10 个装配体特征（快速测试）
3. **Test Assembly Feature Extractor**: 运行完整测试套件
4. **Show Assembly Statistics**: 显示数据库统计信息

### 编程接口

```python
from src.extractors.assembly_feature_extractor import AssemblyFeatureExtractor

# 创建提取器
extractor = AssemblyFeatureExtractor()

# 提取所有特征
features = extractor.extract_all_features(
    include_shape_features=True,
    include_semantic_features=True,
    process_features=True,
    enable_fusion=True
)

# 保存特征
extractor.save_features(features, 'assembly_features.pkl')
extractor.save_scalers('assembly_scalers.pkl')

# 获取单个装配体特征
assembly_id = "your_assembly_id"
single_features = extractor.get_assembly_features_by_ids([assembly_id])

# 从Milvus获取向量特征
vectors = extractor.get_assembly_vector_from_milvus(assembly_id)
```

## 输出文件

### assembly_features.pkl

包含提取的装配体特征的 pickle 文件，数据结构如下：

```python
{
    'assembly_ids': [装配体ID列表],
    'structural': np.array,     # 结构化特征矩阵 (n_assemblies, 12)
    'shape': np.array,          # 形状特征矩阵 (n_assemblies, 64)
    'semantic': np.array,       # 语义特征矩阵 (n_assemblies, 64)
    'fused_features': np.array, # 融合特征矩阵 (n_assemblies, 140) - 可选
    'metadata': {               # 元数据信息
        'processing_enabled': bool,
        'fusion_enabled': bool,
        'feature_dimensions': dict
    }
}
```

### assembly_feature_scalers.pkl

包含标准化器和 PCA 模型的 pickle 文件，用于后续特征转换。

## 特征处理流程

1. **数据提取**

   - 从 PostgreSQL 提取结构化特征
   - 从 Milvus 提取形状和语义向量

2. **特征对齐**

   - 去除缺失特征的装配体
   - 确保所有特征类型的装配体 ID 一致

3. **标准化处理**

   - 结构化特征：Z-score 标准化
   - 向量特征：保持原始分布

4. **降维处理**

   - 形状特征：768 维 → 64 维 (PCA)
   - 语义特征：1024 维 → 64 维 (PCA)
   - 结构化特征：保持 12 维

5. **归一化处理**

   - 所有特征类型：L2 归一化

6. **特征融合（可选）**
   - 加权拼接：形状(0.3) + 语义(0.2) + 结构化(0.5)

## 配置选项

### 默认配置

```python
FEATURE_EXTRACTION_CONFIG = {
    'milvus_collection_name': 'cad_collection_en',
    'structural_feature_dim': 12,         # 结构化特征维度
    'shape_feature_dim': 768,            # 形状特征维度
    'semantic_feature_dim': 1024,        # 语义特征维度
    'pca_dim': 64,                       # PCA降维目标维度
    'batch_size': 1000,                  # 批处理大小
    'max_assemblies_per_batch': 100,     # 每批最大装配体数
    'feature_weights': {                 # 特征权重
        'shape': 0.3,
        'semantic': 0.2,
        'structural': 0.5
    }
}
```

### 特征列映射

```python
FEATURE_COLUMNS = {
    'numerical_features': [
        'length', 'width', 'height', 'area', 'volume',
        'density', 'mass', 'part_count',
        'joints_count', 'contacts_count',
        'part_volume_std', 'part_mass_std'
    ],
    'milvus_vector_fields': {
        'shape_vector': 'shape_vector',
        'semantic_vector': 'dense_vector',
    }
}
```

## 测试和验证

### 运行测试

```bash
# 基本测试
python test_scripts/test_assembly_feature_extractor.py

# 详细输出
python test_scripts/test_assembly_feature_extractor.py --verbose

# 指定测试样本大小
python test_scripts/test_assembly_feature_extractor.py --sample-size 20
```

### 测试内容

- 数据库连接测试
- Milvus 连接测试
- 结构化特征提取测试
- 向量特征提取测试
- 特征处理测试
- 保存和加载测试
- 单个装配体查询测试
- 配置信息测试

## 注意事项

1. **数据依赖**

   - PostgreSQL 中需要有`assemblies`表
   - Milvus 中需要有`cad_collection_en`集合
   - 装配体 ID 在两个数据源中需要一致

2. **内存使用**

   - 大量装配体可能消耗大量内存
   - 建议使用`--max-assemblies`参数限制数量

3. **特征对齐**

   - 只有同时存在于所有特征类型中的装配体才会保留
   - 缺失特征的装配体会被自动过滤

4. **PCA 模型**

   - PCA 模型在第一次使用时训练
   - 需要保存和加载以保持一致性

5. **权重调整**
   - 特征权重可以根据应用需求调整
   - 建议基于实际效果进行优化

## 故障排除

### 常见问题

1. **数据库连接失败**

   - 检查 PostgreSQL 配置
   - 确认网络连接和权限

2. **Milvus 连接失败**

   - 检查 Milvus 服务状态
   - 确认集合名称和字段名称

3. **内存不足**

   - 减少`--max-assemblies`参数
   - 使用批处理模式

4. **特征维度不匹配**
   - 检查 Milvus 中的向量维度
   - 确认 PCA 模型是否正确训练

### 日志分析

程序提供详细的日志输出，包括：

- 特征提取进度
- 数据质量统计
- 错误和警告信息
- 性能指标

通过分析日志可以快速定位和解决问题。

## 性能优化

1. **批处理**：使用合适的批处理大小
2. **缓存**：保存和重用标准化器和 PCA 模型
3. **过滤**：预先过滤无效数据
4. **并行**：考虑并行处理大量数据

## 扩展开发

装配体特征提取器采用模块化设计，易于扩展：

1. **新特征类型**：添加新的特征提取方法
2. **新处理算法**：实现新的特征处理流程
3. **新存储后端**：支持其他数据库和存储系统
4. **新评估指标**：添加特征质量评估方法
