# MCQ-Bench 基准数据集生成器

## 概述

本工具用于生成面向真实工业应用场景的多模态复杂查询基准数据集（Multi-modal Complex Query Benchmark, MCQ-Bench）。

## 功能特点

- **自动化生成**：基于 LLM 自动生成查询，无需手工编写
- **多模态支持**：支持属性、结构、语义三种单模态查询及其组合
- **真实数据驱动**：基于真实的 CAD 装配体数据生成查询
- **结构化输出**：生成标准化的 JSON 格式查询文件
- **可扩展性**：支持自定义提示词和查询类型

## 查询类型

### 单模态查询

1. **属性查询**：基于物理属性（质量、尺寸、材质、零件数量等）
2. **结构查询**：基于装配体结构和组成关系
3. **语义查询**：基于功能用途和应用场景

### 多模态查询

1. **两两组合**：属性+结构、属性+语义、结构+语义
2. **三类融合**：属性+结构+语义

## 使用方法

### 基本使用

```bash
# 运行生成器
python scripts/generate_query_dataset/generate_mcq_bench.py
```

### 环境要求

- Python 3.8+
- PostgreSQL 数据库（装配体和零件数据）
- Neo4j 数据库（装配体结构数据）
- 配置好的 LLM 服务

### 配置要求

确保在`src/config.py`中正确配置：

```python
# PostgreSQL配置
POSTGRES_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'dbname': 'postgres',
    'user': 'postgres',
    'password': 'your_password'
}

# Neo4j配置
NEO4J_URI = "bolt://localhost:7687"
NEO4J_USER = "neo4j"
NEO4J_PASSWORD = "your_password"

# LLM配置
LLM_API_URL = "your_llm_endpoint"
LLM_API_KEY = "your_api_key"
```

## 输出结构

### 文件组织

```
data/benchmark/
├── {assembly_id_1}/
│   ├── single_modal_query.json    # 9个单模态查询
│   └── Hybrid_query.json          # 4个多模态查询
├── {assembly_id_2}/
│   ├── single_modal_query.json
│   └── Hybrid_query.json
└── generation_statistics.json     # 生成统计信息
```

### 查询格式

#### 单模态查询格式

```json
{
  "query_id": "Attribute_001",
  "query_type": "Attribute",
  "natural_language_prompt": "查找质量小于5kg的装配体",
  "ground_truth_plan": {
    "sub_queries": [
      {
        "agent": "属性查询智能体",
        "task_params": {
          "query_text": "质量小于5kg"
        }
      }
    ],
    "fusion_strategy": "NONE"
  },
  "ground_truth_results": [
    {
      "uuid": "assembly_uuid_here"
    }
  ]
}
```

#### 多模态查询格式

```json
{
  "query_id": "Hybrid_001",
  "query_type": "Hybrid:Structure+Attribute",
  "natural_language_prompt": "我需要一个包含轴承且质量小于3kg的装配体",
  "ground_truth_plan": {
    "sub_queries": [
      {
        "agent": "结构关系查询智能体",
        "task_params": {
          "query_text": "包含轴承"
        }
      },
      {
        "agent": "属性查询智能体",
        "task_params": {
          "query_text": "质量小于3kg"
        }
      }
    ],
    "fusion_strategy": "INTERSECT"
  },
  "ground_truth_results": [
    {
      "uuid": "assembly_uuid_here"
    }
  ]
}
```

## 生成统计

生成完成后，会输出详细的统计信息：

- 总装配体数量：100
- 单模态查询总数：900（100×9）
- 多模态查询总数：400（100×4）
- 查询总数：1300
- 各类型查询分布
- 成功率和失败信息

## 自定义扩展

### 修改提示词

可以编辑`prompts/`目录下的提示词文件：

- `attribute_query_prompt.py`：属性查询提示词
- `structure_query_prompt.py`：结构查询提示词
- `semantic_query_prompt.py`：语义查询提示词
- `hybrid_query_prompt.py`：混合查询提示词

### 修改生成数量

在`generate_mcq_bench.py`中修改：

```python
# 修改装配体数量
generator = MCQBenchGenerator(num_assemblies=200)

# 修改单模态查询数量
single_modal_queries = self._generate_single_modal_queries(assembly_info, count=5)
```

## 常见问题

### 1. 数据库连接失败

检查数据库配置和网络连接：

```bash
# 测试PostgreSQL连接
psql -h localhost -p 5432 -U postgres -d postgres

# 测试Neo4j连接
cypher-shell -a bolt://localhost:7687 -u neo4j -p your_password
```

### 2. LLM 调用失败

检查 LLM 配置和 API 状态：

```python
from src.models.LLM import MultiProviderLLM
llm = MultiProviderLLM()
response = llm.generate_text("测试")
```

### 3. 生成的查询质量不佳

- 调整提示词内容
- 使用更强的 LLM 模型
- 增加更多上下文信息

## 许可证

本项目遵循 MIT 许可证。
