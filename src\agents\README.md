# 多智能体系统 (Multi-Agent System)

本系统实现了三个专业检索智能体，用于 CAD 装配体的多模态查询和检索。

## 系统架构

### 1. 几何和语义查询智能体 (GeometrySemanticAgent)

- **数据库**: Milvus 向量数据库
- **功能**: 相似度搜索
- **支持的查询类型**:
  - 几何相似性检索：基于 3D 形状的视觉特征向量
  - 深层语义检索：基于文本描述的语义理解
  - 混合检索：结合文本语义和几何形状

### 2. 属性筛选智能体 (StructuredDataAgent)

- **数据库**: PostgreSQL 关系型数据库
- **功能**: Text2SQL，精确数据查询
- **支持的查询类型**:
  - 基于物理属性的精确查询（质量、体积、材料等）
  - 基于制造特征的查询（孔的数量、直径等）
  - 复杂的 SQL 查询生成和执行

### 3. 结构拓扑智能体 (StructuralRelationshipAgent)

- **数据库**: Neo4j 图数据库
- **功能**: Text2Cypher，结构关系查询
- **支持的查询类型**:
  - BOM 层级关系查询
  - 装配体-零件关系遍历
  - 零件特征关系查询
  - 复杂图模式匹配

## 快速开始

### 1. 环境配置

确保以下数据库服务正在运行：

- Milvus (默认端口: 19530)
- PostgreSQL (默认端口: 5434)
- Neo4j (默认端口: 7687)

### 2. 配置文件

在项目根目录的`.env`文件中配置数据库连接信息：

```bash
# Neo4j配置
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=password123

# PostgreSQL配置
POSTGRES_HOST=localhost
POSTGRES_PORT=5434
POSTGRES_DB=postgres
POSTGRES_USER=postgres
POSTGRES_PASSWORD=123456

# Milvus配置
MILVUS_HOST=localhost
MILVUS_PORT=19530

# LLM配置
LLM_API_URL=http://your-llm-endpoint/v1
LLM_API_KEY=your-api-key
DEFAULT_LLM_MODEL=your-model-name
```

### 3. 基本使用

#### 使用智能体管理器（推荐）

```python
import asyncio
from src.agents.agent_manager import AgentManager

async def main():
    async with AgentManager() as manager:
        # 1. 文本语义搜索
        result = await manager.execute_text_search(
            query_text="圆柱形零件",
            top_k=5
        )

        # 2. 结构化数据查询
        result = await manager.execute_structured_query(
            query_text="查找质量大于100克的钢制零件"
        )

        # 3. 结构关系查询
        result = await manager.execute_structural_query(
            query_text="查找所有装配体中的零件"
        )

        # 4. 多模态查询
        result = await manager.execute_multimodal_query(
            text_query="轴承组件",
            material_filter="steel",
            mass_range=(10.0, 500.0),
            top_k=5
        )

asyncio.run(main())
```

#### 直接使用单个智能体

```python
import asyncio
from src.agents.geometry_semantic_agent import GeometrySemanticAgent

async def main():
    agent = GeometrySemanticAgent()

    try:
        await agent.connect()

        # 文本搜索
        results = await agent.search_by_text("齿轮装配体", top_k=5)

        # 形状搜索
        shape_vector = [...] # 768维向量
        results = await agent.search_by_shape(shape_vector, top_k=5)

        # 混合搜索
        results = await agent.search_hybrid_with_shape(
            query_text="支架零件",
            shape_vector=shape_vector,
            top_k=5,
            shape_weight=0.3
        )

    finally:
        await agent.disconnect()

asyncio.run(main())
```

## API 参考

### AgentManager

主要方法：

- `execute_text_search(query_text, top_k, use_reranker)`: 执行文本语义搜索
- `execute_shape_search(shape_vector, top_k)`: 执行形状几何搜索
- `execute_hybrid_search(query_text, shape_vector, top_k, shape_weight)`: 执行混合搜索
- `execute_structured_query(query_text, generate_sql)`: 执行结构化数据查询
- `execute_structural_query(query_text, generate_cypher)`: 执行结构关系查询
- `execute_property_filter(material, mass_min, mass_max, ...)`: 执行属性过滤查询
- `execute_multimodal_query(...)`: 执行多模态查询

### GeometrySemanticAgent

主要方法：

- `search_by_text(query_text, top_k, use_reranker)`: 文本语义搜索
- `search_by_shape(shape_vector, top_k)`: 形状几何搜索
- `search_hybrid_with_shape(query_text, shape_vector, top_k, shape_weight)`: 混合搜索

### StructuredDataAgent

主要方法：

- `text_to_sql(query_text)`: 将自然语言转换为 SQL
- `query_by_text(query_text, generate_sql)`: 基于文本执行查询
- `query_parts_by_properties(material, mass_min, mass_max, ...)`: 基于属性查询零件

### StructuralRelationshipAgent

主要方法：

- `text_to_cypher(query_text)`: 将自然语言转换为 Cypher
- `query_by_text(query_text, generate_cypher)`: 基于文本执行图查询
- `find_assembly_parts(assembly_name)`: 查找装配体中的零件
- `find_part_parents(part_name)`: 查找零件的父装配体
- `find_parts_with_features(feature_type)`: 查找具有特定特征的零件

## 测试和演示

### 快速测试

运行快速测试脚本来验证系统功能：

```bash
python tests/unit/quick_test_agents.py
```

### 完整演示

运行完整演示脚本来了解所有功能：

```bash
python tests/unit/test_mas_agents.py
```

## 数据模型

### 查询任务模型

- `TextSearchTask`: 文本语义搜索任务
- `ShapeSearchTask`: 形状几何搜索任务
- `HybridSearchTask`: 混合搜索任务
- `Text2SQLTask`: 自然语言转 SQL 任务
- `Text2CypherTask`: 自然语言转 Cypher 任务
- `PropertyFilterTask`: 属性过滤任务
- `MultiModalQueryTask`: 多模态查询任务

### 查询结果模型

- `EnhancedQueryResult`: 增强的查询结果
- `SearchResultItem`: 搜索结果项

## 扩展和定制

### 添加新的查询类型

1. 在`data_models.py`中定义新的任务模型
2. 在相应的智能体中实现处理逻辑
3. 在`AgentManager`中添加统一接口

### 自定义融合策略

在`AgentManager._fuse_results()`方法中实现自定义的结果融合逻辑。

### 调整权重和阈值

修改各智能体中的权重配置来优化查询结果的相关性。

## 注意事项

1. **数据库连接**: 确保所有数据库服务正常运行并可访问
2. **向量维度**: 形状向量必须是 768 维（CLIP 模型输出）
3. **LLM 配置**: 确保 LLM 服务可用，用于 Text2SQL 和 Text2Cypher 功能
4. **错误处理**: 系统会自动处理连接错误和查询失败，返回相应的错误信息
5. **性能优化**: 大规模查询时建议调整批处理大小和并发参数

## 故障排除

### 常见问题

1. **连接失败**: 检查数据库服务状态和配置信息
2. **查询超时**: 增加查询超时时间或优化查询条件
3. **向量维度错误**: 确保输入向量维度正确
4. **SQL/Cypher 生成失败**: 检查 LLM 服务配置和网络连接

### 日志调试

启用详细日志来诊断问题：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 贡献指南

欢迎提交问题报告和功能请求。在提交代码前，请确保：

1. 代码符合项目的编码规范
2. 添加适当的单元测试
3. 更新相关文档
4. 运行现有测试确保无回归

## 许可证

本项目遵循项目主许可证。
