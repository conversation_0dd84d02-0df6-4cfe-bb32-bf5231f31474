#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试文本嵌入模型的功能
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from src.models.text_embedding import TextEmbedding, get_default_text_embedding
from src.config import Config

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_single_embedding():
    """测试单个文本的嵌入向量生成"""
    logger.info("测试单个文本的嵌入向量生成")
    
    # 获取文本嵌入实例
    text_embedding = get_default_text_embedding()
    
    # 测试文本
    text = "这是一个用于测试文本嵌入功能的示例句子。"
    
    # 获取嵌入向量
    embedding = text_embedding.get_embedding(text)
    
    # 输出向量维度
    logger.info(f"文本嵌入向量维度: {len(embedding)}")
    logger.info(f"向量前10个元素: {embedding[:10]}")
    
    return embedding


def test_batch_embedding():
    """测试批量文本的嵌入向量生成"""
    logger.info("测试批量文本的嵌入向量生成")
    
    # 获取文本嵌入实例
    text_embedding = get_default_text_embedding()
    
    # 测试文本列表
    texts = [
        "这是第一个测试文本，用于验证批量嵌入功能。",
        "这是第二个测试文本，内容与第一个相似。",
        "这是第三个测试文本，内容与前两个完全不同。"
    ]
    
    # 获取嵌入向量列表
    embeddings = text_embedding.get_embeddings(texts)
    
    # 输出向量信息
    logger.info(f"批量嵌入向量数量: {len(embeddings)}")
    for i, embedding in enumerate(embeddings):
        logger.info(f"文本{i+1}嵌入向量维度: {len(embedding)}")
    
    return embeddings


def test_text_similarity():
    """测试文本相似度计算"""
    logger.info("测试文本相似度计算")
    
    # 获取文本嵌入实例
    text_embedding = get_default_text_embedding()
    
    # 测试文本
    text1 = """
	室内加热器；这是一款用于室内取暖的加热设备。整体呈阶梯状结构，顶部设有烟囱孔，外壳由多种材料组合而成，兼具保温和美观功能。；组件：；内部钢结构：作为整个加热器的
承载框架和燃烧室，提供强度支撑；外壳石材部分：构成加热器主要外观，具有良好的保温性能；外壳底部石材：构成加热器底座，提供稳定支撑；外壳顶部石材：构成加热器上部，
与底部石材共同形成保温结构；外壳侧面石材：构成加热器两侧，与顶部和底部石材共同形成保温结构；外壳布料部分：覆盖在石材结构上，进一步提升保温效果和美观度；燃烧管：
用于燃料燃烧，顶部设有烟囱孔；金属底座：支撑整个加热器，提供稳定性；材料工艺：主要材料包括钢材、石材（花岗岩和野石）以及布料。钢结构提供强度，石材具有良好的保温
性能，布料进一步提升保温效果。整体采用组合结构，便于安装和维护。
"""
    text2 = """
具有良好的保温性能；外壳底部石材：构成加热器底座，提供稳定支撑；外壳顶部石材：构成加热器上部，
与底部石材共同形成保温结构；外壳侧面石材：构成加热器两侧，与顶部和底部石材共同形成保温结构；外壳布料部分：覆盖在石材结构上，进一步提升保温效果和美观度；燃烧管：
用于燃料燃烧，顶部设有烟囱孔；金属底座：支撑整个加热器，提供稳定性；
"""
    
    # 计算相似度
    similarity1 = text_embedding.compute_similarity(text1, text2)
    
    # 输出相似度
    logger.info(f"相似文本的余弦相似度: {similarity1:.4f}")
    
    return similarity1

def main():
    """主函数"""
    logger.info("开始测试文本嵌入模型")
    logger.info(f"使用API URL: {Config.TEXT_EMBEDDING_API_URL}")
    logger.info(f"使用模型: {Config.TEXT_EMBEDDING_MODEL}")
    
    # # 测试单个文本嵌入
    # test_single_embedding()
    
    # # 测试批量文本嵌入
    # test_batch_embedding()
    
    # 测试文本相似度
    test_text_similarity()
    

    
    logger.info("文本嵌入模型测试完成")


if __name__ == "__main__":
    main() 