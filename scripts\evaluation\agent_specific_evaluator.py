#!/usr/bin/env python3
"""
特定智能体评估器
用于评估单个特定智能体的查询准确率
"""

import os
import sys
import json
import asyncio
import time
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import logging

# 添加项目根目录到系统路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.append(project_root)

from src.agents.attribute_filtering_agent import AttributeFilteringAgent
from src.agents.structural_topology_agent import StructuralTopologyAgent
from src.agents.geometry_semantic_agent import GeometrySemanticAgent
from src.agents.data_models import (
    UnifiedStructuredDataTask,
    UnifiedStructuralQueryTask,
    UnifiedGeometrySemanticTask
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@dataclass
class AgentEvaluationResult:
    """智能体评估结果"""
    agent_name: str
    query_type: str
    total_queries: int
    correct_predictions: int
    accuracy: float
    avg_execution_time: float
    failed_queries: int
    error_rate: float
    detailed_results: List[Dict[str, Any]]


class AgentSpecificEvaluator:
    """特定智能体评估器"""
    
    def __init__(self, benchmark_dir: str = "data/benchmark"):
        """
        初始化评估器
        
        Args:
            benchmark_dir: 基准测试数据目录
        """
        self.benchmark_dir = Path(benchmark_dir)
        
        # 智能体映射
        self.agent_mapping = {
            "attribute": {
                "name": "属性查询智能体",
                "class": AttributeFilteringAgent,
                "task_class": UnifiedStructuredDataTask,
                "query_types": ["Attribute"]
            },
            "structural": {
                "name": "结构关系查询智能体", 
                "class": StructuralTopologyAgent,
                "task_class": UnifiedStructuralQueryTask,
                "query_types": ["Structure"]
            },
            "semantic": {
                "name": "几何和语义查询智能体",
                "class": GeometrySemanticAgent,
                "task_class": UnifiedGeometrySemanticTask,
                "query_types": ["Semantic"]
            }
        }
    
    async def evaluate_agent(self, 
                           agent_type: str,
                           max_assemblies: Optional[int] = None,
                           top_k: int = 10) -> AgentEvaluationResult:
        """
        评估特定智能体
        
        Args:
            agent_type: 智能体类型 ("attribute", "structural", "semantic")
            max_assemblies: 最大评估装配体数量
            top_k: 每个查询返回的结果数量
            
        Returns:
            智能体评估结果
        """
        if agent_type not in self.agent_mapping:
            raise ValueError(f"不支持的智能体类型: {agent_type}")
        
        agent_info = self.agent_mapping[agent_type]
        agent_name = agent_info["name"]
        agent_class = agent_info["class"]
        task_class = agent_info["task_class"]
        target_query_types = agent_info["query_types"]
        
        logger.info(f"开始评估 {agent_name}...")
        
        # 初始化智能体
        agent = agent_class()
        await agent.connect()
        
        # 收集所有相关查询
        all_queries = []
        assembly_dirs = [d for d in self.benchmark_dir.iterdir() 
                        if d.is_dir() and d.name != "generation_statistics.json"]
        
        if max_assemblies:
            assembly_dirs = assembly_dirs[:max_assemblies]
        
        for assembly_dir in assembly_dirs:
            single_modal_file = assembly_dir / "single_modal_query.json"
            
            if not single_modal_file.exists():
                continue
                
            try:
                with open(single_modal_file, 'r', encoding='utf-8') as f:
                    queries = json.load(f)
                    
                # 筛选目标查询类型
                for query in queries:
                    if query.get('query_type') in target_query_types:
                        all_queries.append(query)
                        
            except Exception as e:
                logger.error(f"加载装配体 {assembly_dir.name} 的查询失败: {e}")
                continue
        
        logger.info(f"找到 {len(all_queries)} 个 {agent_name} 相关查询")
        
        # 评估所有查询
        detailed_results = []
        correct_count = 0
        failed_count = 0
        execution_times = []
        
        for i, query in enumerate(all_queries):
            try:
                result = await self._evaluate_single_query(
                    agent, task_class, query, top_k
                )
                detailed_results.append(result)
                
                if result['is_correct']:
                    correct_count += 1
                    
                if result['error_message']:
                    failed_count += 1
                else:
                    execution_times.append(result['execution_time'])
                
                logger.info(f"查询 {i+1}/{len(all_queries)} 完成: "
                          f"{'正确' if result['is_correct'] else '错误'}")
                
                # 添加延迟
                await asyncio.sleep(0.5)
                
            except Exception as e:
                logger.error(f"评估查询 {query.get('query_id', 'unknown')} 失败: {e}")
                failed_count += 1
                detailed_results.append({
                    'query_id': query.get('query_id', 'unknown'),
                    'query_type': query.get('query_type', 'unknown'),
                    'natural_language_prompt': query.get('natural_language_prompt', ''),
                    'ground_truth_uuids': [],
                    'predicted_uuids': [],
                    'is_correct': False,
                    'execution_time': 0.0,
                    'error_message': str(e)
                })
        
        # 计算统计信息
        total_queries = len(all_queries)
        accuracy = correct_count / total_queries if total_queries > 0 else 0.0
        error_rate = failed_count / total_queries if total_queries > 0 else 0.0
        avg_execution_time = sum(execution_times) / len(execution_times) if execution_times else 0.0
        
        return AgentEvaluationResult(
            agent_name=agent_name,
            query_type=", ".join(target_query_types),
            total_queries=total_queries,
            correct_predictions=correct_count,
            accuracy=accuracy,
            avg_execution_time=avg_execution_time,
            failed_queries=failed_count,
            error_rate=error_rate,
            detailed_results=detailed_results
        )
    
    async def _evaluate_single_query(self, agent, task_class, query: Dict[str, Any], top_k: int) -> Dict[str, Any]:
        """评估单个查询"""
        query_id = query['query_id']
        query_type = query['query_type']
        natural_language_prompt = query['natural_language_prompt']
        
        # 提取ground truth UUIDs
        ground_truth_uuids = []
        for gt_result in query.get('ground_truth_results', []):
            if 'uuid' in gt_result:
                ground_truth_uuids.append(gt_result['uuid'])
        
        start_time = time.time()
        
        try:
            # 创建任务并执行
            task = task_class(
                task_id=query_id,
                query_text=natural_language_prompt,
                top_k=top_k
            )
            
            result = await agent.execute_task(task)
            execution_time = time.time() - start_time
            
            # 提取预测的UUIDs
            predicted_uuids = []
            if result.status == 'success' and result.results:
                predicted_uuids = [item.uuid for item in result.results]
            
            # 判断是否正确
            is_correct = any(uuid in predicted_uuids for uuid in ground_truth_uuids)
            
            return {
                'query_id': query_id,
                'query_type': query_type,
                'natural_language_prompt': natural_language_prompt,
                'ground_truth_uuids': ground_truth_uuids,
                'predicted_uuids': predicted_uuids,
                'is_correct': is_correct,
                'execution_time': execution_time,
                'error_message': result.error_message if result.status != 'success' else None
            }
            
        except Exception as e:
            execution_time = time.time() - start_time
            return {
                'query_id': query_id,
                'query_type': query_type,
                'natural_language_prompt': natural_language_prompt,
                'ground_truth_uuids': ground_truth_uuids,
                'predicted_uuids': [],
                'is_correct': False,
                'execution_time': execution_time,
                'error_message': str(e)
            }
    
    def print_agent_result(self, result: AgentEvaluationResult):
        """打印智能体评估结果"""
        print("\n" + "="*60)
        print(f"{result.agent_name} 评估结果")
        print("="*60)
        print(f"查询类型: {result.query_type}")
        print(f"总查询数: {result.total_queries}")
        print(f"正确预测数: {result.correct_predictions}")
        print(f"准确率: {result.accuracy:.2%}")
        print(f"平均执行时间: {result.avg_execution_time:.2f}秒")
        print(f"失败查询数: {result.failed_queries}")
        print(f"错误率: {result.error_rate:.2%}")
        print("="*60)
    
    def save_agent_result(self, result: AgentEvaluationResult, output_file: str):
        """保存智能体评估结果"""
        result_data = {
            'agent_name': result.agent_name,
            'query_type': result.query_type,
            'total_queries': result.total_queries,
            'correct_predictions': result.correct_predictions,
            'accuracy': result.accuracy,
            'avg_execution_time': result.avg_execution_time,
            'failed_queries': result.failed_queries,
            'error_rate': result.error_rate,
            'detailed_results': result.detailed_results
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"智能体评估结果已保存到: {output_file}")


async def main():
    """主函数 - 评估所有智能体"""
    evaluator = AgentSpecificEvaluator()
    
    agent_types = ["attribute", "structural", "semantic"]
    
    for agent_type in agent_types:
        try:
            result = await evaluator.evaluate_agent(
                agent_type=agent_type,
                max_assemblies=5,  # 测试用，评估5个装配体
                top_k=10
            )
            
            evaluator.print_agent_result(result)
            
            # 保存结果
            output_dir = Path("results/evaluation")
            output_dir.mkdir(parents=True, exist_ok=True)
            
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            output_file = output_dir / f"{agent_type}_agent_evaluation_{timestamp}.json"
            evaluator.save_agent_result(result, str(output_file))
            
        except Exception as e:
            logger.error(f"评估 {agent_type} 智能体失败: {e}")


if __name__ == "__main__":
    asyncio.run(main())
