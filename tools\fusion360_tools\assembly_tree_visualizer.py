import json
import os
from pathlib import Path
import networkx as nx
import matplotlib.pyplot as plt
from typing import Dict, Any

class AssemblyTreeVisualizer:
    def __init__(self, assembly_file: str):
        """
        初始化装配体树可视化器
        Args:
            assembly_file: 装配体JSON文件的路径
        """
        self.assembly_file = Path(assembly_file)
        if not self.assembly_file.exists():
            raise FileNotFoundError(f"找不到装配体文件: {assembly_file}")
        
        with open(self.assembly_file, 'r', encoding='utf-8') as f:
            self.assembly_data = json.load(f)
            
        self.tree_data = self.assembly_data.get('tree', {})
        self.components = self.assembly_data.get('components', {})
        self.occurrences = self.assembly_data.get('occurrences', {})
        self.properties = self.assembly_data.get('properties', {})
        
    def extract_tree_structure(self) -> Dict[str, Any]:
        """
        提取装配体的树形结构
        Returns:
            包含树形结构的字典
        """
        def process_node(node_data: Dict, parent_id: str = None) -> Dict:
            result = {}
            for occ_id, children in node_data.items():
                if occ_id in self.occurrences:
                    occ_data = self.occurrences[occ_id]
                    comp_id = occ_data.get('component')
                    if comp_id in self.components:
                        comp_data = self.components[comp_id]
                        # 检查是否为子装配体（包含其他组件）
                        is_subassembly = bool(children)
                        node_info = {
                            'id': occ_id,
                            'name': occ_data.get('name', ''),
                            'component_name': comp_data.get('name', ''),
                            'is_grounded': occ_data.get('is_grounded', False),
                            'is_visible': occ_data.get('is_visible', True),
                            'is_subassembly': is_subassembly,
                            'children': process_node(children, occ_id) if children else {}
                        }
                        result[occ_id] = node_info
            return result

        # 创建根节点（装配体名称）
        root_name = self.properties.get('name', '未命名装配体')
        root_data = self.tree_data.get('root', {})
        tree_structure = {
            'root': {
                'id': 'root',
                'name': root_name,
                'component_name': root_name,
                'is_grounded': True,
                'is_visible': True,
                'is_subassembly': True,
                'children': process_node(root_data)
            }
        }
        return tree_structure

    def save_tree_json(self, output_file: str):
        """
        将树形结构保存为JSON文件
        Args:
            output_file: 输出JSON文件的路径
        """
        tree_structure = self.extract_tree_structure()
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(tree_structure, f, indent=2, ensure_ascii=False)

    def visualize_tree(self, output_image: str):
        """
        生成树形结构的可视化图片
        Args:
            output_image: 输出图片的路径
        """
        G = nx.DiGraph()
        
        def add_nodes_edges(node_data: Dict, parent_id: str = None):
            for node_id, node_info in node_data.items():
                # 为子装配体和零件使用不同的标签格式
                # if node_info['is_subassembly']:
                #     label = f"[Assembly]\\n{node_info['name']}"
                # else:
                #     label = f"[Part]\\n{node_info['name']}"
                label = f"{node_info['name']}"
                
                G.add_node(node_id, label=label, is_subassembly=node_info['is_subassembly'])
                if parent_id:
                    G.add_edge(parent_id, node_id)
                if node_info['children']:
                    add_nodes_edges(node_info['children'], node_id)

        tree_structure = self.extract_tree_structure()
        add_nodes_edges(tree_structure)

        plt.figure(figsize=(15, 10))
        # 使用分层布局
        root_node = [n for n, d in G.in_degree() if d == 0][0]
        pos = self.hierarchy_pos(G, root_node, vert_gap=0.25)
        
        # 分别绘制子装配体和零件节点
        subassembly_nodes = [n for n, d in G.nodes(data=True) if d.get('is_subassembly')]
        part_nodes = [n for n, d in G.nodes(data=True) if not d.get('is_subassembly')]
        
        # 绘制子装配体节点（蓝色）
        nx.draw_networkx_nodes(G, pos, nodelist=subassembly_nodes,
                             node_color='lightblue', node_size=3000, alpha=0.7)
        
        # 绘制零件节点（浅绿色）
        nx.draw_networkx_nodes(G, pos, nodelist=part_nodes,
                             node_color='lightgreen', node_size=2500, alpha=0.7)
        
        # 绘制边
        nx.draw_networkx_edges(G, pos, edge_color='gray', 
                             arrows=True, arrowsize=20)
        
        # 添加标签
        labels = nx.get_node_attributes(G, 'label')
        nx.draw_networkx_labels(G, pos, labels, font_size=8)
        
        plt.title("装配体BOM结构树")
        plt.axis('off')
        plt.tight_layout()
        
        # 保存图片
        plt.savefig(output_image, dpi=300, bbox_inches='tight')
        plt.close()

    def hierarchy_pos(self, G, root, width=1., vert_gap=0.2, vert_loc=0, xcenter=0.5, pos=None, parent=None):
        '''
        生成分层布局的位置字典，使得每一层的节点在同一水平线上
        '''
        if pos is None:
            pos = {root: (xcenter, vert_loc)}
        else:
            pos[root] = (xcenter, vert_loc)
        children = list(G.successors(root))
        if len(children) != 0:
            dx = width / len(children)
            nextx = xcenter - width / 2 - dx / 2
            for child in children:
                nextx += dx
                pos = self.hierarchy_pos(G, child, width=dx, vert_gap=vert_gap,
                                    vert_loc=vert_loc - vert_gap, xcenter=nextx, pos=pos, parent=root)
        return pos

def main():
    # 示例使用
    assembly_file = r"datasets\fusion360_assembly\41032_ed481084\assembly.json"  # 替换为实际的装配体文件路径
    output_json = "assembly_tree.json"
    output_image = "assembly_tree.png"
    
    visualizer = AssemblyTreeVisualizer(assembly_file)
    visualizer.save_tree_json(output_json)
    visualizer.visualize_tree(output_image)
    print(f"树形结构已保存到: {output_json}")
    print(f"可视化图片已保存到: {output_image}")

if __name__ == "__main__":
    main() 