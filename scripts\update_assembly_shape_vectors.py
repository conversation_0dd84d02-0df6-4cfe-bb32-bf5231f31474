#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
更新零件和装配体形状向量脚本

该脚本从CLIP特征文件加载零件和装配体的形状向量，并将它们更新到Milvus数据库中。
它支持以下功能：
1. 从多个pickle文件加载CLIP特征（零件和装配体）
2. 将形状向量更新到Milvus数据库

使用方法:
    python scripts/update_assembly_shape_vectors.py --shape_embeddings dataset/clip_features.pkl,dataset/assembly_clip_features.pkl --collection cad_collection_en
"""

import os
import sys
import logging
import pickle
import argparse
import numpy as np
from tqdm import tqdm

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.utils.database.milvus_utils import MilvusManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_shape_embeddings(file_paths):
    """
    加载形状嵌入向量，支持多个文件
    参考 import_to_milvus.py 中的实现
    
    参数:
        file_paths: 形状嵌入向量文件路径，多个文件用逗号分隔
        
    返回:
        形状嵌入字典 {id: feature_vector}
    """
    shape_embeddings = {}
    
    for file_path in file_paths.split(','):
        file_path = file_path.strip()
        if not file_path:
            continue
            
        logger.info(f"正在加载形状嵌入向量: {file_path}")
        
        try:
            with open(file_path, 'rb') as f:
                features = pickle.load(f)
            
            # 根据文件名判断是零件特征还是装配体特征
            file_name = os.path.basename(file_path)
            
            # 处理不同格式的形状嵌入文件
            if "assembly" in file_name:
                # 装配体特征格式: {assembly_id: feature_vector}
                shape_embeddings.update(features)
                logger.info(f"已加载 {len(features)} 个装配体形状向量")
            else:
                # 零件特征格式: {assembly_id: {component_id: feature_vector}}
                for assembly_id, components in features.items():
                    for component_id, feature_vector in components.items():
                        # 创建唯一ID
                        component_full_id = f"{component_id}"
                        shape_embeddings[component_full_id] = feature_vector
                logger.info(f"已加载零件形状向量")
            
        except Exception as e:
            logger.error(f"加载形状嵌入向量文件 {file_path} 失败: {e}")
    
    logger.info(f"共加载 {len(shape_embeddings)} 个形状向量")
    return shape_embeddings

def update_shape_vectors(
    shape_embeddings, 
    collection_name="cad_collection_en", 
    batch_size=100,
    device="cpu"
):
    """
    更新Milvus中的零件和装配体形状向量
    
    参数:
        shape_embeddings: 形状嵌入字典 {id: feature_vector}
        collection_name: Milvus集合名称
        batch_size: 批处理大小，控制内存使用
        device: 使用的设备，'cpu'或'cuda'
        
    返回:
        更新的数量
    """
    logger.info(f"开始更新形状向量到集合 {collection_name}")
    
    # 初始化Milvus管理器
    milvus_manager = MilvusManager(device=device)
    
    # 准备要更新的数据
    ids = []
    shape_vectors = []
    
    # 处理每个特征向量
    for item_id, feature_vector in tqdm(shape_embeddings.items(), desc="处理特征"):
        try:
            # 归一化特征向量
            feature_vector = np.asarray(feature_vector, dtype=np.float32)
            
            # 检查形状向量的维度，确保是一维的768维向量
            if feature_vector.ndim > 1:
                # 如果是二维的(1,768)，转换为一维(768,)
                feature_vector = feature_vector.flatten()
            
            # 确保长度为768
            if feature_vector.shape[0] != 768:
                logger.warning(f"项目 {item_id} 的形状向量维度不是768，而是 {feature_vector.shape}，跳过更新")
                continue
            
            # 归一化
            norm = np.linalg.norm(feature_vector)
            if norm > 0:
                feature_vector = feature_vector / norm
                
            ids.append(item_id)
            shape_vectors.append(feature_vector)
            
            # 当积累了足够多的数据，执行批量更新
            if len(ids) >= batch_size:
                milvus_manager.update_cad_shape_vectors(
                    collection_name=collection_name,
                    ids=ids,
                    shape_vectors=shape_vectors
                )
                # 清空缓存数据
                ids = []
                shape_vectors = []
                
        except Exception as e:
            logger.error(f"处理项目 {item_id} 时出错: {e}")
    
    # 处理剩余的数据
    if ids:
        milvus_manager.update_cad_shape_vectors(
            collection_name=collection_name,
            ids=ids,
            shape_vectors=shape_vectors
        )
    
    logger.info(f"形状向量更新完成，总共处理 {len(shape_embeddings)} 个项目")
    return len(shape_embeddings)

def main():
    parser = argparse.ArgumentParser(description="更新Milvus零件和装配体形状向量")
    parser.add_argument('--shape_embeddings', type=str, default='dataset/clip_features.pkl,dataset/assembly_clip_features.pkl', help='形状嵌入向量文件路径，多个文件用逗号分隔')
    parser.add_argument('--collection', type=str, default='cad_collection_en', help='Milvus集合名称')
    parser.add_argument('--batch-size', type=int, default=100, help='批处理大小')
    parser.add_argument('--device', type=str, choices=['cpu', 'cuda'], default='cpu', help='计算设备')
    
    args = parser.parse_args()
    
    try:
        # 加载形状嵌入向量
        shape_embeddings = load_shape_embeddings(args.shape_embeddings)
        
        # 更新形状向量
        update_shape_vectors(
            shape_embeddings=shape_embeddings,
            collection_name=args.collection,
            batch_size=args.batch_size,
            device=args.device
        )
        
        logger.info("形状向量更新成功")
        
    except Exception as e:
        logger.error(f"更新形状向量失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 