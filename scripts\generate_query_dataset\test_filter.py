#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试装配体筛选逻辑
"""

import sys
import os

# 添加项目根目录到系统路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.append(project_root)

from utils.data_sampler import DataSampler

def test_filter():
    """测试筛选逻辑"""
    print("测试装配体筛选逻辑...")
    print("连接Neo4j数据库，查询Assembly节点及其所有子节点...")
    
    sampler = DataSampler()
    
    # 获取符合条件的装配体
    qualified_uuids = sampler.get_qualified_assemblies()
    
    print(f"符合条件的装配体数量: {len(qualified_uuids)}")
    
    if qualified_uuids:
        print(f"前5个符合条件的装配体UUID:")
        for i, uuid in enumerate(qualified_uuids[:5]):
            print(f"  {i+1}. {uuid}")
    else:
        print("没有找到符合条件的装配体")

if __name__ == "__main__":
    test_filter()
