#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
混合查询提示词模板

用于生成多模态混合查询
"""

def get_hybrid_query_prompt(assembly_info: dict, single_queries: list) -> str:
    """
    Get hybrid query generation prompt
    
    Args:
        assembly_info: Assembly information
        single_queries: Single modal query list
        
    Returns:
        Prompt string
    """
    
    # Extract basic assembly information
    name = assembly_info.get('name', 'Unknown Assembly')
    description = assembly_info.get('description', '')
    
    # Extract structural information for exact naming
    structure = assembly_info.get('structure', {})
    component_names = structure.get('component_names', [])
    all_component_names = list(set(component_names))  # 去重
    all_component_names = [name for name in all_component_names if name and name.strip()]  # 过滤空名称
    
    # Analyze single modal query types
    query_types = []
    query_texts = []
    
    for query in single_queries:
        query_type = query['query_type']
        query_text = query['ground_truth_plan']['sub_queries'][0]['task_params']['query_text']
        query_types.append(query_type)
        query_texts.append(query_text)
    
    # Determine hybrid query type
    if len(query_types) == 2:
        hybrid_type = f"Hybrid:{'+'.join(query_types)}"
    else:
        hybrid_type = f"Hybrid:{'+'.join(query_types)}"
    
    prompt = f"""
You are a professional CAD assembly multimodal query expert. Based on the following assembly information and single modal queries, generate a logically reasonable hybrid query.

Assembly Information:
- Name: {name}
- Description: {description}

**COMPLETE DATABASE COMPONENT NAMES LIST:**
Available component names that can be used in queries:
{', '.join(all_component_names[:20]) if all_component_names else 'None'}
{f'... and {len(all_component_names) - 20} more' if len(all_component_names) > 20 else ''}

Single Modal Query Information:
"""
    
    for i, (query_type, query_text) in enumerate(zip(query_types, query_texts), 1):
        prompt += f"- {query_type} Query {i}: {query_text}\n"
    
    prompt += f"""
**CRITICAL NAMING REQUIREMENT:**
When referring to any assembly, sub-assembly, or part names in the query, you MUST use the EXACT names as they appear in the database. Do NOT modify, abbreviate, or paraphrase these names. Use the complete, exact names including any special characters, numbers, or unusual formatting.

Query Requirements:
1. Query Type: {hybrid_type}
2. Logically combine the above {len(query_types)} single modal queries
3. Use appropriate connectives (such as "and", "while", "simultaneously", etc.) to combine different types of query conditions
4. Ensure clear query logic with "AND" relationships between conditions
5. Query language should be natural and fluent, conforming to engineers' expression habits
6. Use this assembly as the target answer to generate a composite query that can accurately find it
7. **MANDATORY**: When mentioning any assembly or component names, use the EXACT names from the database

Composition Guidelines:
- Attribute+Structure: Combine physical attribute conditions with structural composition conditions, using exact database names
- Attribute+Semantic: Combine physical attribute conditions with functional/application conditions, using exact database names
- Structure+Semantic: Combine structural composition conditions with functional/application conditions, using exact database names
- Three Combined: Organically combine conditions from attribute, structure, and semantic dimensions, using exact database names

Please generate a natural language hybrid query that can simultaneously satisfy multiple dimensional query conditions.

Output Format:
- Output the natural language query statement directly
- Do not include any explanations or additional information
- The statement should be concise and clear, suitable for direct use in query systems
- **ENSURE**: All component names used in the query match EXACTLY with the database names

Example References:
- "I need an assembly containing gears with mass less than 5kg" (only if "gears" is the exact database name)
- "Find assemblies for transmission with more than 20 parts"
- "I need an assembly containing bearings, for rotational transmission, with mass less than 3kg" (only if "bearings" is the exact database name)
- "Find assemblies similar to '{name}' with specific components and performance requirements" (using exact assembly name)
"""
    
    return prompt.strip()


def get_hybrid_task_params(single_queries: list) -> list:
    """
    Generate task_params for hybrid queries
    
    Args:
        single_queries: Single modal query list
        
    Returns:
        task_params list
    """
    task_params = []
    
    for query in single_queries:
        query_type = query['query_type']
        query_text = query['ground_truth_plan']['sub_queries'][0]['task_params']['query_text']
        
        # Determine agent name
        if query_type == 'Attribute':
            agent_name = 'AttributeFilteringAgent'
        elif query_type == 'Structure':
            agent_name = 'StructuralTopologyAgent'
        elif query_type == 'Semantic':
            agent_name = 'GeometrySemanticAgent'
        else:
            agent_name = 'Query Agent'
        
        task_params.append({
            'agent': agent_name,
            'task_params': {
                'query_text': query_text
            }
        })
    
    return task_params


def get_hybrid_query_type(single_queries: list) -> str:
    """
    Determine hybrid query type
    
    Args:
        single_queries: Single modal query list
        
    Returns:
        Hybrid query type string
    """
    query_types = []
    
    for query in single_queries:
        query_type = query['query_type']
        query_types.append(query_type)
    
    # Sort to ensure consistency
    query_types.sort()
    
    return f"Hybrid:{'+'.join(query_types)}"
