#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
装配体导入到Milvus脚本

此脚本执行以下操作：
1. 获取测试集中的所有装配体JSON文件
2. 从每个装配体提取描述信息
3. 将装配体ID、描述和向量存储到Milvu<PERSON>中
"""

import os
import logging
import argparse
import json
from pathlib import Path

import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入项目模块
from src.utils.file_utils import get_fusion360_test_paths
from src.extractors.fusion360_extractor import Fusion360Extractor
from src.database.milvus_utils import MilvusManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def get_assembly_description(json_file_path: str):
    """
    从装配体JSON文件获取描述信息
    
    参数:
        json_file_path: 装配体JSON文件路径
    
    返回:
        包含装配体ID、名称和描述的字典
    """
    try:
        # 使用Fusion360Extractor从JSON文件提取信息
        extractor = Fusion360Extractor(json_file_path, extract_shape_embedding=False)
        model_data = extractor.convert()
        assembly = model_data.assembly
        
        return {
            "id": assembly.assembly_id,
            "name": assembly.name,
            "description": assembly.description
        }
    except Exception as e:
        logger.error(f"处理装配体 {json_file_path} 时出错: {e}")
        return None


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="将装配体描述导入到Milvus数据库")
    parser.add_argument("--collection_name", default="assembly_collection", help="Milvus集合名称")
    parser.add_argument("--recreate", default=True, help="如果集合已存在，是否重新创建")
    parser.add_argument("--batch_size", type=int, default=16, help="嵌入批处理大小，用于控制内存占用")
    parser.add_argument("--insert_batch_size", type=int, default=16, help="插入Milvus的批处理大小")
    parser.add_argument("--device", choices=["cpu", "cuda"], default="cpu", help="使用的设备，CPU或CUDA")
    args = parser.parse_args()
    
    try:
        # 创建Milvus管理器
        milvus_manager = MilvusManager(use_reranker=True, device=args.device)
        
        # 创建集合
        collection = milvus_manager.create_assembly_collection(
            collection_name=args.collection_name,
            recreate=args.recreate
        )
        
        # 获取测试集路径
        test_paths = get_fusion360_test_paths()
        logger.info(f"找到 {len(test_paths)} 个测试集装配体")
        
        # 处理每个装配体并分批插入
        total_inserted = 0
        batch_assemblies = []
        batch_descriptions = []
        batch_assembly_ids = []
        
        for i, test_path in enumerate(test_paths):
            json_file = os.path.join(test_path, "description.json")
            
            if os.path.exists(json_file):
                # 从json文件中获取描述信息
                with open(json_file, "r", encoding="utf-8") as f:
                    desc_data = json.load(f)
                # 提取重点信息再拼成自然语言，减少 token 数
                component_desc = "组件包括："
                for comp in desc_data["components"]:
                    component_desc += f'{comp["name"]}（功能：{comp["feature"]}），'

                component_desc = component_desc.rstrip("，") + "。"
                assembly_info = desc_data["description"] + "。材料与工艺：" + desc_data["materials and techniques"] + "。" + component_desc

                # test_path的文件夹名称为装配体id
                assembly_id = os.path.basename(test_path)

                if assembly_info:
                    batch_assemblies.append(assembly_id)
                    batch_assembly_ids.append(assembly_id)  # 使用文件夹名称作为装配体ID
                    batch_descriptions.append(assembly_info)  # 使用构建的描述文本
                    logger.info(f"处理装配体 [{i+1}/{len(test_paths)}]: {assembly_id}")
                    
                    # 当批次达到指定大小或处理完最后一个装配体时插入数据
                    if len(batch_descriptions) >= args.insert_batch_size or i == len(test_paths) - 1:
                        if batch_descriptions:
                            logger.info(f"插入批次 {total_inserted//args.insert_batch_size + 1}, 大小: {len(batch_descriptions)}")
                            
                            # 插入数据到Milvus
                            inserted_count = milvus_manager.insert_assemblies(
                                args.collection_name,
                                batch_assembly_ids,
                                batch_descriptions,
                                batch_size=args.batch_size
                            )
                            
                            total_inserted += inserted_count
                            logger.info(f"当前已插入: {total_inserted} 个装配体")
                            
                            # 清空批次
                            batch_assemblies = []
                            batch_descriptions = []
                            batch_assembly_ids = []
        
        if total_inserted > 0:
            logger.info(f"成功将 {total_inserted} 个装配体描述向量插入Milvus")
        else:
            logger.warning("未找到任何装配体描述，无法创建索引")
        
        # 释放集合资源
        milvus_manager.release_collection(args.collection_name)
    
    except Exception as e:
        logger.error(f"执行过程中出错: {e}", exc_info=True)


if __name__ == "__main__":
    main() 