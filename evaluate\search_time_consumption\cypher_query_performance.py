#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Cypher查询性能测试脚本

测试不同类型的Cypher查询在Neo4j图数据库上的执行时间：
1. 查找同时包含多个特定零件的装配体 (AND 条件)
2. 查找包含任一特定组件的装配体 (OR 条件)
3. 查找包含A但不包含B的装配体 (AND NOT 条件)
4. 根据组件类型和数量筛选装配体
5. 根据组件的模糊名称进行查找

在整个装配体集合和随机子集上分别测试，并生成对比表格
"""

import os
import sys
import time
import random
import logging
import json
from typing import List, Dict, Any, Tuple, Optional
from statistics import mean, stdev
from datetime import datetime
import pandas as pd
import numpy as np
from pathlib import Path
from neo4j import GraphDatabase
from tqdm import tqdm

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.config import Config

# 配置日志
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CypherQueryPerformanceTester:
    """Cypher查询性能测试器"""
    
    def __init__(self, neo4j_config: Dict[str, str], subset_size: int = 100, queries_per_type: int = 100):
        """
        初始化Cypher查询性能测试器
        
        Args:
            neo4j_config: Neo4j数据库连接参数
            subset_size: 测试子集大小
            queries_per_type: 每种查询类型的测试语句数量
        """
        self.neo4j_config = neo4j_config
        self.subset_size = subset_size
        self.queries_per_type = queries_per_type
        self.driver = None
        self.evaluation_results = {}
        self.part_names = []
        self.assembly_uuids = []
        self.subset_assembly_uuids = []
        
    def connect(self):
        """建立Neo4j数据库连接"""
        try:
            self.driver = GraphDatabase.driver(
                self.neo4j_config["uri"],
                auth=(self.neo4j_config["user"], self.neo4j_config["password"])
            )
            
            # 测试连接
            with self.driver.session() as session:
                session.run("RETURN 1")
            logger.info("成功连接到Neo4j数据库")
            
        except Exception as e:
            logger.error(f"连接Neo4j数据库失败: {e}")
            raise
            
    def close(self):
        """关闭数据库连接"""
        if self.driver:
            self.driver.close()
            logger.info("Neo4j数据库连接已关闭")
    
    def get_graph_stats(self) -> Dict[str, Any]:
        """获取图数据库的统计信息"""
        stats_queries = {
            'assembly_count': "MATCH (a:Assembly) RETURN COUNT(a) AS count",
            'part_count': "MATCH (p:Part) RETURN COUNT(p) AS count",
            'subassembly_count': "MATCH (s:SubAssembly) RETURN COUNT(s) AS count",
            'feature_count': "MATCH (f:Feature) RETURN COUNT(f) AS count",
            'hasComponent_count': "MATCH ()-[r:hasComponent]->() RETURN COUNT(r) AS count",
            'hasFeature_count': "MATCH ()-[r:hasFeature]->() RETURN COUNT(r) AS count"
        }
        
        stats = {}
        with self.driver.session() as session:
            for key, query in stats_queries.items():
                try:
                    result = session.run(query)
                    record = result.single()
                    stats[key] = record["count"] if record else 0
                    logger.info(f"{key}: {stats[key]}")
                except Exception as e:
                    logger.error(f"获取统计信息失败 {key}: {e}")
                    stats[key] = None
                
        return stats
    
    def fetch_all_part_names(self) -> List[str]:
        """获取数据库中所有零件的名称"""
        logger.info("获取数据库中所有零件的名称...")
        with self.driver.session() as session:
            query = """
            MATCH (p:Part)
            WHERE p.name IS NOT NULL
            RETURN DISTINCT p.name AS name
            LIMIT 10000
            """
            result = session.run(query)
            part_names = [record["name"] for record in result]
            
        logger.info(f"获取到 {len(part_names)} 个不同的零件名称")
        return part_names
    
    def fetch_all_assembly_uuids(self) -> List[str]:
        """获取数据库中所有装配体的UUID"""
        logger.info("获取数据库中所有装配体的UUID...")
        with self.driver.session() as session:
            query = """
            MATCH (a:Assembly)
            WHERE a.uuid IS NOT NULL
            RETURN a.uuid AS uuid
            LIMIT 10000
            """
            result = session.run(query)
            assembly_uuids = [record["uuid"] for record in result]
            
        logger.info(f"获取到 {len(assembly_uuids)} 个装配体UUID")
        return assembly_uuids
    
    def create_assembly_subset(self) -> List[str]:
        """创建装配体子集"""
        if not self.assembly_uuids:
            self.assembly_uuids = self.fetch_all_assembly_uuids()
        
        subset_size = min(self.subset_size, len(self.assembly_uuids))
        subset_uuids = random.sample(self.assembly_uuids, subset_size)
        logger.info(f"创建了包含 {len(subset_uuids)} 个装配体的子集")
        return subset_uuids
    
    def get_parts_in_assembly(self, assembly_uuid: str) -> List[str]:
        """获取指定装配体中的所有零件名称"""
        with self.driver.session() as session:
            query = """
            MATCH (a:Assembly {uuid: $uuid})-[:hasComponent*]->(p:Part)
            WHERE p.name IS NOT NULL
            RETURN DISTINCT p.name AS name
            """
            result = session.run(query, uuid=assembly_uuid)
            parts = [record["name"] for record in result]
        return parts
    
    def get_common_parts_in_subset(self) -> Dict[str, int]:
        """统计子集中常见的零件及其出现次数"""
        part_counts = {}
        logger.info("统计子集中零件出现频率...")
        
        for uuid in tqdm(self.subset_assembly_uuids, desc="统计零件频率"):
            try:
                parts = self.get_parts_in_assembly(uuid)
                for part in parts:
                    if part in part_counts:
                        part_counts[part] += 1
                    else:
                        part_counts[part] = 1
            except Exception as e:
                logger.warning(f"获取装配体 {uuid} 的零件时出错: {e}")
        
        # 按出现频率排序
        sorted_parts = {k: v for k, v in sorted(part_counts.items(), key=lambda item: item[1], reverse=True)}
        logger.info(f"子集中共有 {len(sorted_parts)} 种不同的零件")
        
        return sorted_parts
    
    def generate_and_query(self, query_type: int, is_subset: bool = False) -> Tuple[List[str], List[float], List[int]]:
        """
        生成指定类型的查询并执行
        
        Args:
            query_type: 查询类型 (1-5)
            is_subset: 是否在子集上执行查询
            
        Returns:
            查询列表, 执行时间列表, 结果数量列表
        """
        queries = []
        execution_times = []
        result_counts = []
        
        if not self.part_names:
            self.part_names = self.fetch_all_part_names()
        
        # 根据查询类型生成查询
        if query_type == 1:
            # 查找同时包含多个特定零件的装配体 (AND 条件)
            queries = self.generate_and_condition_queries(self.queries_per_type)
        elif query_type == 2:
            # 查找包含任一特定组件的装配体 (OR 条件)
            queries = self.generate_or_condition_queries(self.queries_per_type)
        elif query_type == 3:
            # 查找包含A但不包含B的装配体 (AND NOT 条件)
            queries = self.generate_and_not_condition_queries(self.queries_per_type)
        elif query_type == 4:
            # 根据组件类型和数量筛选装配体
            queries = self.generate_component_count_queries(self.queries_per_type)
        elif query_type == 5:
            # 根据组件的模糊名称进行查找
            queries = self.generate_fuzzy_name_queries(self.queries_per_type)
        
        # 执行查询
        for query in tqdm(queries, desc=f"执行查询类型 {query_type}" + (" (子集)" if is_subset else "")):
            if is_subset:
                # 在子集上执行查询
                modified_query = self.modify_query_for_subset(query)
                time_taken, result_count = self.execute_query_with_timing(modified_query)
            else:
                # 在全库执行查询
                time_taken, result_count = self.execute_query_with_timing(query)
            
            execution_times.append(time_taken)
            result_counts.append(result_count)
        
        return queries, execution_times, result_counts
    
    def generate_and_condition_queries(self, count: int) -> List[str]:
        """生成AND条件查询：查找同时包含多个特定零件的装配体"""
        queries = []
        
        # 从常见零件中选择组合
        common_parts = list(self.get_common_parts_in_subset().keys())[:50]  # 取前50个常见零件
        
        for _ in range(count):
            # 随机选择2-3个零件组成查询
            num_parts = random.randint(2, 3)
            selected_parts = random.sample(common_parts, num_parts)
            
            query = f"""
            MATCH (a:Assembly)
            WHERE ALL(name IN {json.dumps(selected_parts)} WHERE (a)-[:hasComponent*]->(:Part|SubAssembly {{name: name}}))
            RETURN
                a.name AS assembly_name,
                a.uuid AS assembly_uuid
            """
            queries.append(query.strip())
        
        return queries
    
    def generate_or_condition_queries(self, count: int) -> List[str]:
        """生成OR条件查询：查找包含任一特定组件的装配体"""
        queries = []
        
        common_parts = list(self.get_common_parts_in_subset().keys())[:50]  # 取前50个常见零件
        
        for _ in range(count):
            # 随机选择2-3个零件组成查询
            num_parts = random.randint(2, 3)
            selected_parts = random.sample(common_parts, num_parts)
            
            query = f"""
            MATCH (a:Assembly)-[:hasComponent*]->(p:Part|SubAssembly)
            WHERE p.name IN {json.dumps(selected_parts)}
            RETURN
                DISTINCT a.name AS assembly_name,
                a.uuid AS assembly_uuid
            """
            queries.append(query.strip())
        
        return queries
    
    def generate_and_not_condition_queries(self, count: int) -> List[str]:
        """生成AND NOT条件查询：查找包含A但不包含B的装配体"""
        queries = []
        
        common_parts = list(self.get_common_parts_in_subset().keys())[:50]  # 取前50个常见零件
        
        for _ in range(count):
            # 随机选择1个必须包含的零件和1个必须不包含的零件
            include_part = random.choice(common_parts)
            exclude_part = random.choice([p for p in common_parts if p != include_part])
            
            query = f"""
            MATCH (a:Assembly)-[:hasComponent*]->(p:Part|SubAssembly {{name: '{include_part}'}})
            WHERE NOT (a)-[:hasComponent*]->(:Part|SubAssembly {{name: '{exclude_part}'}})
            RETURN
                DISTINCT a.name AS assembly_name,
                a.uuid AS assembly_uuid
            """
            queries.append(query.strip())
        
        return queries
    
    def generate_component_count_queries(self, count: int) -> List[str]:
        """生成组件数量查询：根据组件类型和数量筛选装配体"""
        queries = []
        
        common_parts = list(self.get_common_parts_in_subset().keys())[:50]  # 取前50个常见零件
        
        for _ in range(count):
            # 随机选择零件和数量
            part_name = random.choice(common_parts)
            part_count = random.randint(1, 10)  # 随机选择一个合理的数量
            
            query = f"""
            MATCH (a:Assembly)-[:hasComponent*]->(p:Part|SubAssembly {{name: '{part_name}'}})
            WITH a, count(p) AS part_count
            WHERE part_count = {part_count}
            RETURN
                a.name AS assembly_name,
                a.uuid AS assembly_uuid,
                part_count
            """
            queries.append(query.strip())
        
        return queries
    
    def generate_fuzzy_name_queries(self, count: int) -> List[str]:
        """生成模糊名称查询：根据组件的模糊名称进行查找"""
        queries = []
        
        # 获取常见零件中的一些子字符串
        common_parts = list(self.get_common_parts_in_subset().keys())[:50]
        
        common_substrings = set()
        for part in common_parts:
            if len(part) > 4:
                for i in range(len(part) - 2):
                    if part[i:i+3].isalpha():  # 只使用字母子串
                        common_substrings.add(part[i:i+3].lower())
        
        common_substrings = list(common_substrings)
        
        for _ in range(count):
            # 随机选择一个子字符串
            if common_substrings:
                substring = random.choice(common_substrings)
                
                query = f"""
                MATCH (a:Assembly)-[:hasComponent*]->(p:Part|SubAssembly)
                WHERE toLower(p.name) CONTAINS '{substring}'
                RETURN
                    DISTINCT a.name AS assembly_name,
                    a.uuid AS assembly_uuid
                """
                queries.append(query.strip())
            else:
                # 如果没有找到合适的子字符串，使用简单查询
                query = """
                MATCH (a:Assembly)-[:hasComponent*]->(p:Part|SubAssembly)
                WHERE p.name CONTAINS 'a'
                RETURN
                    DISTINCT a.name AS assembly_name,
                    a.uuid AS assembly_uuid
                LIMIT 100
                """
                queries.append(query.strip())
        
        return queries
    
    def modify_query_for_subset(self, query: str) -> str:
        """修改查询以在子集上执行"""
        # 添加子集约束
        subset_uuids_str = "'" + "', '".join(self.subset_assembly_uuids) + "'"
        
        # 修改查询，在第一个WHERE子句后添加AND a.uuid IN [...]
        if "WHERE" in query:
            modified_query = query.replace(
                "WHERE", 
                f"WHERE a.uuid IN [{subset_uuids_str}] AND"
            )
        else:
            # 如果查询中没有WHERE子句，则在MATCH后添加WHERE子句
            match_end_index = query.find("RETURN")
            if match_end_index > 0:
                modified_query = (
                    query[:match_end_index] + 
                    f"WHERE a.uuid IN [{subset_uuids_str}] " + 
                    query[match_end_index:]
                )
            else:
                # 无法修改查询
                modified_query = query
                
        return modified_query
    
    def execute_query_with_timing(self, query: str) -> Tuple[float, int]:
        """
        执行查询并计时
        
        Args:
            query: 要执行的Cypher查询
            
        Returns:
            执行时间(秒), 结果数量
        """
        start_time = time.time()
        
        try:
            with self.driver.session() as session:
                result = session.run(query)
                records = list(result)
                execution_time = time.time() - start_time
                return execution_time, len(records)
        except Exception as e:
            logger.error(f"查询执行失败: {e}")
            return time.time() - start_time, 0
    
    def run_performance_test(self) -> Dict[str, Any]:
        """
        运行性能测试
        
        Returns:
            测试结果字典
        """
        logger.info("开始性能测试...")
        
        # 连接数据库
        self.connect()
        
        # 获取统计信息
        stats = self.get_graph_stats()
        
        # 获取所有装配体UUID
        self.assembly_uuids = self.fetch_all_assembly_uuids()
        
        # 创建子集
        self.subset_assembly_uuids = self.create_assembly_subset()
        
        # 获取所有零件名称
        self.part_names = self.fetch_all_part_names()
        
        # 测试结果
        results = {
            "database_stats": stats,
            "query_types": {},
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # 测试每种查询类型
        query_type_names = {
            1: "AND条件查询",
            2: "OR条件查询",
            3: "AND NOT条件查询",
            4: "组件数量查询",
            5: "模糊名称查询"
        }
        
        for query_type in range(1, 6):
            logger.info(f"测试查询类型 {query_type}: {query_type_names[query_type]}")
            
            # 在全库上测试
            full_queries, full_times, full_counts = self.generate_and_query(query_type, is_subset=False)
            
            # 在子集上测试
            subset_queries, subset_times, subset_counts = self.generate_and_query(query_type, is_subset=True)
            
            # 保存结果
            results["query_types"][query_type] = {
                "name": query_type_names[query_type],
                "full_database": {
                    "queries": full_queries,
                    "execution_times": full_times,
                    "result_counts": full_counts,
                    "average_time": mean(full_times) if full_times else 0,
                    "min_time": min(full_times) if full_times else 0,
                    "max_time": max(full_times) if full_times else 0,
                    "std_time": stdev(full_times) if len(full_times) > 1 else 0,
                    "median_time": sorted(full_times)[len(full_times)//2] if full_times else 0
                },
                "subset": {
                    "queries": subset_queries,
                    "execution_times": subset_times,
                    "result_counts": subset_counts,
                    "average_time": mean(subset_times) if subset_times else 0,
                    "min_time": min(subset_times) if subset_times else 0,
                    "max_time": max(subset_times) if subset_times else 0,
                    "std_time": stdev(subset_times) if len(subset_times) > 1 else 0,
                    "median_time": sorted(subset_times)[len(subset_times)//2] if subset_times else 0
                }
            }
            
        # 关闭连接
        self.close()
        
        self.evaluation_results = results
        return results
    
    def generate_comparison_table(self) -> pd.DataFrame:
        """
        生成对比表格
        
        Returns:
            包含对比结果的DataFrame
        """
        if not self.evaluation_results:
            logger.error("未执行测试，无法生成对比表格")
            return pd.DataFrame()
        
        data = []
        for query_type, results in self.evaluation_results["query_types"].items():
            query_name = results["name"]
            
            # 全库结果
            full_avg = results["full_database"]["average_time"]
            full_median = results["full_database"]["median_time"]
            full_min = results["full_database"]["min_time"]
            full_max = results["full_database"]["max_time"]
            full_std = results["full_database"]["std_time"]
            
            # 子集结果
            subset_avg = results["subset"]["average_time"]
            subset_median = results["subset"]["median_time"]
            subset_min = results["subset"]["min_time"]
            subset_max = results["subset"]["max_time"]
            subset_std = results["subset"]["std_time"]
            
            # 计算加速比
            speedup = full_avg / subset_avg if subset_avg > 0 else float('inf')
            
            data.append({
                "查询类型": query_name,
                "全库平均时间(秒)": full_avg,
                "全库中位时间(秒)": full_median,
                "全库最小时间(秒)": full_min,
                "全库最大时间(秒)": full_max,
                "全库标准差(秒)": full_std,
                "子集平均时间(秒)": subset_avg,
                "子集中位时间(秒)": subset_median,
                "子集最小时间(秒)": subset_min,
                "子集最大时间(秒)": subset_max,
                "子集标准差(秒)": subset_std,
                "加速比": speedup
            })
        
        return pd.DataFrame(data)
    
    def print_comparison_table(self):
        """打印对比表格"""
        df = self.generate_comparison_table()
        if not df.empty:
            print("\nCypher查询性能对比表格:")
            print(df.to_string(index=False, float_format=lambda x: f"{x:.6f}"))
    
    def save_results(self, output_file: str = "cypher_query_performance_results.json"):
        """
        保存测试结果
        
        Args:
            output_file: 输出文件路径
        """
        if not self.evaluation_results:
            logger.error("未执行测试，无法保存结果")
            return
        
        # 创建输出目录
        output_dir = os.path.dirname(output_file)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 保存JSON结果
        with open(output_file, 'w', encoding='utf-8') as f:
            # 转换numpy数组和其他不可序列化对象
            def json_serialize(obj):
                if isinstance(obj, np.ndarray):
                    return obj.tolist()
                if isinstance(obj, np.float32) or isinstance(obj, np.float64):
                    return float(obj)
                if isinstance(obj, np.int32) or isinstance(obj, np.int64):
                    return int(obj)
                return str(obj)
            
            json.dump(self.evaluation_results, f, ensure_ascii=False, indent=2, default=json_serialize)
        
        logger.info(f"测试结果已保存到: {output_file}")
        
        # 生成并保存Excel表格
        df = self.generate_comparison_table()
        if not df.empty:
            excel_file = output_file.replace('.json', '.xlsx')
            df.to_excel(excel_file, index=False, float_format="%.6f")
            logger.info(f"对比表格已保存到: {excel_file}")
            
            # 保存CSV版本
            csv_file = output_file.replace('.json', '.csv')
            df.to_csv(csv_file, index=False, float_format="%.6f")
            logger.info(f"CSV对比表格已保存到: {csv_file}")

def main():
    """主函数"""
    # 获取Neo4j配置
    neo4j_config = Config.get_neo4j_config()
    
    # 创建性能测试器
    tester = CypherQueryPerformanceTester(
        neo4j_config=neo4j_config,
        subset_size=100,
        queries_per_type=100
    )
    
    # 运行测试
    results = tester.run_performance_test()
    
    # 打印对比表格
    tester.print_comparison_table()
    
    # 保存结果
    output_file = "results/cypher_query_performance_results.json"
    tester.save_results(output_file)

if __name__ == "__main__":
    main() 