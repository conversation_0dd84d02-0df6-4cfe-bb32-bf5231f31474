# CAD 知识图谱多智能体系统使用指南

## 系统概述

本系统采用分层协作的多智能体系统（MAS）架构，专为 CAD/机械设计领域的知识图谱查询而设计。系统以 Neo4j 图数据库为统一的数据核心，实现清晰的职责分离、高度的模块化和强大的可扩展性。

## 系统架构

### 核心智能体

1. **编排智能体 (Orchestrator Agent)**

   - 作为系统的"大脑"和总调度中心
   - 唯一直接与用户交互的智能体
   - 负责查询分解、策略规划和智能体委派

2. **多模态查询智能体 (Multi-modal Query Agent)**

   - 作为系统的"执行臂膀"
   - 封装与 Neo4j 图数据库的所有交互细节
   - 支持元数据查询、图结构查询和向量相似性搜索

3. **融合智能体 (Fusion Agent)**
   - 作为系统的"整合专家"
   - 负责多路径查询结果的智能融合、排序与提炼
   - 生成统一、高质量的候选列表

## 快速开始

### 基本安装

```bash
# 安装依赖
pip install -r requirements.txt

# 确保Neo4j数据库运行
# 配置环境变量或修改config.py中的数据库连接信息
```

### 简单使用示例

```python
import asyncio
from src.agents import MultiAgentSystem

async def simple_example():
    # 创建并启动多智能体系统
    mas = MultiAgentSystem()
    await mas.start()

    try:
        # 执行简单查询
        result = await mas.process_user_query("查找所有的发动机装配体")

        print(f"查询成功: {result['success']}")
        print(f"结果数量: {len(result['results'])}")

        # 显示结果
        for res in result['results'][:3]:
            print(f"- {res['data']['name']} (置信度: {res['confidence_score']:.2f})")

    finally:
        await mas.stop()

# 运行示例
asyncio.run(simple_example())
```

## 详细使用指南

### 1. 多模态查询

系统支持多种模态的查询输入：

```python
from src.agents import MultimodalQuery

# 创建多模态查询
query = MultimodalQuery(
    text_query="查找铝合金材料的零件",
    filters={
        "material": "aluminum",
        "category": "mechanical"
    },
    query_intent="寻找特定材料的机械零件"
)

result = await mas.process_multimodal_query_advanced(query)
```

### 2. 自定义检索策略

```python
from src.agents import RetrievalStrategy, QueryType

# 创建自定义策略
strategy = RetrievalStrategy(
    query_types=[QueryType.METADATA, QueryType.STRUCTURE],
    parallel_execution=True,
    fusion_required=True,
    confidence_threshold=0.6,
    max_results_per_query=10
)

result = await mas.process_multimodal_query_advanced(query, strategy)
```

### 3. 并行查询处理

```python
# 创建多个查询
queries = [
    MultimodalQuery(text_query="查找装配体", filters={"category": "automotive"}),
    MultimodalQuery(text_query="查找零件", filters={"material": "steel"}),
    MultimodalQuery(text_query="查找特征", filters={"type": "hole"})
]

# 并行执行
results = await mas.execute_parallel_queries(queries, max_concurrent=3)
```

### 4. 向量相似性查询

```python
import numpy as np

# 模拟图像特征向量（实际应用中从图像提取）
image_embedding = np.random.rand(768).tolist()

query = MultimodalQuery(
    text_query="查找相似形状的零件",
    image_data=image_embedding,
    filters={"type": "Part"}
)

result = await mas.process_multimodal_query_advanced(query)
```

## 查询类型说明

### 元数据查询 (METADATA)

- 基于节点属性的精确匹配或模糊搜索
- 支持的属性：name, material, category, industry 等
- 适用于已知具体属性值的查询

### 图结构查询 (STRUCTURE)

- 基于节点间关系的路径遍历
- 支持装配体结构、零件关系、特征查询
- 适用于"包含"、"组成"等结构性查询

### 向量相似性查询 (VECTOR_SIMILARITY)

- 基于形状嵌入向量的相似性搜索
- 支持图像、草图、CAD 片段的相似性匹配
- 适用于"相似形状"、"类似设计"等查询

### 混合查询 (HYBRID)

- 结合多种查询类型的综合搜索
- 自动融合不同路径的查询结果
- 适用于复杂的多模态查询

## 系统配置

### 数据库配置

```python
# 在config.py中配置Neo4j连接
NEO4J_URI = "bolt://localhost:7687"
NEO4J_USER = "neo4j"
NEO4J_PASSWORD = "your_password"
```

### 智能体配置

```python
# 创建系统时传入配置
config = {
    "fusion_strategy": "consensus_based",
    "confidence_threshold": 0.6,
    "max_results": 20
}

mas = MultiAgentSystem(config)
```

## 系统监控

### 获取系统状态

```python
status = mas.get_system_status()
print(f"运行状态: {status['is_running']}")
print(f"活跃会话: {status['active_sessions']}")

# 智能体状态
for agent_id, agent_status in status['agents'].items():
    print(f"{agent_status['name']}: {agent_status['status']}")
```

### 会话管理

```python
# 创建命名会话
result = await mas.process_user_query("查询内容", session_id="my_session")

# 获取会话信息
session_info = mas.get_session_info("my_session")
print(f"会话状态: {session_info['status']}")

# 清理旧会话
mas.clear_sessions(older_than_hours=24)
```

## 最佳实践

### 1. 查询优化

- 使用具体的过滤条件缩小搜索范围
- 对于大量结果，设置合适的 confidence_threshold
- 利用并行查询提高处理效率

### 2. 错误处理

```python
try:
    result = await mas.process_user_query("查询内容")
    if not result['success']:
        print(f"查询失败: {result.get('error', '未知错误')}")
except Exception as e:
    print(f"系统错误: {e}")
```

### 3. 资源管理

```python
# 确保正确关闭系统
try:
    # 执行查询操作
    pass
finally:
    await mas.stop()  # 释放数据库连接等资源
```

## 扩展开发

### 添加新的查询类型

1. 在`QueryType`枚举中添加新类型
2. 在`MultimodalQueryAgent`中实现对应的执行器
3. 更新编排智能体的策略规划逻辑

### 自定义融合策略

```python
class CustomFusionAgent(FusionAgent):
    async def _custom_fusion_strategy(self, fusion_results, fusion_params):
        # 实现自定义融合逻辑
        return processed_results

# 注册新策略
fusion_agent.fusion_strategies["custom"] = fusion_agent._custom_fusion_strategy
```

## 故障排除

### 常见问题

1. **数据库连接失败**

   - 检查 Neo4j 服务是否运行
   - 验证连接配置和认证信息

2. **查询结果为空**

   - 检查数据库中是否有相关数据
   - 降低 confidence_threshold 阈值
   - 使用更宽泛的查询条件

3. **性能问题**
   - 启用并行查询处理
   - 优化 Neo4j 索引配置
   - 调整 max_results_per_query 参数

### 调试模式

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# 系统将输出详细的调试信息
```

## 示例代码

完整的使用示例请参考：

- `examples/mas_usage_examples.py` - 各种使用场景的示例
- `tests/test_mas_system.py` - 单元测试和集成测试

## 技术支持

如有问题或建议，请：

1. 查看系统日志获取详细错误信息
2. 参考测试用例了解正确用法
3. 检查 Neo4j 数据库状态和数据完整性
