import requests
import base64

def test_ollama_image_prompt(image_path, prompt, api_url="http://192.168.10.77:11434/v1/chat/completions"):
    # 读取图片并进行 base64 编码
    with open(image_path, "rb") as img_file:
        img_base64 = base64.b64encode(img_file.read()).decode("utf-8")
    
    # 构造请求 payload
    payload = {
        "model": "gemma3:27b",
        "messages": [
            {"role": "user", "content": prompt},
            {"role": "user", "content": f"data:image/jpeg;base64,{img_base64}"}
        ]
    }
    
    # 发送 POST 请求
    response = requests.post(api_url, json=payload)
    if response.status_code == 200:
        print("Ollama 返回：", response.json())
    else:
        print("请求失败，状态码：", response.status_code)
        print("返回内容：", response.text)

# 示例调用
test_ollama_image_prompt(r"test\41032_ed481084\assembly.png", "请描述这张图片")