#!/usr/bin/env python3
"""
测试预加载模型和数据库连接的多智能体系统
"""

import asyncio
import time
import os
import sys

# 添加项目根目录到系统路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.agents.multi_agent_coordinator import MultiAgentCoordinator


async def test_preloaded_system():
    """测试预加载模型和数据库连接的多智能体系统"""
    
    print("=" * 50)
    print("测试预加载模型和数据库连接的多智能体系统")
    print("=" * 50)
    
    # 使用异步上下文管理器初始化多智能体系统
    # 这将在进入上下文时自动连接数据库和加载模型
    async with MultiAgentCoordinator() as coordinator:
        print("\n系统已完成初始化，准备执行查询...\n")
        
        # 测试查询1：简单文本查询
        print("\n📝 执行查询1：简单文本查询")
        query1 = "查找所有与齿轮相关的装配体"
        result1 = await coordinator.execute_multi_modal_query(query1, top_k=3)
        
        print(f"查询结果状态: {result1.status}")
        print(f"查询执行时间: {result1.execution_time:.2f}秒")
        print(f"找到结果数量: {result1.total_results}")
        
        if result1.results:
            print("\n查询结果:")
            for i, item in enumerate(result1.results, 1):
                print(f"  {i}. {item.name or item.uuid} (相似度: {item.similarity_score:.4f})")
                print(f"     描述: {item.description[:100]}..." if len(item.description) > 100 else f"     描述: {item.description}")
                print()
        
        # 测试查询2：复杂结构查询
        print("\n🔍 执行查询2：复杂结构查询")
        query2 = "找出包含至少5个零件且有螺栓连接的机械装置"
        result2 = await coordinator.execute_multi_modal_query(query2, top_k=3)
        
        print(f"查询结果状态: {result2.status}")
        print(f"查询执行时间: {result2.execution_time:.2f}秒")
        print(f"找到结果数量: {result2.total_results}")
        
        if result2.results:
            print("\n查询结果:")
            for i, item in enumerate(result2.results, 1):
                print(f"  {i}. {item.name or item.uuid} (相似度: {item.similarity_score:.4f})")
                print(f"     描述: {item.description[:100]}..." if len(item.description) > 100 else f"     描述: {item.description}")
                print()
    
    # 退出上下文管理器后，系统会自动断开连接
    print("\n系统已自动断开所有连接")


if __name__ == "__main__":
    # 运行异步测试函数
    asyncio.run(test_preloaded_system()) 