#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试装配体聚类分析脚本

本脚本用于测试装配体特征的HDBSCAN聚类分析和可视化功能
"""

import sys
import os
import subprocess
from pathlib import Path

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_assembly_feature_clustering():
    """测试装配体特征聚类"""
    print("=== 测试装配体特征聚类 ===")
    
    # 检查装配体特征文件是否存在
    features_file = "dataset/assembly_features.pkl"
    if not os.path.exists(features_file):
        print(f"错误: 装配体特征文件 {features_file} 不存在")
        print("请先运行装配体特征提取脚本:")
        print("python scripts/extract_assembly_features.py --max-assemblies 50")
        return False
    
    print(f"✓ 装配体特征文件存在: {features_file}")
    
    # 测试单个特征类型聚类
    test_commands = [
        {
            "name": "结构化特征聚类",
            "cmd": [
                "python", "evaluate_scripts/clustering_analysis/hdbscan_cluster_assembly_embeddings.py",
                "--embed", features_file,
                "--feature-type", "structural",
                "--min_cluster_size", "3"
            ]
        },
        {
            "name": "形状特征聚类",
            "cmd": [
                "python", "evaluate_scripts/clustering_analysis/hdbscan_cluster_assembly_embeddings.py",
                "--embed", features_file,
                "--feature-type", "shape",
                "--min_cluster_size", "3"
            ]
        }
    ]
    
    for test in test_commands:
        print(f"\n--- 测试 {test['name']} ---")
        try:
            result = subprocess.run(test['cmd'], capture_output=True, text=True, timeout=300)
            if result.returncode == 0:
                print(f"✓ {test['name']} 测试成功")
                # 显示部分输出
                output_lines = result.stdout.split('\n')
                for line in output_lines[-10:]:
                    if line.strip():
                        print(f"  {line}")
            else:
                print(f"✗ {test['name']} 测试失败")
                print(f"错误: {result.stderr}")
                return False
        except subprocess.TimeoutExpired:
            print(f"✗ {test['name']} 测试超时")
            return False
        except Exception as e:
            print(f"✗ {test['name']} 测试异常: {e}")
            return False
    
    return True


def test_assembly_cluster_visualization():
    """测试装配体聚类可视化"""
    print("\n=== 测试装配体聚类可视化 ===")
    
    # 检查聚类标签文件是否存在
    label_files = [
        "dataset/hdbscan_assembly_labels_structural.pkl",
        "dataset/hdbscan_assembly_labels_shape.pkl"
    ]
    
    existing_files = [f for f in label_files if os.path.exists(f)]
    if not existing_files:
        print("错误: 没有找到装配体聚类标签文件")
        print("请先运行装配体聚类分析")
        return False
    
    # 使用第一个存在的标签文件进行测试
    test_label_file = existing_files[0]
    print(f"✓ 使用标签文件: {test_label_file}")
    
    # 测试可视化
    try:
        cmd = [
            "python", "evaluate_scripts/clustering_analysis/visualize_hdbscan_assembly_clusters.py",
            "--labels", test_label_file,
            "--img_dir", "datasets/fusion360_assembly",
            "--top_n", "5",
            "--samples_per_cluster", "9",
            "--out_dir", "test_results/assembly_clusters_test"
        ]
        
        print("运行装配体聚类可视化测试...")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=180)
        
        if result.returncode == 0:
            print("✓ 装配体聚类可视化测试成功")
            # 检查输出文件
            output_dir = "test_results/assembly_clusters_test"
            if os.path.exists(os.path.join(output_dir, "index.html")):
                print(f"✓ 生成了可视化索引文件: {output_dir}/index.html")
            
            # 显示部分输出
            output_lines = result.stdout.split('\n')
            for line in output_lines[-5:]:
                if line.strip():
                    print(f"  {line}")
        else:
            print("✗ 装配体聚类可视化测试失败")
            print(f"错误: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("✗ 装配体聚类可视化测试超时")
        return False
    except Exception as e:
        print(f"✗ 装配体聚类可视化测试异常: {e}")
        return False
    
    return True


def test_all_feature_types_clustering():
    """测试所有装配体特征类型的聚类分析"""
    print("\n=== 测试所有装配体特征类型聚类 ===")
    
    features_file = "dataset/assembly_features.pkl"
    if not os.path.exists(features_file):
        print(f"错误: 装配体特征文件 {features_file} 不存在")
        return False
    
    try:
        cmd = [
            "python", "evaluate_scripts/clustering_analysis/hdbscan_cluster_assembly_embeddings.py",
            "--embed", features_file,
            "--feature-type", "all",
            "--min_cluster_size", "3"
        ]
        
        print("运行所有装配体特征类型聚类分析...")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            print("✓ 所有装配体特征类型聚类分析成功")
            
            # 检查是否生成了Markdown结果文件
            result_file = "dataset/assembly_cluster_result.md"
            if os.path.exists(result_file):
                print(f"✓ 生成了聚类结果摘要: {result_file}")
                # 显示结果文件内容
                with open(result_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    print("聚类结果摘要:")
                    print(content[:500] + "..." if len(content) > 500 else content)
            
        else:
            print("✗ 所有装配体特征类型聚类分析失败")
            print(f"错误: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("✗ 所有装配体特征类型聚类分析超时")
        return False
    except Exception as e:
        print(f"✗ 所有装配体特征类型聚类分析异常: {e}")
        return False
    
    return True


def main():
    """主测试函数"""
    print("开始测试装配体聚类分析脚本...")
    
    # 创建测试结果目录
    os.makedirs("test_results", exist_ok=True)
    
    success_count = 0
    total_tests = 3
    
    # 测试1: 装配体特征聚类
    if test_assembly_feature_clustering():
        success_count += 1
    
    # 测试2: 装配体聚类可视化
    if test_assembly_cluster_visualization():
        success_count += 1
    
    # 测试3: 所有特征类型聚类
    if test_all_feature_types_clustering():
        success_count += 1
    
    # 测试结果总结
    print(f"\n=== 测试总结 ===")
    print(f"通过测试: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("✓ 所有装配体聚类分析测试通过!")
    else:
        print("✗ 部分装配体聚类分析测试失败")
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
