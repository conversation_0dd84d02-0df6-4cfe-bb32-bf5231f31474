import requests
import logging
import numpy as np
from typing import List, Dict, Any, Optional, Union
from src.config import Config


class TextEmbedding:
    """
    使用llama.cpp部署的Qwen3-Embedding模型生成文本嵌入向量。
    默认使用本地部署的llama-server生成文本嵌入。
    """

    def __init__(
        self,
        api_url: Optional[str] = None,
        model: Optional[str] = None
    ):
        """
        初始化文本嵌入模型客户端

        Parameters
        ----------
        api_url : 基础 URL（到本地端口为止，末尾不带斜杠）
                 如果为 None，使用 Config.TEXT_EMBEDDING_API_URL 或默认本地地址
        model   : 模型名称
                 如果为 None，使用 Config.TEXT_EMBEDDING_MODEL
        kwargs  : 其他可选参数
        """
        # 从配置或默认值获取API URL
        self.base_url = (api_url or 
                        getattr(Config, 'TEXT_EMBEDDING_API_URL', None) or 
                        'http://localhost:8080')
        self.base_url = self.base_url.rstrip('/')
        
        # 从配置或默认值获取模型名称
        self.model = (model or 
                     getattr(Config, 'TEXT_EMBEDDING_MODEL', None) or 
                     'Qwen3-Embedding-8B')
        
        
        # 预生成endpoint
        self.endpoint = f"{self.base_url}/embedding"
        
        
        logging.info(f"初始化文本嵌入模型: {self.model}, API: {self.base_url}")

    def get_embedding(self, text: str) -> np.ndarray:
        """
        获取单个文本的嵌入向量

        Parameters
        ----------
        text : 需要获取嵌入向量的文本

        Returns
        -------
        np.ndarray
            文本的嵌入向量，numpy数组格式
        """
        try:
            # 检查文本是否已经以<|endoftext|>结尾，如果没有则添加
            if not text.endswith("<|endoftext|>"):
                text = f"{text} <|endoftext|>"
            
            payload = {
                "input": text
            }
            
            headers = {"Content-Type": "application/json"}
            
            response = requests.post(
                self.endpoint,
                headers=headers,
                json=payload
            )
            
            response.raise_for_status()
            result = response.json()
            
            # 从响应中提取嵌入向量

            embedding = result[0]["embedding"]
              
            # 转换为numpy数组并归一化
            embedding_array = np.array(embedding[0])
            return self._normalize_vector(embedding_array)
                
        except Exception as e:
            logging.error(f"获取文本嵌入失败: {e}")
            # 返回零向量作为回退
            return np.zeros(1536)  # 默认维度


    def compute_similarity(self, text1: str, text2: str) -> float:
        """
        计算两个文本之间的余弦相似度

        Parameters
        ----------
        text1 : 第一个文本
        text2 : 第二个文本

        Returns
        -------
        float
            两个文本的余弦相似度，范围为[-1, 1]
        """
        # 获取两个文本的嵌入向量
        embedding1 = self.get_embedding(text1)
        embedding2 = self.get_embedding(text2)
        
        # 计算余弦相似度
        return self._cosine_similarity(embedding1, embedding2)
    
    @staticmethod
    def _normalize_vector(vector: np.ndarray) -> np.ndarray:
        """
        对向量进行L2归一化

        Parameters
        ----------
        vector : 需要归一化的向量

        Returns
        -------
        np.ndarray
            归一化后的向量
        """
        norm = np.linalg.norm(vector)
        
        # 避免除零错误
        if norm == 0:
            return vector
        
        return vector / norm
    
    @staticmethod
    def _cosine_similarity(vec1: np.ndarray, vec2: np.ndarray) -> float:
        """
        计算两个向量之间的余弦相似度

        Parameters
        ----------
        vec1 : 第一个向量
        vec2 : 第二个向量

        Returns
        -------
        float
            两个向量的余弦相似度，范围为[-1, 1]
        """
        dot_product = np.dot(vec1, vec2)
        norm1 = np.linalg.norm(vec1)
        norm2 = np.linalg.norm(vec2)
        
        # 避免除零错误
        if norm1 == 0 or norm2 == 0:
            return 0.0
            
        return dot_product / (norm1 * norm2)


# 便捷函数，获取默认文本嵌入实例
def get_default_text_embedding() -> TextEmbedding:
    """
    获取默认配置的文本嵌入实例

    Returns
    -------
    TextEmbedding
        默认配置的文本嵌入实例
    """
    return TextEmbedding()
