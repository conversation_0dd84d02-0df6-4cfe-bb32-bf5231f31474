# 装配体聚类分析使用指南

本文档介绍如何使用装配体聚类分析和可视化功能。

## 概述

装配体聚类分析功能可以对 CAD 装配体进行无监督聚类，帮助发现相似的装配体模式。支持多种特征类型的聚类分析，包括结构化特征、形状特征、语义特征和融合特征。

## 功能特点

- **多特征类型支持**: 结构化、形状、语义、融合特征
- **HDBSCAN 聚类算法**: 可以自动确定簇数量，处理噪声数据
- **内部评估指标**: Silhouette Score、Davies-Bouldin Index、Calinski-Harabasz Index
- **可视化展示**: 生成装配体簇的图片网格和 HTML 索引页面
- **统计分析**: 提供详细的聚类统计信息

## 脚本文件

### 1. 装配体特征聚类脚本

`evaluate_scripts/clustering_analysis/hdbscan_cluster_assembly_embeddings.py`

### 2. 装配体聚类可视化脚本

`evaluate_scripts/clustering_analysis/visualize_hdbscan_assembly_clusters.py`

### 3. 测试脚本

`test_scripts/test_assembly_clustering.py`

## 使用方法

### 前置条件

1. 确保已提取装配体特征：

```bash
python scripts/extract_assembly_features.py --max-assemblies 100
```

2. 确保装配体图片存在于正确的目录结构中：
   - 图片路径格式：`datasets/fusion360_assembly/{装配体ID}/assembly.png`
   - 每个装配体有自己的子目录，包含名为 `assembly.png` 的图片文件

### 基本用法

#### 1. 单个特征类型聚类

```bash
# 分析形状特征
python evaluate_scripts/clustering_analysis/hdbscan_cluster_assembly_embeddings.py \
    --embed dataset/assembly_features.pkl \
    --feature-type shape \
    --min_cluster_size 5

# 分析语义特征
python evaluate_scripts/clustering_analysis/hdbscan_cluster_assembly_embeddings.py \
    --embed dataset/assembly_features.pkl \
    --feature-type semantic \
    --min_cluster_size 5

# 分析结构化特征
python evaluate_scripts/clustering_analysis/hdbscan_cluster_assembly_embeddings.py \
    --embed dataset/assembly_features.pkl \
    --feature-type structural \
    --min_cluster_size 5

# 分析融合特征
python evaluate_scripts/clustering_analysis/hdbscan_cluster_assembly_embeddings.py \
    --embed dataset/assembly_features.pkl \
    --feature-type fused_features \
    --min_cluster_size 5
```

#### 2. 分析所有特征类型

```bash
python evaluate_scripts/clustering_analysis/hdbscan_cluster_assembly_embeddings.py \
    --embed dataset/assembly_features.pkl \
    --feature-type all \
    --min_cluster_size 5
```

这会生成一个 Markdown 表格文件 `dataset/assembly_cluster_result.md`，包含所有特征类型的聚类结果对比。

#### 3. 可视化聚类结果

```bash
# 可视化融合特征聚类结果
python evaluate_scripts/clustering_analysis/visualize_hdbscan_assembly_clusters.py \
    --labels dataset/hdbscan_assembly_labels_fused_features.pkl \
    --img_dir datasets/fusion360_assembly \
    --top_n 10

# 可视化形状特征聚类结果
python evaluate_scripts/clustering_analysis/visualize_hdbscan_assembly_clusters.py \
    --labels dataset/hdbscan_assembly_labels_shape.pkl \
    --img_dir datasets/fusion360_assembly \
    --top_n 10
```

### 高级参数配置

#### 聚类参数

- `--min_cluster_size`: 最小簇大小（默认 5，装配体数量较少）
- `--min_samples`: 最小样本数（默认 None）
- `--metric`: 距离度量（默认 euclidean）
- `--norm`: 是否 L2 归一化（1/0，默认 1）
- `--pca`: 是否 PCA 降维（1/0，默认 1）
- `--pca-components`: PCA 降维维度（默认 64）

#### 融合特征权重

- `--shape-weight`: 形状特征权重（默认 0.3）
- `--semantic-weight`: 语义特征权重（默认 0.2）
- `--structural-weight`: 结构化特征权重（默认 0.5）

#### 可视化参数

- `--top_n`: 显示前 N 个最大的簇
- `--samples_per_cluster`: 每个簇显示的样本数（默认 16）
- `--min_cluster_size`: 最小簇大小过滤
- `--out_dir`: 输出目录（默认根据特征类型自动命名）

### 使用 VS Code 任务

在 VS Code 中，你可以使用预定义的任务：

1. **Ctrl+Shift+P** 打开命令面板
2. 输入 "Tasks: Run Task"
3. 选择相应的任务：
   - `Cluster Assembly Features (All)`: 分析所有特征类型
   - `Cluster Assembly Features (Shape)`: 分析形状特征
   - `Cluster Assembly Features (Semantic)`: 分析语义特征
   - `Cluster Assembly Features (Fused)`: 分析融合特征
   - `Visualize Assembly Clusters (Fused)`: 可视化融合特征聚类
   - `Visualize Assembly Clusters (Shape)`: 可视化形状特征聚类
   - `Test Assembly Clustering`: 运行测试

## 输出文件

### 聚类结果文件

- `dataset/hdbscan_assembly_labels_structural.pkl`: 结构化特征聚类结果
- `dataset/hdbscan_assembly_labels_shape.pkl`: 形状特征聚类结果
- `dataset/hdbscan_assembly_labels_semantic.pkl`: 语义特征聚类结果
- `dataset/hdbscan_assembly_labels_fused_features.pkl`: 融合特征聚类结果
- `dataset/assembly_cluster_result.md`: 所有特征类型的聚类结果对比表格

### 可视化文件

- `visualization_results/{feature_type}_assembly_clusters/`: 可视化结果目录
  - `index.html`: HTML 索引页面
  - `assembly_cluster_XXX_YYY.png`: 各个簇的可视化图片

## 输出解读

### 聚类指标

- **Silhouette Score**: 范围[-1, 1]，越接近 1 越好，表示类内相似度高、类间区分度好
- **Davies-Bouldin Index**: 越小越好，表示簇间距离大、簇内距离小
- **Calinski-Harabasz Index**: 越大越好，表示簇间方差与簇内方差比值大

### 聚类统计

- **簇数**: 自动发现的簇数量
- **噪声比例**: 被标记为噪声的装配体比例
- **覆盖率**: 成功聚类的装配体比例

## 示例工作流

1. **提取装配体特征**：

```bash
python scripts/extract_assembly_features.py --max-assemblies 100 --enable-fusion
```

2. **运行聚类分析**：

```bash
python evaluate_scripts/clustering_analysis/hdbscan_cluster_assembly_embeddings.py \
    --feature-type all --min_cluster_size 5
```

3. **可视化最佳特征类型**：

```bash
# 根据聚类结果选择表现最好的特征类型进行可视化
python evaluate_scripts/clustering_analysis/visualize_hdbscan_assembly_clusters.py \
    --labels dataset/hdbscan_assembly_labels_fused_features.pkl \
    --top_n 15 --samples_per_cluster 20
```

4. **查看结果**：

- 检查 `dataset/assembly_cluster_result.md` 了解各特征类型的聚类性能
- 在浏览器中打开可视化 HTML 文件查看聚类结果

## 注意事项

1. **数据量要求**: 装配体数量较少时，建议降低 `min_cluster_size` 参数
2. **图片路径**: 确保装配体图片存在于 `datasets/fusion360_assembly/{装配体ID}/assembly.png` 路径下
3. **内存使用**: 大量装配体时可能需要较多内存，可以分批处理
4. **特征质量**: 聚类效果很大程度上依赖于特征的质量，建议先检查特征提取结果

## 故障排除

### 常见问题

1. **找不到特征文件**: 确保先运行了装配体特征提取
2. **聚类结果差**: 尝试调整 `min_cluster_size` 或使用不同的特征类型
3. **可视化图片缺失**: 检查装配体图片是否存在于 `datasets/fusion360_assembly/{装配体ID}/assembly.png` 路径
4. **内存不足**: 减少 PCA 维度或分批处理装配体

### 性能优化

1. **使用 PCA 降维**: 对于高维特征，启用 PCA 可以提高性能
2. **调整簇大小**: 根据数据集大小调整 `min_cluster_size`
3. **选择合适的距离度量**: 根据特征类型选择 euclidean 或 cosine 距离
