from abc import ABC, abstractmethod
import configparser
from src.agents.data_models import BaseTask, QueryResult

class BaseAgent(ABC):
    def __init__(self, agent_name: str, config_path: str = 'config.ini'):
        self.agent_name = agent_name
        # TODO: 修改配置来源
        self.config = configparser.ConfigParser()
        self.config.read(config_path)
        print(f"Initializing {self.agent_name}...")

    @abstractmethod
    async def connect(self):
        pass

    @abstractmethod
    async def disconnect(self):
        pass

    @abstractmethod
    async def execute_task(self, task: BaseTask) -> QueryResult:
        pass