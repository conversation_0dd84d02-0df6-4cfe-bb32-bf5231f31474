

#!/usr/bin/env python3
"""
装配体词袋聚类结果可视化脚本

可视化装配体词袋聚类的结果：
- 聚类分布图
- 聚类内容分析
- 特征重要性分析
- 聚类质量评估
"""

import argparse
import pickle
from pathlib import Path
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE
import umap
from collections import Counter, defaultdict
import logging
import os
import random
from PIL import Image
from tqdm import tqdm

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")

def load_clustering_results(result_file):
    """加载聚类结果"""
    logger.info(f"正在加载聚类结果: {result_file}")
    
    with open(result_file, 'rb') as f:
        results = pickle.load(f)
    
    labels = results['labels']
    assembly_names = results['assembly_names']
    
    logger.info(f"加载了 {len(assembly_names)} 个装配体的聚类结果")
    logger.info(f"聚类算法: {results.get('algorithm', 'unknown')}")
    
    return results

def load_bow_features(feature_file):
    """加载原始词袋特征"""
    logger.info(f"正在加载词袋特征: {feature_file}")
    
    with open(feature_file, 'rb') as f:
        bow_data = pickle.load(f)
    
    return bow_data['features'], bow_data

def plot_cluster_distribution(labels, save_path=None):
    """绘制聚类分布图"""
    logger.info("绘制聚类分布图...")
    
    unique_labels, counts = np.unique(labels, return_counts=True)
    n_clusters = len(unique_labels) - (1 if -1 in unique_labels else 0)
    n_noise = counts[unique_labels == -1][0] if -1 in unique_labels else 0
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 聚类大小条形图
    valid_mask = unique_labels != -1
    valid_labels = unique_labels[valid_mask]
    valid_counts = counts[valid_mask]
    
    if len(valid_labels) > 0:
        bars = ax1.bar(range(len(valid_labels)), valid_counts, 
                      color=plt.cm.Set3(np.linspace(0, 1, len(valid_labels))))
        ax1.set_xlabel('聚类ID')
        ax1.set_ylabel('装配体数量')
        ax1.set_title(f'聚类大小分布 (共{n_clusters}个聚类)')
        ax1.set_xticks(range(len(valid_labels)))
        ax1.set_xticklabels([f'C{i}' for i in valid_labels], rotation=45)
        
        # 添加数值标签
        for bar, count in zip(bars, valid_counts):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                    str(count), ha='center', va='bottom')
    
    # 聚类大小分布直方图
    ax2.hist(valid_counts, bins=min(20, len(valid_counts)), alpha=0.7, color='skyblue')
    ax2.set_xlabel('聚类大小')
    ax2.set_ylabel('聚类数量')
    ax2.set_title('聚类大小分布直方图')
    ax2.axvline(np.mean(valid_counts), color='red', linestyle='--', 
               label=f'平均值: {np.mean(valid_counts):.1f}')
    ax2.legend()
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"聚类分布图已保存: {save_path}")
    
    plt.show()
    
    # 输出统计信息
    logger.info(f"聚类统计:")
    logger.info(f"  总聚类数: {n_clusters}")
    logger.info(f"  噪声点数: {n_noise}")
    if len(valid_counts) > 0:
        logger.info(f"  平均聚类大小: {np.mean(valid_counts):.2f}")
        logger.info(f"  聚类大小范围: {np.min(valid_counts)} - {np.max(valid_counts)}")
        logger.info(f"  聚类大小标准差: {np.std(valid_counts):.2f}")

def plot_2d_clustering(features, labels, method='tsne', save_path=None):
    """绘制2D聚类可视化"""
    logger.info(f"使用{method.upper()}进行2D可视化...")
    
    if method == 'pca':
        reducer = PCA(n_components=2, random_state=42)
        coords_2d = reducer.fit_transform(features)
    elif method == 'tsne':
        reducer = TSNE(n_components=2, random_state=42, perplexity=30)
        coords_2d = reducer.fit_transform(features)
    elif method == 'umap':
        reducer = umap.UMAP(n_components=2, random_state=42)
        coords_2d = reducer.fit_transform(features)
    else:
        raise ValueError(f"不支持的降维方法: {method}")
    
    plt.figure(figsize=(12, 8))
    
    unique_labels = np.unique(labels)
    colors = plt.cm.Set3(np.linspace(0, 1, len(unique_labels)))
    
    for label, color in zip(unique_labels, colors):
        if label == -1:
            # 噪声点用黑色表示
            mask = labels == label
            plt.scatter(coords_2d[mask, 0], coords_2d[mask, 1], 
                       c='black', marker='x', s=20, alpha=0.6, label='噪声')
        else:
            mask = labels == label
            plt.scatter(coords_2d[mask, 0], coords_2d[mask, 1], 
                       c=[color], s=30, alpha=0.7, label=f'聚类 {label}')
    
    plt.title(f'装配体聚类2D可视化 ({method.upper()})')
    plt.xlabel(f'{method.upper()} 1')
    plt.ylabel(f'{method.upper()} 2')
    
    # 只显示前15个聚类的图例
    handles, labels_legend = plt.gca().get_legend_handles_labels()
    if len(handles) > 15:
        plt.legend(handles[:15], labels_legend[:15], bbox_to_anchor=(1.05, 1), loc='upper left')
    else:
        plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"2D聚类图已保存: {save_path}")
    
    plt.show()

def analyze_cluster_features(features, labels, cluster_to_idx, top_features=10):
    """分析聚类的特征重要性"""
    logger.info("分析聚类特征重要性...")
    
    unique_labels = np.unique(labels)
    valid_labels = unique_labels[unique_labels != -1]
    
    if len(valid_labels) == 0:
        logger.warning("没有有效的聚类")
        return
    
    # 计算每个聚类的平均特征值
    cluster_features = {}
    for label in valid_labels:
        mask = labels == label
        cluster_mean = np.mean(features[mask], axis=0)
        cluster_features[label] = cluster_mean
    
    # 计算全局平均特征值
    global_mean = np.mean(features, axis=0)
    
    # 找出每个聚类最重要的特征（与全局均值差异最大）
    idx_to_cluster = {idx: cluster for cluster, idx in cluster_to_idx.items()}
    
    plt.figure(figsize=(15, 8))
    
    for i, label in enumerate(valid_labels[:6]):  # 只显示前6个聚类
        cluster_mean = cluster_features[label]
        feature_importance = np.abs(cluster_mean - global_mean)
        
        # 找出最重要的特征
        top_indices = np.argsort(feature_importance)[-top_features:][::-1]
        top_values = feature_importance[top_indices]
        
        # 获取特征名称（零件簇ID）
        feature_names = [f"簇{idx_to_cluster.get(idx, idx)}" for idx in top_indices]
        
        plt.subplot(2, 3, i+1)
        bars = plt.bar(range(len(top_values)), top_values)
        plt.title(f'聚类 {label} 重要特征')
        plt.xlabel('零件簇')
        plt.ylabel('重要性分数')
        plt.xticks(range(len(feature_names)), feature_names, rotation=45)
        
        # 添加数值标签
        for bar, value in zip(bars, top_values):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.001,
                    f'{value:.3f}', ha='center', va='bottom', fontsize=8)
    
    plt.tight_layout()
    plt.show()

def print_cluster_summary(results, bow_data):
    """打印聚类摘要信息"""
    logger.info("聚类摘要信息:")
    
    labels = results['labels']
    assembly_names = results['assembly_names']
    algorithm = results.get('algorithm', 'unknown')
    params = results.get('params', {})
    evaluation_metrics = results.get('evaluation_metrics', {})
    
    print("\n" + "="*60)
    print("装配体词袋聚类结果摘要")
    print("="*60)
    
    print(f"聚类算法: {algorithm.upper()}")
    print(f"算法参数: {params}")
    
    print(f"\n词袋特征信息:")
    source_bow = results.get('source_bow_data', {})
    print(f"  特征类型: {source_bow.get('feature_type', 'unknown')}")
    print(f"  装配体数量: {source_bow.get('num_assemblies', len(assembly_names))}")
    print(f"  零件簇数量: {source_bow.get('num_clusters', 0)}")
    print(f"  特征维度: {source_bow.get('feature_dim', 0)}")
    
    unique_labels = np.unique(labels)
    n_clusters = len(unique_labels) - (1 if -1 in unique_labels else 0)
    n_noise = np.sum(labels == -1) if -1 in labels else 0
    
    print(f"\n聚类结果:")
    print(f"  总装配体数: {len(assembly_names)}")
    print(f"  聚类数量: {n_clusters}")
    print(f"  噪声点数: {n_noise}")
    print(f"  噪声比例: {n_noise/len(labels)*100:.1f}%")
    
    if evaluation_metrics:
        print(f"\n评估指标:")
        for metric, value in evaluation_metrics.items():
            print(f"  {metric}: {value:.4f}")
    
    # 显示聚类大小分布
    valid_labels = unique_labels[unique_labels != -1]
    if len(valid_labels) > 0:
        _, counts = np.unique(labels[labels != -1], return_counts=True)
        print(f"\n聚类大小统计:")
        print(f"  平均大小: {np.mean(counts):.2f}")
        print(f"  大小范围: {np.min(counts)} - {np.max(counts)}")
        print(f"  最大5个聚类: {sorted(counts, reverse=True)[:5]}")
    
    print("="*60)

def create_assembly_cluster_grid(cluster_indices, assembly_names, img_dir, 
                                samples_per_cluster=16, grid_size=None):
    """为一个装配体簇创建图片网格"""
    if grid_size is None:
        # 自动计算网格大小
        grid_size = int(np.ceil(np.sqrt(samples_per_cluster)))
    
    # 如果簇太大，随机采样
    if len(cluster_indices) > samples_per_cluster:
        sample_indices = random.sample(cluster_indices, samples_per_cluster)
    else:
        sample_indices = cluster_indices
    
    # 创建网格
    fig = plt.figure(figsize=(grid_size*2.5, grid_size*2.5))
    
    for i, idx in enumerate(sample_indices):
        if i >= samples_per_cluster:
            break
            
        assembly_name = assembly_names[idx]
        
        # 构建图片路径 - 装配体图片位于 datasets/fusion360_assembly/{assembly_name}/assembly.png
        # 尝试多种可能的路径格式
        possible_paths = [
            os.path.join(img_dir, assembly_name, "assembly.png"),
            os.path.join(img_dir, f"{assembly_name}.png"),
            os.path.join(img_dir, "assemblies", f"{assembly_name}.png"),
        ]
        
        img_path = None
        for path in possible_paths:
            if os.path.exists(path):
                img_path = path
                break
        
        if img_path and os.path.exists(img_path):
            try:
                # 添加子图
                ax = fig.add_subplot(grid_size, grid_size, i + 1)
                img = Image.open(img_path)
                ax.imshow(img)
                ax.axis('off')
                
                # 不显示任何标题或文本
            except Exception as e:
                logger.warning(f"无法加载装配体图片 {img_path}: {e}")
                # 显示空白作为备选
                ax = fig.add_subplot(grid_size, grid_size, i + 1)
                ax.axis('off')
        else:
            # 如果图片不存在，显示空白
            ax = fig.add_subplot(grid_size, grid_size, i + 1)
            ax.axis('off')
    
    plt.tight_layout()
    return fig

def visualize_assembly_bow_clusters(results, img_dir, output_dir, 
                                   top_n=None, samples_per_cluster=16, min_cluster_size=2):
    """可视化装配体词袋聚类的图片结果"""
    logger.info("开始生成装配体聚类图片可视化...")
    
    labels = results['labels']
    assembly_names = results['assembly_names']
    feature_type = results.get('source_bow_data', {}).get('feature_type', 'bow')
    
    # 构建每个簇的样本索引
    clusters = defaultdict(list)
    for i, label in enumerate(labels):
        if label != -1:  # 忽略噪声点
            clusters[int(label)].append(i)
    
    # 获取簇统计信息并排序
    stats = [(cluster_id, len(indices)) for cluster_id, indices in clusters.items()]
    stats.sort(key=lambda x: x[1], reverse=True)
    
    # 过滤小簇
    if min_cluster_size > 0:
        stats = [(cid, size) for cid, size in stats if size >= min_cluster_size]
    
    # 限制簇数量
    if top_n is not None:
        stats = stats[:top_n]
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    if not stats:
        logger.warning("没有找到符合条件的聚类")
        return 0
    
    logger.info(f"将可视化 {len(stats)} 个聚类")
    
    # 为每个簇创建可视化
    for cluster_id, size in tqdm(stats, desc="生成装配体簇图片可视化"):
        cluster_indices = clusters[cluster_id]
        
        # 创建图片网格
        fig = create_assembly_cluster_grid(
            cluster_indices, assembly_names, img_dir, 
            samples_per_cluster=samples_per_cluster
        )
        
        # 不显示标题，避免编码混乱
        # plt.suptitle(f"装配体词袋聚类 #{cluster_id} - 大小: {size} ({feature_type} 特征)", fontsize=16)
        
        # 保存图片
        out_path = os.path.join(output_dir, f"bow_cluster_{cluster_id:03d}_{size}.png")
        plt.savefig(out_path, dpi=150, bbox_inches='tight')
        plt.close(fig)
        
        logger.info(f"已保存聚类 {cluster_id} 的可视化: {out_path}")
    
    # 创建索引HTML文件
    create_bow_html_index(output_dir, stats, feature_type, results)
    
    return len(stats)

def create_bow_html_index(output_dir, stats, feature_type, results):
    """创建词袋聚类HTML索引文件"""
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>装配体词袋聚类结果可视化 - {feature_type} 特征</title>
        <meta charset="utf-8">
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            .cluster-grid {{ display: grid; grid-template-columns: repeat(auto-fill, minmax(400px, 1fr)); gap: 20px; }}
            .cluster-item {{ border: 1px solid #ddd; padding: 15px; border-radius: 8px; }}
            .cluster-item img {{ max-width: 100%; height: auto; }}
            .cluster-info {{ background: #f9f9f9; padding: 10px; margin-bottom: 10px; border-radius: 5px; }}
            h1, h2 {{ color: #333; }}
            .feature-info {{ background: #e8f4f8; padding: 15px; border-radius: 8px; margin-bottom: 20px; }}
            .summary {{ background: #f0f8ff; padding: 15px; border-radius: 8px; margin-bottom: 20px; }}
            .stat-item {{ margin-bottom: 5px; }}
        </style>
    </head>
    <body>
        <h1>装配体词袋聚类结果可视化</h1>
        <div class="feature-info">
            <strong>特征类型:</strong> {feature_type}<br>
            <strong>聚类算法:</strong> {results.get('algorithm', 'unknown')}<br>
            <strong>簇数量:</strong> {len(stats)}<br>
            <strong>分析对象:</strong> 装配体
        </div>
    """
    
    # 添加聚类统计摘要
    labels = results['labels']
    total_assemblies = len(labels)
    clustered_assemblies = np.sum(labels != -1)
    n_noise = np.sum(labels == -1)
    
    html_content += f"""
    <div class="summary">
        <h3>聚类统计摘要</h3>
        <div class="stat-item"><strong>总装配体数:</strong> {total_assemblies}</div>
        <div class="stat-item"><strong>成功聚类装配体数:</strong> {clustered_assemblies}</div>
        <div class="stat-item"><strong>噪声点数:</strong> {n_noise}</div>
        <div class="stat-item"><strong>聚类覆盖率:</strong> {clustered_assemblies/total_assemblies*100:.1f}%</div>
    </div>
    """
    
    html_content += '<div class="cluster-grid">'
    
    for cluster_id, size in stats:
        img_filename = f"bow_cluster_{cluster_id:03d}_{size}.png"
        
        html_content += f"""
        <div class="cluster-item">
            <h2>词袋聚类 #{cluster_id} (大小: {size})</h2>
            <div class="cluster-info">
                包含 {size} 个装配体<br>
                特征类型: {feature_type}
            </div>
            <a href="{img_filename}" target="_blank">
                <img src="{img_filename}" alt="词袋聚类 {cluster_id}">
            </a>
        </div>
        """
    
    html_content += """
        </div>
    </body>
    </html>
    """
    
    index_path = os.path.join(output_dir, "index.html")
    with open(index_path, "w", encoding="utf-8") as f:
        f.write(html_content)
    
    logger.info(f"已创建HTML索引文件: {index_path}")

def main():
    parser = argparse.ArgumentParser(description='可视化装配体词袋聚类结果')
    parser.add_argument('--result-file', type=str,
                       default='data/clustering/assemblies/assembly_bow_clustering_results.pkl',
                       help='聚类结果文件')
    parser.add_argument('--feature-file', type=str,
                       default='data/features/assemblies/assembly_bow_features.pkl',
                       help='原始词袋特征文件')
    parser.add_argument('--output-dir', type=str, default='results/visualization/bow_clustering',
                       help='输出图片目录')
    parser.add_argument('--viz-method', type=str, choices=['pca', 'tsne', 'umap'],
                       default='tsne', help='2D可视化方法')
    parser.add_argument('--save-plots', action='store_true',
                       help='保存图片到文件')
    parser.add_argument('--show-features', action='store_true',
                       help='显示特征重要性分析')
    parser.add_argument('--img-dir', type=str, default='data/datasets/fusion360_assembly',
                       help='装配体图片目录')
    parser.add_argument('--visualize-clusters', default=True,
                       help='生成聚类图片可视化')
    parser.add_argument('--samples-per-cluster', type=int, default=16,
                       help='每个聚类显示的样本数')
    parser.add_argument('--min-cluster-size', type=int, default=2,
                       help='最小聚类大小过滤')
    
    args = parser.parse_args()
    
    # 检查输入文件
    if not Path(args.result_file).exists():
        logger.error(f"聚类结果文件不存在: {args.result_file}")
        return
    
    if args.show_features and not Path(args.feature_file).exists():
        logger.error(f"特征文件不存在: {args.feature_file}")
        return
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    if args.save_plots:
        output_dir.mkdir(parents=True, exist_ok=True)
    
    try:
        # 加载聚类结果
        results = load_clustering_results(args.result_file)
        labels = results['labels']
        assembly_names = results['assembly_names']
        
        # 加载原始特征
        if args.show_features or True:  # 总是加载用于可视化
            features, bow_data = load_bow_features(args.feature_file)
        
        # 打印摘要信息
        print_cluster_summary(results, bow_data)
        
        # 绘制聚类分布
        dist_save_path = None
        if args.save_plots:
            dist_save_path = output_dir / 'cluster_distribution.png'
        plot_cluster_distribution(labels, dist_save_path)
        
        # 绘制2D可视化
        viz_save_path = None
        if args.save_plots:
            viz_save_path = output_dir / f'cluster_2d_{args.viz_method}.png'
        plot_2d_clustering(features, labels, args.viz_method, viz_save_path)
        
        # 特征重要性分析
        if args.show_features:
            cluster_to_idx = bow_data.get('cluster_to_idx', {})
            if cluster_to_idx:
                analyze_cluster_features(features, labels, cluster_to_idx)
            else:
                logger.warning("无法进行特征分析：缺少cluster_to_idx映射")
        
        # 生成聚类图片可视化
        if args.visualize_clusters:
            if not Path(args.img_dir).exists():
                logger.warning(f"图片目录不存在: {args.img_dir}")
                logger.info("跳过图片可视化")
            else:
                cluster_output_dir = output_dir / 'cluster_images'
                visualized_count = visualize_assembly_bow_clusters(
                    results, args.img_dir, str(cluster_output_dir),
                    top_n=None,  # 显示所有聚类
                    samples_per_cluster=args.samples_per_cluster,
                    min_cluster_size=args.min_cluster_size
                )
                logger.info(f"已生成 {visualized_count} 个聚类的图片可视化")
                logger.info(f"请在浏览器中打开 {cluster_output_dir / 'index.html'} 查看结果")
        
        logger.info("可视化完成!")
        
    except Exception as e:
        logger.error(f"可视化过程中出错: {e}")
        raise

if __name__ == "__main__":
    main()