#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
可视化 HDBSCAN 聚类结果，为每个簇生成图片网格。

支持新的特征格式（part_features.pkl）和不同的特征类型。

用法示例：
python evaluate_scripts/clustering_analysis/visualize_hdbscan_clusters.py \
    --labels dataset/hdbscan_labels_shape.pkl \
    --img_dir datasets/fusion360_assembly

# 可视化融合特征的聚类结果，并显示零件名称
python evaluate_scripts/clustering_analysis/visualize_hdbscan_clusters.py \
    --labels dataset/hdbscan_labels_fused_features.pkl \
    --img_dir datasets/fusion360_assembly \
    --top_n 15 --samples_per_cluster 20 --show_names

# 自定义输出目录
python evaluate_scripts/clustering_analysis/visualize_hdbscan_clusters.py \
    --labels dataset/hdbscan_labels_shape.pkl \
    --img_dir datasets/fusion360_assembly \
    --out_dir visualization_results/my_custom_clusters

可视化选项：
--top_n 10          # 只显示前10个最大的簇
--samples_per_cluster 16  # 每个簇展示16张图片
--min_cluster_size 20     # 只显示大于20个样本的簇
--show_names        # 显示零件名称（从Neo4j数据库获取）
"""
import argparse
import os
import pickle
import random
from collections import defaultdict
from pathlib import Path

import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
from tqdm import tqdm
import psycopg2
from psycopg2.extras import RealDictCursor

import sys
# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from src.config import Config
from neo4j import GraphDatabase


def get_part_names_from_neo4j(part_ids, max_batch_size=100):
    """从Neo4j数据库中获取零件的名称，按需获取且限制每次查询数量"""
    print(f"开始从Neo4j获取 {len(part_ids)} 个零件的名称...")
    
    try:
        # 连接Neo4j数据库
        neo4j_config = Config.get_neo4j_config()
        print(f"连接Neo4j: {neo4j_config['uri']}")
        
        driver = GraphDatabase.driver(
            neo4j_config["uri"], 
            auth=(neo4j_config["user"], neo4j_config["password"]),
            connection_timeout=10  # 10秒连接超时
        )
        
        # 测试连接
        with driver.session() as test_session:
            test_session.run("RETURN 1").consume()
        print("Neo4j连接成功")
        
        # 分批处理，限制每次查询数量
        name_mapping = {}
        
        for i in range(0, len(part_ids), max_batch_size):
            batch = part_ids[i:i+max_batch_size]
            print(f"处理批次 {i//max_batch_size + 1}/{(len(part_ids)-1)//max_batch_size + 1}, 包含 {len(batch)} 个零件")
            
            with driver.session() as session:
                # 批量查询零件名称
                query = """
                    UNWIND $part_ids AS pid
                    MATCH (p:Part {uuid: pid})
                    RETURN p.uuid AS uuid, p.name AS name
                """
                result = session.run(query, {"part_ids": batch})
                
                # 构建名称映射
                batch_mapping = {}
                for record in result:
                    batch_mapping[record["uuid"]] = record["name"] or f"part_{record['uuid'][:8]}"
                
                name_mapping.update(batch_mapping)
                print(f"批次完成，找到 {len(batch_mapping)} 个零件名称")
        
        # 为没有找到的零件提供默认名称
        for pid in part_ids:
            if pid not in name_mapping:
                name_mapping[pid] = f"part_{pid[:8]}"
        
        print(f"从Neo4j获取了 {len(name_mapping)} 个零件的名称")
        return name_mapping
        
    except Exception as e:
        print(f"从Neo4j获取零件名称失败: {e}")
        print("使用默认名称...")
        # 返回默认值
        return {pid: f"part_{pid[:8]}" for pid in part_ids}
    finally:
        if 'driver' in locals():
            driver.close()
            print("Neo4j连接已关闭")


def get_assembly_ids_from_db(part_names):
    """从PostgreSQL数据库中获取零件对应的装配体ID"""
    try:
        # 连接数据库
        conn = psycopg2.connect(**Config.POSTGRES_CONFIG)
        
        with conn.cursor(cursor_factory=RealDictCursor) as cursor:
            # 查询零件的装配体ID
            placeholders = ','.join(['%s'] * len(part_names))
            query = f"""
                SELECT uuid, top_level_assembly_id 
                FROM parts 
                WHERE uuid IN ({placeholders})
            """
            cursor.execute(query, part_names)
            results = cursor.fetchall()
            
            # 构建字典映射
            assembly_mapping = {row['uuid']: row['top_level_assembly_id'] for row in results}
            
            # 为每个零件生成装配体名称列表
            assembly_names = []
            for part_name in part_names:
                assembly_id = assembly_mapping.get(part_name, 'unknown')
                assembly_names.append(assembly_id)
            
            print(f"从数据库获取了 {len(assembly_mapping)} 个零件的装配体ID")
            return assembly_names
            
    except Exception as e:
        print(f"从数据库获取装配体ID失败: {e}")
        # 返回默认值
        return ['unknown'] * len(part_names)
    finally:
        if 'conn' in locals():
            conn.close()


def load_clustering_results(labels_path):
    """加载聚类结果"""
    with open(labels_path, 'rb') as f:
        data = pickle.load(f)
    
    labels = data['labels']
    part_names = data['part_names']
    
    # 检查是否有装配体名称，如果没有则从数据库读取
    if 'assembly_names' in data:
        assembly_names = data['assembly_names']
        print("使用已有的装配体名称")
    else:
        print("装配体名称不存在，从数据库中读取...")
        assembly_names = get_assembly_ids_from_db(part_names)
    
    feature_type = data.get('feature_type', 'unknown')
    
    # 构建每个簇的样本索引
    clusters = defaultdict(list)
    for i, label in enumerate(labels):
        if label != -1:  # 忽略噪声点
            clusters[int(label)].append(i)
    
    return clusters, labels, part_names, assembly_names, data.get('params', {}), feature_type


def get_cluster_stats(clusters):
    """获取簇的统计信息"""
    stats = []
    for cluster_id, indices in clusters.items():
        stats.append((cluster_id, len(indices)))
    
    # 按簇大小排序
    stats.sort(key=lambda x: x[1], reverse=True)
    return stats


def create_cluster_grid(cluster_indices, part_names, assembly_names, img_dir, 
                        samples_per_cluster=16, grid_size=None, show_names=False, part_names_dict=None):
    """为一个簇创建图片网格"""
    if grid_size is None:
        # 自动计算网格大小
        grid_size = int(np.ceil(np.sqrt(samples_per_cluster)))
    
    # 如果簇太大，随机采样
    if len(cluster_indices) > samples_per_cluster:
        sample_indices = random.sample(cluster_indices, samples_per_cluster)
    else:
        sample_indices = cluster_indices
    
    # 创建网格
    fig = plt.figure(figsize=(grid_size*2, grid_size*2))
    
    for i, idx in enumerate(sample_indices):
        if i >= samples_per_cluster:
            break
            
        assembly = assembly_names[idx]
        part = part_names[idx]
        
        # 构建图片路径 - 支持不同的路径格式
        img_path = None
        possible_paths = [
            os.path.join(img_dir, assembly, f"{part}.png"),  # 旧格式
            os.path.join(img_dir, f"{part}.png"),             # 新格式：直接使用part_id
            os.path.join(img_dir, f"{assembly}_{part}.png"),  # 可能的组合格式
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                img_path = path
                break
        
        if img_path and os.path.exists(img_path):
            try:
                # 添加子图
                ax = fig.add_subplot(grid_size, grid_size, i + 1)
                img = Image.open(img_path)
                ax.imshow(img)
                ax.axis('off')
                
                # 如果需要显示名称，添加标题
                if show_names and part_names_dict:
                    display_name = part_names_dict.get(part, f"part_{part[:8]}")
                    ax.set_title(display_name, fontsize=8, pad=5, wrap=True)
            except Exception as e:
                print(f"无法加载图片 {img_path}: {e}")
        else:
            # 如果图片不存在，显示空白
            ax = fig.add_subplot(grid_size, grid_size, i + 1)
            ax.axis('off')
            
            # 如果需要显示名称，即使图片不存在也显示
            if show_names and part_names_dict:
                display_name = part_names_dict.get(part, f"part_{part[:8]}")
                ax.set_title(display_name, fontsize=8, pad=5, wrap=True)
    
    plt.tight_layout()
    return fig
    
    plt.tight_layout()
    return fig


def visualize_clusters(clusters, part_names, assembly_names, img_dir, out_dir,
                       top_n=None, samples_per_cluster=16, min_cluster_size=0, feature_type="unknown", 
                       show_names=False):
    """可视化多个簇"""
    # 获取簇统计信息
    stats = get_cluster_stats(clusters)
    
    # 过滤小簇
    if min_cluster_size > 0:
        stats = [(cid, size) for cid, size in stats if size >= min_cluster_size]
    
    # 限制簇数量
    if top_n is not None:
        stats = stats[:top_n]
    
    # 创建输出目录
    os.makedirs(out_dir, exist_ok=True)
    
    # 初始化零件名称字典
    part_names_dict = {}
    
    # 为每个簇创建可视化
    for cluster_id, size in tqdm(stats, desc="生成簇可视化"):
        cluster_indices = clusters[cluster_id]
        
        # 如果需要显示名称，按需获取当前簇中的零件名称
        current_part_names_dict = None
        if show_names:
            # 获取当前簇中的零件ID
            cluster_part_ids = [part_names[idx] for idx in cluster_indices]
            
            # 只获取尚未获取过的零件名称
            new_part_ids = [pid for pid in cluster_part_ids if pid not in part_names_dict]
            
            if new_part_ids:
                print(f"为簇 {cluster_id} 获取 {len(new_part_ids)} 个新零件的名称...")
                new_names = get_part_names_from_neo4j(new_part_ids)
                part_names_dict.update(new_names)
            
            current_part_names_dict = part_names_dict
        
        # 创建图片网格
        fig = create_cluster_grid(
            cluster_indices, part_names, assembly_names, img_dir, 
            samples_per_cluster=samples_per_cluster, show_names=show_names, part_names_dict=current_part_names_dict
        )
        
        # 保存图片
        out_path = os.path.join(out_dir, f"cluster_{cluster_id:03d}_{size}.png")
        plt.savefig(out_path, dpi=150)
        plt.close(fig)
        
    # 创建索引HTML文件
    create_html_index(out_dir, stats, feature_type)
    
    return len(stats)


def create_html_index(out_dir, stats, feature_type="unknown"):
    """创建HTML索引文件，方便浏览所有簇"""
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>HDBSCAN 聚类结果可视化 - {feature_type} 特征</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            .cluster-grid {{ display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 20px; }}
            .cluster-item {{ border: 1px solid #ddd; padding: 10px; border-radius: 5px; }}
            .cluster-item img {{ max-width: 100%; height: auto; }}
            h1, h2 {{ color: #333; }}
            .feature-info {{ background: #f0f0f0; padding: 10px; border-radius: 5px; margin-bottom: 20px; }}
        </style>
    </head>
    <body>
        <h1>HDBSCAN 聚类结果可视化</h1>
        <div class="feature-info">
            <strong>特征类型:</strong> {feature_type}<br>
            <strong>簇数量:</strong> {len(stats)}
        </div>
        <div class="cluster-grid">
    """
    
    for cluster_id, size in stats:
        img_filename = f"cluster_{cluster_id:03d}_{size}.png"
        html_content += f"""
        <div class="cluster-item">
            <h2>簇 #{cluster_id} (大小: {size})</h2>
            <a href="{img_filename}" target="_blank">
                <img src="{img_filename}" alt="簇 {cluster_id}">
            </a>
        </div>
        """
    
    html_content += """
        </div>
    </body>
    </html>
    """
    
    with open(os.path.join(out_dir, "index.html"), "w", encoding="utf-8") as f:
        f.write(html_content)


def main():
    parser = argparse.ArgumentParser(description="可视化 HDBSCAN 聚类结果")
    parser.add_argument("--labels", default="data/clustering/parts/hdbscan_labels_fused_features.pkl", help="聚类标签文件路径")
    parser.add_argument("--img_dir", default="data/datasets/fusion360_assembly", help="原始图片目录")
    parser.add_argument("--out_dir", default=None, help="输出目录，如果不指定则根据特征类型自动命名")
    parser.add_argument("--top_n", type=int, default=None, help="只显示前N个最大的簇")
    parser.add_argument("--samples_per_cluster", type=int, default=16, help="每个簇显示的样本数")
    parser.add_argument("--min_cluster_size", type=int, default=0, help="最小簇大小过滤")
    parser.add_argument("--show_names", default=True, help="是否在图片下方显示零件名称")
    args = parser.parse_args()
    
    # 加载聚类结果
    print(f"加载聚类结果: {args.labels}")
    clusters, labels, part_names, assembly_names, params, feature_type = load_clustering_results(args.labels)
    
    # 如果没有指定输出目录，根据特征类型自动命名
    if args.out_dir is None:
        args.out_dir = f"visualization_results/{feature_type}_clusters"
    
    # 打印参数
    print(f"特征类型: {feature_type}")
    print(f"输出目录: {args.out_dir}")
    print(f"显示名称: {args.show_names}")
    print("聚类参数:")
    for k, v in params.items():
        print(f"  {k}: {v}")
    
    # 统计信息
    n_clusters = len(clusters)
    n_noise = np.sum(labels == -1)
    n_total = len(labels)
    
    print(f"共 {n_clusters} 个簇, {n_noise} 个噪声点 ({n_noise/n_total:.2%})")
    
    # 可视化簇
    print(f"开始生成可视化到: {args.out_dir}")
    visualized = visualize_clusters(
        clusters, part_names, assembly_names, args.img_dir, args.out_dir,
        top_n=args.top_n, 
        samples_per_cluster=args.samples_per_cluster,
        min_cluster_size=args.min_cluster_size,
        feature_type=feature_type,
        show_names=args.show_names
    )
    
    print(f"已生成 {visualized} 个簇的可视化")
    print(f"请在浏览器中打开 {os.path.join(args.out_dir, 'index.html')} 查看结果")


if __name__ == "__main__":
    main()