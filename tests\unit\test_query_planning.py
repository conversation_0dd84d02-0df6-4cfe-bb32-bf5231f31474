#!/usr/bin/env python3
"""
测试查询规划智能体和多智能体系统
"""

import asyncio
import sys
import os


# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.agents.multi_agent_coordinator import MultiAgentCoordinator


# 测试查询示例
EXAMPLE_QUERIES = [
    "Find Steel Plate parts with length greater than 4", 
    "Find assemblies containing Cabos and Shell components for fluid flow measurement and monitoring with weight over 100",
    "Assemblies that provide power output with height greater than 8 composed of multiple Plates and Cylinders"
]


async def test_query_planning_agent():
    """测试查询规划智能体"""
    print("\n" + "="*60)
    print("🧪 测试查询规划智能体")
    print("="*60)
    
    from src.agents.query_planner_agent import QueryPlannerAgent
    from src.agents.data_models import QueryPlannerTask
    import uuid
    
    planner = QueryPlannerAgent()
    
    for i, query in enumerate(EXAMPLE_QUERIES, 1):
        print(f"\n📝 测试查询 {i}: {query}")
        print("-" * 50)
        
        try:
            task = QueryPlannerTask(
                task_id=str(uuid.uuid4()),
                query_text=query,
                top_k=10
            )
            
            result = await planner.execute_task(task)
            
            if result.status == 'success':
                print("✅ 查询规划生成成功")
                plan = planner.parse_query_plan_from_result(result)
                if plan:
                    print(f"   📊 查询类型: {plan.query_type}")
                    print(f"   📋 子查询数: {len(plan.sub_queries)}")
                    for i, sub_query in enumerate(plan.sub_queries, 1):
                        print(f"      - 子查询{i}: {sub_query['agent']}")
                    print(f"   🔄 融合策略: {plan.fusion_strategy}")
                else:
                    print("❌ 无法解析查询计划")
            else:
                print(f"❌ 查询规划失败: {result.error_message}")
                
        except Exception as e:
            print(f"❌ 测试异常: {e}")


async def test_multi_agent_system():
    """测试完整的多智能体系统"""
    print("\n" + "="*60) 
    print("🚀 测试完整的多智能体系统")
    print("="*60)
    
    coordinator = MultiAgentCoordinator()
    
    for i, query in enumerate(EXAMPLE_QUERIES, 1):
        print(f"\n🔍 执行查询 {i}: {query}")
        print("-" * 50)
        
        try:
            # 执行多模态查询
            result = await coordinator.execute_multi_modal_query(
                query_text=query,
                shape_vector=None,  # 这些示例没有形状向量
                top_k=10
            )
            
            if result.status == 'success':
                print(f"✅ 查询成功完成")
                print(f"   ⏱️ 执行时间: {result.execution_time:.2f}秒")
                print(f"   📊 结果数量: {result.total_results}")
                
                if result.results:
                    print(f"   📋 搜索结果列表:")
                    for j, item in enumerate(result.results[:5], 1):  # 只显示前5个
                        print(f"      {j}. UUID: {item.uuid}")
                        print(f"         名称: {item.name}")
                        print(f"         描述: {item.description}")
                        if item.similarity_score is not None:
                            print(f"         相似度: {item.similarity_score:.3f}")
                        print()
                        
                    if result.total_results > 5:
                        print(f"      ... 还有 {result.total_results - 5} 个结果")
                else:
                    print("   ⚠️ 无搜索结果")
            else:
                print(f"❌ 查询失败: {result.error_message}")
                
        except Exception as e:
            print(f"❌ 查询异常: {e}")
        
        print()
    
    # 断开连接
    await coordinator.disconnect()


async def test_query_with_shape_vector():
    """测试带形状向量的查询"""
    print("\n" + "="*60)
    print("🎯 测试带形状向量的查询")
    print("="*60)
    
    coordinator = MultiAgentCoordinator()
    
    # 模拟一个形状向量（实际应该是768维）
    mock_shape_vector = [0.1] * 768  # 简化的模拟向量
    
    query_with_shape = "给我找跟上传的模型差不多的模型，长度大于15，用于抵御外界冲击和干扰"
    
    print(f"🔍 执行带形状向量的查询: {query_with_shape}")
    print("-" * 50)
    
    try:
        result = await coordinator.execute_multi_modal_query(
            query_text=query_with_shape,
            shape_vector=mock_shape_vector,
            top_k=5
        )
        
        if result.status == 'success':
            print(f"✅ 查询成功完成")
            print(f"   ⏱️ 执行时间: {result.execution_time:.2f}秒")
            print(f"   📊 结果数量: {result.total_results}")
            
            if result.results:
                print(f"   📋 搜索结果列表:")
                for j, item in enumerate(result.results, 1):
                    print(f"      {j}. UUID: {item.uuid}")
                    print(f"         名称: {item.name}")  
                    print(f"         描述: {item.description}")
                    if item.similarity_score is not None:
                        print(f"         相似度: {item.similarity_score:.3f}")
                    print(f"         类型: {item.search_type}")
                    print()
            else:
                print("   ⚠️ 无搜索结果")
        else:
            print(f"❌ 查询失败: {result.error_message}")
            
    except Exception as e:
        print(f"❌ 查询异常: {e}")
    
    await coordinator.disconnect()


def print_example_queries():
    """打印示例查询"""
    print("\n" + "="*60)
    print("📚 测试查询示例")
    print("="*60)
    
    for i, query in enumerate(EXAMPLE_QUERIES, 1):
        print(f"{i}. {query}")
    
    print(f"\n总共 {len(EXAMPLE_QUERIES)} 个测试查询")


async def main():
    """主测试函数"""
    print("🚀 开始测试查询规划智能体和多智能体系统")
    
    # 打印测试查询
    print_example_queries()
    
    # # 测试1: 查询规划智能体
    # await test_query_planning_agent()
    
    # 测试2: 完整的多智能体系统
    await test_multi_agent_system()
    
    # 测试3: 带形状向量的查询
    await test_query_with_shape_vector()
    
    print("\n" + "="*60)
    print("🎉 所有测试完成！")
    print("="*60)


if __name__ == "__main__":
    asyncio.run(main())
